import { useTranslation } from 'react-i18next';
import { useCallback } from 'react';
import { changeLanguage, languageNames } from '../i18n';

/**
 * Custom hook for language management in the app
 * Provides easy access to translation functions and language switching
 */
export const useLanguage = () => {
  const { t, i18n } = useTranslation();
  
  // Get current language code
  const currentLanguage = i18n.language || 'en';
  
  // Get current language name
  const currentLanguageName = languageNames[currentLanguage as keyof typeof languageNames] || languageNames.en;
  
  // Switch language function
  const switchLanguage = useCallback(async (langCode: string) => {
    await changeLanguage(langCode);
  }, []);
  
  // Get all supported languages
  const supportedLanguages = [
    { code: 'en', name: languageNames.en },
    { code: 'hu', name: languageNames.hu },
    { code: 'es', name: languageNames.es },
    { code: 'de', name: languageNames.de },
    { code: 'fr', name: languageNames.fr },
  ];
  
  return {
    t,
    i18n,
    currentLanguage,
    currentLanguageName,
    switchLanguage,
    supportedLanguages,
  };
};

export default useLanguage;
