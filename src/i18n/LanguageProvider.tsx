import React, { useEffect, useState } from 'react';
import { View, ActivityIndicator } from 'react-native';
import { I18nextProvider } from 'react-i18next';
import i18n, { detectAndSetDeviceLanguage } from './index';

interface LanguageProviderProps {
  children: React.ReactNode;
}

const LanguageProvider: React.FC<LanguageProviderProps> = ({ children }) => {
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    // Initialize language settings on component mount
    const initializeLanguage = async () => {
      try {
        await detectAndSetDeviceLanguage();
      } catch (error) {
        console.error('Error initializing language:', error);
      } finally {
        setIsLoading(false);
      }
    };

    initializeLanguage();
  }, []);

  if (isLoading) {
    return (
      <View style={{ flex: 1, justifyContent: 'center', alignItems: 'center' }}>
        <ActivityIndicator size="large" color="#007AFF" />
      </View>
    );
  }

  return <I18nextProvider i18n={i18n}>{children}</I18nextProvider>;
};

export default LanguageProvider;
