import i18n from 'i18next';
import { initReactI18next } from 'react-i18next';
import AsyncStorage from '@react-native-async-storage/async-storage';
import * as Localization from 'expo-localization';

// Import translations
import enTranslation from '../locales/en/translation.json';
import huTranslation from '../locales/hu/translation.json';
import esTranslation from '../locales/es/translation.json';
import deTranslation from '../locales/de/translation.json';
import frTranslation from '../locales/fr/translation.json';

// Language resources
const resources = {
  en: { translation: enTranslation },
  hu: { translation: huTranslation },
  es: { translation: esTranslation },
  de: { translation: deTranslation },
  fr: { translation: frTranslation }
};

// Language names for UI display
export const languageNames = {
  en: 'English',
  hu: 'Magyar',
  es: 'Español',
  de: 'Deutsch',
  fr: 'Français'
};

// Storage key for language preference
const LANGUAGE_STORAGE_KEY = '@tripzy_language';

// Get saved language from AsyncStorage
export const getSavedLanguage = async (): Promise<string | null> => {
  try {
    return await AsyncStorage.getItem(LANGUAGE_STORAGE_KEY);
  } catch (error) {
    console.error('Error getting saved language:', error);
    return null;
  }
};

// Save language to AsyncStorage
export const saveLanguage = async (language: string): Promise<void> => {
  try {
    await AsyncStorage.setItem(LANGUAGE_STORAGE_KEY, language);
  } catch (error) {
    console.error('Error saving language:', error);
  }
};

// Initialize i18n
i18n
  .use(initReactI18next)
  .init({
    resources,
    lng: 'en', // Default language
    fallbackLng: 'en', // Fallback language if translation is missing
    interpolation: {
      escapeValue: false // React already escapes values
    },
    react: {
      useSuspense: false // Prevents issues with React Native
    }
  });

// Import Firebase auth and Firestore
import { getAuth } from 'firebase/auth';
import { doc, getDoc } from 'firebase/firestore';
import { db } from '../../services/firebase';

// Function to detect device language and set it if supported
export const detectAndSetDeviceLanguage = async (): Promise<void> => {
  try {
    console.log('Starting language detection process...');
    
    // First, check if user is logged in and has a language preference in Firestore
    const auth = getAuth();
    const currentUser = auth.currentUser;
    
    if (currentUser) {
      console.log('User is authenticated, checking Firestore language preference...');
      try {
        // Check user's Firestore profile for language preference
        const userDocRef = doc(db, 'users', currentUser.uid);
        const userDoc = await getDoc(userDocRef);
        
        if (userDoc.exists()) {
          console.log('User document exists, checking for languageCode...');
          const userData = userDoc.data();
          console.log('User data:', JSON.stringify(userData, null, 2));
          
          // Check if user is in onboarding process
          const onboardingCompleted = userData.onboardingCompleted === true;
          console.log('Onboarding completed:', onboardingCompleted);
          
          // CRITICAL CHANGE: Only use language from profile if onboarding is completed
          // This ensures new users always go through language selection
          if (userData.languageCode && onboardingCompleted === true) {
            // User has completed onboarding and has a language preference
            const userLanguage = userData.languageCode;
            console.log('Found language in user profile and onboarding completed:', userLanguage);
            await changeLanguage(userLanguage);
            return;
          } else {
            console.log('No languageCode found or onboarding not completed');
            
            // CRITICAL: For new users in onboarding, we don't want to use any saved preferences
            // or device locale - we want them to explicitly choose in the language screen
            // So we just set a minimal UI language for the language selection screen itself
            console.log('Not setting any language automatically - user must select language manually');
            // We'll use English only for the language selection UI itself
            // This won't be saved as the user's preference
            i18n.changeLanguage('en');
            return;
          }
        } else {
          console.log('User document does not exist in Firestore');
          // For new users with no document, force language selection
          // Don't set any language - they must select one in the language screen
          console.log('New user with no document - must select language manually');
          return;
        }
      } catch (firebaseError) {
        console.error('Error fetching user language preference:', firebaseError);
        // Don't set any language in error cases during onboarding
        // This ensures users must explicitly select a language
        console.log('Error occurred, but not setting any language - user must select manually');
        return;
      }
    } else {
      console.log('No authenticated user found');
      // For unauthenticated users, we'll use a minimal UI language for the auth screens
      // But we won't set any persistent language preference
      console.log('No authenticated user - using minimal UI language for auth screens only');
      return;
    }
    
    // CRITICAL: For new users in onboarding, we don't want to use any saved preferences
    // or device locale - we want them to explicitly choose in the language screen
    // We'll use English only for the minimal UI of the language selection screen itself
    // This won't be saved as the user's preference
    console.log('Using minimal UI language for language selection screen only');
    i18n.changeLanguage('en');
  } catch (error) {
    console.error('Error detecting device language:', error);
    // Default to English on error
    console.log('Error occurred during language detection, defaulting to English');
    i18n.changeLanguage('en');
  }
};

// Function to change language
export const changeLanguage = async (language: string): Promise<void> => {
  try {
    // Change language in i18n
    await i18n.changeLanguage(language);
    
    // Save language preference
    await saveLanguage(language);
    
    console.log(`Language changed to: ${language}`);
  } catch (error) {
    console.error('Error changing language:', error);
  }
};

export default i18n;
