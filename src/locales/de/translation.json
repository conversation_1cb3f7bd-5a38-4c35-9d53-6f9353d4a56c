{"profile": {"title": "Profil", "editProfile": "<PERSON><PERSON>", "languagePreferences": "Spracheinstellungen", "signOut": "Abmelden", "signOutConfirmation": "Bist du sicher, dass du dich abmelden möchtest?", "viewPastAdventures": "Zeige deine vergangenen Abenteuer", "subscription": "Abonnement", "plan": "Plan", "free": "<PERSON><PERSON><PERSON>", "premium": "Premium", "upgradeToPremium": "Auf Premium upgraden", "basicTripStorage": "Grundlegende Reisespeicherung", "standardSupport": "Standard-Support", "unlimitedTripStorage": "Unbegrenzte Reisespeicherung", "exclusiveTravelDeals": "Exklusive Reiseangebote", "prioritySupport": "Prioritäts-Support", "advancedAiPlanning": "Erweiterte KI-Reiseplanung", "settings": "Einstellungen", "loadingProfile": "Profil wird geladen...", "completedTrips": "Abgeschlossene Reisen", "trips": "<PERSON><PERSON><PERSON>"}, "languages": {"english": "<PERSON><PERSON><PERSON>", "hungarian": "Ungarisch", "spanish": "Spanisch", "german": "De<PERSON>ch", "french": "Franzö<PERSON><PERSON>", "languageSelector": "Sprache auswählen"}, "trips": {"completedTrips": "Abgeschlossene Reisen", "completed": "Abgeschlossen", "loadingTrips": "Reisen werden geladen...", "noCompletedTrips": "Noch keine abgeschlossenen Reisen", "completedTripsDescription": "Deine abgeschlossenen Reisen er<PERSON> hier, sobald du sie in deiner Reiseroute als abgeschlossen markierst."}, "tripWizard": {"planNewTrip": "Neue Reise planen", "step1of3": "Schritt 1 von 3", "destination": "<PERSON><PERSON><PERSON><PERSON>", "enterCityOrCountry": "Stadt oder Land eingeben", "selectTravelDates": "Wählen Sie Ihre Reisedaten.", "travelDates": "Reisedaten", "exactDates": "Genaue Daten", "flexibleDates": "Flexible Daten", "month": "<PERSON><PERSON>", "tripLength": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "specialInterests": "Besondere Interessen", "interestsSubtitle": "Wir verbinden Ihre Interessen mit lokalen Aktivitäten", "interests": {"history": "Geschichte", "art": "<PERSON><PERSON>", "gastronomy": "Gastronomie", "fashion": "Mode", "nature": "<PERSON><PERSON>", "sportsEvents": "Sportveranstaltungen", "sightseeing": "Sightseeing"}, "budget": {"title": "Budget", "question": "Wie ist dein typisches Reisebudget?", "lowBudget": "<PERSON><PERSON><PERSON><PERSON>", "mediumBudget": "<PERSON><PERSON>res Budget", "luxuryBudget": "Luxus-Budget"}, "travelGroup": {"title": "Reisegruppe", "question": "Mit wem reist du normalerwei<PERSON>?", "subtitle": "Dies hilft uns, die am besten geeigneten Erlebnisse für deine Reisegruppe vorzuschlagen.", "solo": "<PERSON><PERSON><PERSON>", "couple": "<PERSON><PERSON>", "friends": "Freunde", "familyWithKids": "Familie mit Kindern"}, "activityLevel": {"title": "Aktivitätsniveau", "question": "Welches Aktivitätsniveau bevorzugst du während deiner Reisen?", "subtitle": "Wir passen deinen Reiseplan an dein Energieniveau an.", "relax": "Entspannen", "mediumActivity": "Mittlere Aktivität", "veryActive": "Sehr aktiv", "relaxDescription": "Minimales Gehen, viel Freizeit", "moderateDescription": "Etwas Gehen, ausgewogene Aktivitäten", "activeDescription": "Viel Gehen und körperliche Aktivitäten"}, "foodPreferences": "Essensvorlieben", "food": {"localFood": "Lokale Küche", "vegetarian": "Vegetarisch", "streetFood": "Straßenessen", "fineDining": "Gehobene Küche"}, "generateItinerary": "Reiseplan er<PERSON>llen", "progressText": "{{current}}/{{total}}", "onboardingProgress": "7/7", "interestsQuestion": "Was sind deine Reiseinteressen?", "destinations": {"question": "Welche Arten von Reisezielen bevorzugst du?", "subtitle": "Wir werden Reisen vorschlagen, die deinen Vorlieben entsprechen.", "beach": "Strandziele", "city": "Städtereisen", "mountain": "<PERSON><PERSON><PERSON><PERSON>", "countryside": "<PERSON><PERSON><PERSON><PERSON>", "island": "Inseln", "historic": "Historische Stätten"}, "challenges": {"question": "Was sind die größten Herausforderungen, mit denen du beim Reisen konfrontiert wirst?", "subtitle": "Wir helfen dir, diese Herausforderungen mit unseren Empfehlungen zu überwinden.", "options": {"tooManyOptions": "<PERSON>u viele Optionen, schwer zu wählen", "prioritization": "<PERSON>ß nicht, was zu priorisieren ist", "touristTraps": "Finde nur Touristenfallen, keine versteckten Schätze", "transportation": "Probleme mit Transport oder Logistik", "valueForMoney": "<PERSON><PERSON><PERSON>, was Zeit/Geld wert ist"}}, "travelPreferences": {"title": "<PERSON><PERSON><PERSON><PERSON><PERSON> uns von deinen Reisegewohnheiten", "subtitle": "Dies hilft uns, deine Erfahrung zu personalisieren.", "frequency": "Wie oft reist du?", "frequencyOptions": {"weekly": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "monthly": "<PERSON><PERSON><PERSON>", "quarterly": "Alle paar Monate", "yearly": "Ein- oder zweimal im Jahr"}, "duration": "Wie lang sind deine typischen Reisen?", "durationOptions": {"weekend": "Wochenendtrips", "week": "1 Woche", "twoWeeks": "2 Wochen", "longer": "Längere Reisen"}, "style": "Was ist dein bevorzugter Reisestil?", "styleOptions": {"luxury": "Luxus", "comfort": "Komfort", "budget": "Budgetf<PERSON>undlich", "backpacking": "Backpacking"}, "finish": "Abschließen", "back": "Zurück"}}, "common": {"save": "Speichern", "cancel": "Abbrechen", "loading": "Wird geladen...", "error": "<PERSON><PERSON>", "noPlacesFound": "<PERSON><PERSON> gefunden", "savePlacesFromItinerary": "Speichere Orte aus deiner Reiseroute, um sie auf der Karte zu sehen."}, "dashboard": {"welcome": "<PERSON><PERSON><PERSON><PERSON>,", "planNewTrip": "Neue Reise planen", "upcomingTrips": "Kommende Reisen", "seeAll": "Alle anzeigen", "noUpcomingTrips": "<PERSON><PERSON> kommenden Reisen", "planFirstAdventure": "Plane dein erstes Abenteuer!", "popularDestinations": "Beliebte Reiseziele", "explore": "Entdecken"}, "map": {"allCities": "Alle Städte", "allPlaces": "Alle Orte", "food": "Essen", "attractions": "Attraktionen"}, "quickPlan": {"title": "<PERSON><PERSON>", "findRestaurantNearby": "Restaurant in der Nähe finden", "topAttractionsToVisit": "Top-Sehenswürdigkeiten", "dayTripIdeasNearby": "Tagesausflugideen in der Nähe", "typeMessage": "Nachricht eingeben..."}, "tripPlanner": {"planNewTrip": "Neue Reise planen", "step": "<PERSON><PERSON><PERSON>", "of": "von", "destination": "<PERSON><PERSON><PERSON><PERSON>", "enterCityOrCountry": "Stadt oder Land eingeben", "selectTravelDates": "Wähle deine Reisedaten aus.", "travelDates": "Reisedaten", "exactDates": "Genaue Daten", "flexibleDates": "Flexible Daten", "specialInterests": "Besondere Interessen", "matchInterestsWithActivities": "Wir verbinden deine Interessen mit lokalen Aktivitäten", "history": "Geschichte", "art": "<PERSON><PERSON>", "gastronomy": "Gastronomie", "fashion": "Mode", "editTrip": "<PERSON><PERSON> bear<PERSON>", "share": "Teilen", "day": "Tag", "activities": "Aktivitäten"}, "navigation": {"dashboard": "Dashboard", "map": "<PERSON><PERSON>", "quickPlan": "Schnellplan", "profile": "Profil"}, "tripPlanning": {"questionText": "Wissen Sie bereits, was <PERSON><PERSON> besuchen möchten?", "yesOption": "<PERSON>a <PERSON> <PERSON>ch habe bestimmte Attraktionen im Sinn", "noOption": "Nein <PERSON> <PERSON> Reiseplan von Grund auf erstellen"}, "specificAttractions": {"title": "Planen Sie Ihre Reise", "subtitle": "<PERSON><PERSON><PERSON><PERSON><PERSON> Si<PERSON> uns von Ihren spezifischen Attraktionen und Vorlieben", "destination": "<PERSON><PERSON><PERSON><PERSON>", "destinationPlaceholder": "Stadt oder Land eingeben (z.B. Paris, Frankreich)", "travelDates": "Reisedaten", "exactDates": "Genaue Daten", "numberOfDays": "Anzahl der Tage", "numberOfDaysPlaceholder": "<PERSON><PERSON><PERSON> der Tage (z.B. 7)", "cityAttractions": "Stadtspezifische Attraktionen", "cityAttractionsSubtitle": "Welche Attraktionen möchten Sie in {{destination}} besuchen?", "cityAttractionsPlaceholder": "Attraktionsname eingeben (z.B. Eiffelturm, Louvre Museum, Central Park)", "mustSeeItems": "Sehenswürdigkeiten (Allgemein)", "mustSeeItemsSubtitle": "<PERSON>ichtige Sehenswürdigkeiten, die Sie irgendwo in Ihrer Reise einschließen möchten", "mustSeeItemsPlaceholder": "Sehenswürdigkeit eingeben (z.B. Eiffelturm, Times Square, Golden Gate Bridge)", "customRequests": "Individuelle Wü<PERSON>", "customRequestsSubtitle": "Spezifische Wünsche oder Zeitpräferenzen", "customRequestsPlaceholder": "z.B. Eiffelturm am Mittwochabend besuchen", "tripPreferences": "Reisepräferenzen", "tripPreferencesSubtitle": "<PERSON><PERSON><PERSON>, Ihr perfektes Reiseerlebnis zu personalisieren", "budget": "Budget", "lowBudget": "<PERSON><PERSON><PERSON><PERSON>", "mediumBudget": "<PERSON><PERSON>res Budget", "luxuryBudget": "Luxus Budget", "travelGroup": "Reisegruppe", "solo": "<PERSON><PERSON>", "couple": "<PERSON><PERSON>", "friends": "Freunde", "familyWithKids": "Familie mit Kindern", "activityLevel": "Aktivitätsniveau", "relax": "Entspannt", "mediumActivity": "Mittlere Aktivität", "veryActive": "Sehr Aktiv", "specialInterests": "Besondere Interessen", "specialInterestsSubtitle": "Wir verbinden Ihre Interessen mit lokalen Aktivitäten", "sightseeing": "Sightseeing", "history": "Geschichte", "art": "<PERSON><PERSON>", "gastronomy": "Gastronomie", "fashion": "Mode", "nature": "<PERSON><PERSON>", "sportsEvents": "Sportveranstaltungen", "nightlife": "Nachtleben", "relaxation": "Entspannung", "foodPreferences": "Essenspräferenzen", "localFood": "Lokales Essen", "vegetarian": "Vegetarisch", "streetFood": "Straßenessen", "fineDining": "Gehobene Küche", "createTripPlan": "<PERSON>ise<PERSON><PERSON>", "creatingTripPlan": "Reiseplan wird erstellt...", "missingInformation": "Fehlende Informationen", "enterDestination": "<PERSON>te geben Si<PERSON> ein Reiseziel ein.", "selectTravelDates": "Bitte wählen Sie Reisedaten aus.", "enterValidDays": "Bitte geben Si<PERSON> eine gültige Anzahl von Tagen ein.", "error": "<PERSON><PERSON>", "failedToSave": "Ihr Reiseplan konnte nicht gespeichert werden. Bitte versuchen Si<PERSON> es erneut."}}