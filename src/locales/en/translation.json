{"profile": {"title": "Profile", "editProfile": "Edit Profile", "languagePreferences": "Language Preferences", "signOut": "Sign Out", "signOutConfirmation": "Are you sure you want to sign out?", "viewPastAdventures": "View your past adventures", "subscription": "Subscription", "plan": "Plan", "free": "Free", "premium": "Premium", "upgradeToPremium": "Upgrade to Premium", "basicTripStorage": "Basic trip storage", "standardSupport": "Standard support", "unlimitedTripStorage": "Unlimited trip storage", "exclusiveTravelDeals": "Exclusive travel deals", "prioritySupport": "Priority customer support", "advancedAiPlanning": "Advanced AI trip planning", "settings": "Settings", "loadingProfile": "Loading profile...", "completedTrips": "Completed Trips", "trips": "trips"}, "languages": {"english": "English", "hungarian": "Hungarian", "spanish": "Spanish", "german": "German", "french": "French", "languageSelector": "Select Language"}, "trips": {"completedTrips": "Completed Trips", "completed": "Completed", "loadingTrips": "Loading trips...", "noCompletedTrips": "No completed trips yet", "completedTripsDescription": "Your completed trips will appear here once you mark them as completed in your itinerary."}, "tripWizard": {"planNewTrip": "Plan a New Trip", "step1of3": "Step 1 of 3", "destination": "Destination", "enterCityOrCountry": "Enter city or country", "selectTravelDates": "Select your travel dates.", "travelDates": "Travel Dates", "exactDates": "Exact Dates", "flexibleDates": "Flexible Dates", "month": "Month", "tripLength": "Trip Length", "specialInterests": "Special Interests", "interestsSubtitle": "We'll match your interests with local activities", "challenges": {"question": "What are the biggest challenges you usually face when traveling?", "subtitle": "We'll help you overcome these challenges with our recommendations.", "options": {"tooManyOptions": "Too many options, hard to choose", "prioritization": "Don't know what to prioritize", "touristTraps": "Only find tourist traps, not hidden gems", "transportation": "Struggle with transportation or logistics", "valueForMoney": "Unsure what's worth the time/money"}}, "interests": {"history": "History", "art": "Art", "gastronomy": "Gastronomy", "fashion": "Fashion", "nature": "Nature", "sportsEvents": "Sports Events", "sightseeing": "Sightseeing", "nightlife": "Nightlife", "relaxation": "Relaxation"}, "interestsQuestion": "What are your travel interests?", "destinations": {"question": "What types of destinations do you prefer?", "subtitle": "We'll suggest trips that match your preferences.", "beach": "Beach Destinations", "city": "City Breaks", "mountain": "Mountain Retreats", "countryside": "Countryside", "island": "Islands", "historic": "Historic Sites"}, "budget": {"title": "Budget", "question": "What's your typical travel budget?", "lowBudget": "Low Budget", "mediumBudget": "Medium Budget", "luxuryBudget": "Luxury Budget"}, "travelGroup": {"title": "Travel Group", "solo": "Solo", "couple": "<PERSON><PERSON><PERSON>", "friends": "Friends", "familyWithKids": "Family with Kids", "question": "Who do you usually travel with?", "subtitle": "This helps us suggest the most suitable experiences for your travel group."}, "activityLevel": {"title": "Activity Level", "relax": "Relax", "mediumActivity": "Medium Activity", "veryActive": "Very Active", "question": "What activity level do you prefer during your trips?", "subtitle": "We'll adjust your itinerary to match your energy level.", "relaxDescription": "Minimal walking, plenty of downtime", "moderateDescription": "Some walking, balanced activities", "activeDescription": "Lots of walking and physical activities"}, "foodPreferences": "Food Preferences", "food": {"localFood": "Local food", "vegetarian": "Vegetarian", "streetFood": "Street food", "fineDining": "Fine dining"}, "travelPreferences": {"title": "Tell us about your travel habits", "subtitle": "This helps us personalize your experience.", "frequency": "How often do you travel?", "frequencyOptions": {"weekly": "Weekly", "monthly": "Monthly", "quarterly": "Every Few Months", "yearly": "Once or Twice a Year"}, "duration": "How long are your typical trips?", "durationOptions": {"weekend": "Weekend Trips", "week": "1 Week", "twoWeeks": "2 Weeks", "longer": "Longer <PERSON>s"}, "style": "What's your preferred travel style?", "styleOptions": {"luxury": "Luxury", "comfort": "Comfort", "budget": "Budget-Friendly", "backpacking": "Backpacking"}, "finish": "Finish", "back": "Back"}, "progressText": "{{current}}/{{total}}", "onboardingProgress": "8/8", "generateItinerary": "Generate Itinerary"}, "common": {"save": "Save", "cancel": "Cancel", "loading": "Loading...", "error": "Error", "noPlacesFound": "No Places Found", "savePlacesFromItinerary": "Save places from your itinerary to see them on the map."}, "dashboard": {"welcome": "Welcome,", "planNewTrip": "Plan a New Trip", "upcomingTrips": "Upcoming Trips", "seeAll": "See All", "noUpcomingTrips": "No upcoming trips", "planFirstAdventure": "Plan your first adventure!", "popularDestinations": "Popular Destinations", "explore": "Explore"}, "map": {"allCities": "All Cities", "allPlaces": "All Places", "food": "Food", "attractions": "Attractions"}, "quickPlan": {"title": "<PERSON><PERSON>", "findRestaurantNearby": "Find restaurant nearby", "topAttractionsToVisit": "Top attractions to visit", "dayTripIdeasNearby": "Day trip ideas nearby", "typeMessage": "Type a message..."}, "tripPlanner": {"planNewTrip": "Plan a New Trip", "step": "Step", "of": "of", "destination": "Destination", "enterCityOrCountry": "Enter city or country", "selectTravelDates": "Select your travel dates.", "travelDates": "Travel Dates", "exactDates": "Exact Dates", "flexibleDates": "Flexible Dates", "specialInterests": "Special Interests", "matchInterestsWithActivities": "We'll match your interests with local activities", "history": "History", "art": "Art", "gastronomy": "Gastronomy", "fashion": "Fashion", "editTrip": "Edit Trip", "share": "Share", "day": "Day", "activities": "Activities"}, "navigation": {"dashboard": "Dashboard", "map": "Map", "quickPlan": "Quick Plan", "profile": "Profile"}, "tripPlanning": {"questionText": "Do you already know what you want to visit?", "yesOption": "Yes – I have specific attractions in mind", "noOption": "No – Build the itinerary from scratch"}, "specificAttractions": {"title": "Plan Your Trip", "subtitle": "Tell us about your specific attractions and preferences", "destination": "Destination", "destinationPlaceholder": "Enter city or country (e.g., Paris, France)", "travelDates": "Travel Dates", "exactDates": "Exact Dates", "numberOfDays": "Number of Days", "numberOfDaysPlaceholder": "Number of days (e.g., 7)", "cityAttractions": "City-Specific Attractions", "cityAttractionsSubtitle": "What attractions do you want to visit in {{destination}}?", "cityAttractionsPlaceholder": "Enter attraction name (e.g., Eiffel Tower, Louvre Museum, Central Park)", "mustSeeItems": "Must-<PERSON> <PERSON><PERSON> (General)", "mustSeeItemsSubtitle": "Key sights you want to include anywhere in your trip", "mustSeeItemsPlaceholder": "Enter must-see item (e.g., Eiffel Tower, Times Square, Golden Gate Bridge)", "customRequests": "Custom Requests", "customRequestsSubtitle": "Specific wishes or timing preferences", "customRequestsPlaceholder": "e.g., Visit Eiffel Tower on Wednesday evening", "tripPreferences": "Trip Preferences", "tripPreferencesSubtitle": "Help us personalize your perfect trip experience", "budget": "Budget", "lowBudget": "Low Budget", "mediumBudget": "Medium Budget", "luxuryBudget": "Luxury Budget", "travelGroup": "Travel Group", "solo": "Solo", "couple": "<PERSON><PERSON><PERSON>", "friends": "Friends", "familyWithKids": "Family with Kids", "activityLevel": "Activity Level", "relax": "Relax", "mediumActivity": "Medium Activity", "veryActive": "Very Active", "specialInterests": "Special Interests", "specialInterestsSubtitle": "We'll match your interests with local activities", "sightseeing": "Sightseeing", "history": "History", "art": "Art", "gastronomy": "Gastronomy", "fashion": "Fashion", "nature": "Nature", "sportsEvents": "Sports Events", "nightlife": "Nightlife", "relaxation": "Relaxation", "foodPreferences": "Food Preferences", "localFood": "Local food", "vegetarian": "Vegetarian", "streetFood": "Street food", "fineDining": "Fine dining", "createTripPlan": "Create Trip Plan", "creatingTripPlan": "Creating Trip Plan...", "missingInformation": "Missing Information", "enterDestination": "Please enter a destination.", "selectTravelDates": "Please select travel dates.", "enterValidDays": "Please enter a valid number of days.", "error": "Error", "failedToSave": "Failed to save your trip plan. Please try again."}}