import React, { useState, useEffect } from 'react';
import { View, StyleSheet, TouchableOpacity, Modal, FlatList } from 'react-native';
import { Text } from 'react-native-paper';
import { useTranslation } from 'react-i18next';
import { Ionicons, Feather } from '@expo/vector-icons';
import { languageNames, changeLanguage } from '../i18n';

interface LanguageSelectorProps {
  onLanguageChange?: (language: string) => void;
}

const LanguageSelector: React.FC<LanguageSelectorProps> = ({ onLanguageChange }) => {
  const { t, i18n } = useTranslation();
  const [modalVisible, setModalVisible] = useState(false);
  const [selectedLanguage, setSelectedLanguage] = useState(i18n.language || 'en');

  // List of supported languages with their codes and names
  const languages = [
    { code: 'en', name: languageNames.en },
    { code: 'hu', name: languageNames.hu },
    { code: 'es', name: languageNames.es },
    { code: 'de', name: languageNames.de },
    { code: 'fr', name: languageNames.fr },
  ];

  // Update selected language when i18n.language changes
  useEffect(() => {
    setSelectedLanguage(i18n.language);
  }, [i18n.language]);

  // Handle language selection
  const handleSelectLanguage = async (langCode: string) => {
    await changeLanguage(langCode);
    setSelectedLanguage(langCode);
    setModalVisible(false);
    
    if (onLanguageChange) {
      onLanguageChange(langCode);
    }
  };

  // Get flag emoji for language
  const getLanguageFlag = (langCode: string): string => {
    const flags: Record<string, string> = {
      en: '🇬🇧',
      hu: '🇭🇺',
      es: '🇪🇸',
      de: '🇩🇪',
      fr: '🇫🇷',
    };
    return flags[langCode] || '';
  };

  // Render language item in the selector
  const renderLanguageItem = ({ item }: { item: { code: string; name: string } }) => {
    const isSelected = selectedLanguage === item.code;
    
    return (
      <TouchableOpacity
        style={[styles.languageItem, isSelected && styles.selectedLanguageItem]}
        onPress={() => handleSelectLanguage(item.code)}
      >
        <View style={styles.languageItemContent}>
          <Text style={styles.languageFlag}>{getLanguageFlag(item.code)}</Text>
          <Text style={[styles.languageName, isSelected && styles.selectedLanguageText]}>
            {item.name}
          </Text>
        </View>
        {isSelected && (
          <Ionicons name="checkmark" size={22} color="#007AFF" />
        )}
      </TouchableOpacity>
    );
  };

  return (
    <View>
      <TouchableOpacity 
        style={styles.languageButton} 
        onPress={() => setModalVisible(true)}
      >
        <View style={styles.selectedLanguageContainer}>
          <Text style={styles.selectedLanguageFlag}>
            {getLanguageFlag(selectedLanguage)}
          </Text>
          <Text style={styles.selectedLanguageName}>
            {languages.find(lang => lang.code === selectedLanguage)?.name || languageNames.en}
          </Text>
        </View>
        <Feather name="chevron-right" size={20} color="#CCCCCC" />
      </TouchableOpacity>

      <Modal
        animationType="slide"
        transparent={true}
        visible={modalVisible}
        onRequestClose={() => setModalVisible(false)}
      >
        <View style={styles.modalContainer}>
          <View style={styles.modalContent}>
            <View style={styles.modalHeader}>
              <Text style={styles.modalTitle}>{t('languages.languageSelector')}</Text>
              <TouchableOpacity
                style={styles.closeButton}
                onPress={() => setModalVisible(false)}
              >
                <Ionicons name="close" size={24} color="#333" />
              </TouchableOpacity>
            </View>
            
            <FlatList
              data={languages}
              renderItem={renderLanguageItem}
              keyExtractor={(item) => item.code}
              style={styles.languageList}
            />
          </View>
        </View>
      </Modal>
    </View>
  );
};

const styles = StyleSheet.create({
  languageButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingVertical: 5,
  },
  selectedLanguageContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  selectedLanguageFlag: {
    fontSize: 18,
    marginRight: 8,
  },
  selectedLanguageName: {
    fontSize: 16,
    color: '#333333',
  },
  modalContainer: {
    flex: 1,
    justifyContent: 'flex-end',
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
  },
  modalContent: {
    backgroundColor: 'white',
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    paddingBottom: 20,
    maxHeight: '70%',
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#f0f0f0',
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
  },
  closeButton: {
    padding: 5,
  },
  languageList: {
    paddingHorizontal: 16,
  },
  languageItem: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingVertical: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#f0f0f0',
  },
  selectedLanguageItem: {
    backgroundColor: 'rgba(0, 122, 255, 0.05)',
  },
  languageItemContent: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  languageFlag: {
    fontSize: 22,
    marginRight: 12,
  },
  languageName: {
    fontSize: 16,
    color: '#333',
  },
  selectedLanguageText: {
    fontWeight: '500',
    color: '#007AFF',
  },
});

export default LanguageSelector;
