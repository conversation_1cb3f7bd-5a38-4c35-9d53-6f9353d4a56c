/**
 * idb shim for React Native
 * This provides a mock implementation of the idb module used by Firebase
 */

// Create a simple mock implementation of the idb API
module.exports = {
  openDB: () => Promise.resolve({
    get: () => Promise.resolve(null),
    put: () => Promise.resolve(),
    delete: () => Promise.resolve(),
    clear: () => Promise.resolve(),
    close: () => Promise.resolve(),
    transaction: () => ({
      objectStore: () => ({
        get: () => Promise.resolve(null),
        put: () => Promise.resolve(),
        delete: () => Promise.resolve(),
        clear: () => Promise.resolve()
      }),
      done: Promise.resolve()
    })
  }),
  deleteDB: () => Promise.resolve(),
  unwrap: (value) => value,
  wrap: (value) => value
};
