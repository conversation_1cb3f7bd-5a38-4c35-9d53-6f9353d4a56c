import { doc, getDoc, setDoc } from 'firebase/firestore';
import { db } from '../services/firebase';
import AsyncStorage from '@react-native-async-storage/async-storage';

const ONBOARDING_COMPLETED_KEY = 'onboarding_completed';

/**
 * Checks if a user has completed the onboarding process
 * First checks local storage, then falls back to Firestore
 * @param userId The user's ID
 * @returns Promise<boolean> True if onboarding is completed
 */
export const checkOnboardingStatus = async (userId: string): Promise<boolean> => {
  try {
    console.log(`[checkOnboardingStatus] Checking for user: ${userId}`);
    
    // First check local storage for faster response
    const localStatus = await AsyncStorage.getItem(`${ONBOARDING_COMPLETED_KEY}_${userId}`);
    console.log(`[checkOnboardingStatus] Local storage status: ${localStatus}`);
    
    if (localStatus === 'true') {
      console.log('[checkOnboardingStatus] Found completed status in local storage');
      return true;
    }
    
    // If not in local storage, check Firestore
    const userDocRef = doc(db, 'users', userId);
    const userDoc = await getDoc(userDocRef);
    
    if (userDoc.exists()) {
      const userData = userDoc.data();
      console.log('[checkOnboardingStatus] User data from Firestore:', {
        onboardingCompleted: userData.onboardingCompleted,
        languageCode: userData.languageCode,
        preferredLanguage: userData.preferredLanguage
      });
      
      if (userData.onboardingCompleted === true) {
        // Cache the result in local storage for future checks
        console.log('[checkOnboardingStatus] Onboarding is completed, caching in local storage');
        await AsyncStorage.setItem(`${ONBOARDING_COMPLETED_KEY}_${userId}`, 'true');
        return true;
      }
    } else {
      console.log('[checkOnboardingStatus] User document does not exist in Firestore');
    }
    
    console.log('[checkOnboardingStatus] Onboarding is NOT completed');
    return false;
  } catch (error) {
    console.error('Error checking onboarding status:', error);
    return false;
  }
};

/**
 * Marks onboarding as completed in both Firestore and local storage
 * @param userId The user's ID
 */
export const markOnboardingCompleted = async (userId: string): Promise<void> => {
  try {
    console.log(`Marking onboarding as completed for user ${userId}`);
    
    // Update Firestore to mark onboarding as completed
    const userDocRef = doc(db, 'users', userId);
    await setDoc(userDocRef, {
      onboardingCompleted: true,
      updatedAt: new Date().toISOString()
    }, { merge: true });
    
    // Store in local storage for faster access next time
    await AsyncStorage.setItem(`${ONBOARDING_COMPLETED_KEY}_${userId}`, 'true');
    
    console.log('Successfully marked onboarding as completed in both Firestore and local storage');
  } catch (error) {
    console.error('Error marking onboarding as completed:', error);
  }
};
