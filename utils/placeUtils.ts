/**
 * Utility functions for handling place data
 * This is a simplified version with only the essential functions needed for the itinerary screen
 */

/**
 * Maps activity types to place categories
 */
export const activityTypeToPlaceCategory = (activityType: string): string => {
  switch (activityType.toLowerCase()) {
    case 'food':
    case 'restaurant':
      return 'restaurant';
    case 'attraction':
    case 'sightseeing':
      return 'attraction';
    case 'evening':
    case 'event':
      return 'event';
    case 'hotel':
    case 'accommodation':
      return 'hotel';
    default:
      return 'other';
  }
};

/**
 * Generates a unique ID for a place
 */
export const generatePlaceId = (title: string, location?: string): string => {
  const base = title.toLowerCase().replace(/[^a-z0-9]/g, '-');
  const timestamp = Date.now();
  const random = Math.floor(Math.random() * 1000);
  return `${base}-${timestamp}-${random}`;
};

/**
 * Extracts coordinates from a location string
 * Returns null if no coordinates can be extracted
 */
export const extractCoordinates = (location?: string): { lat: number; lng: number } | null => {
  if (!location) return null;
  
  // Try to match coordinates in various formats
  const patterns = [
    // Format: "Latitude: 47.4979, Longitude: 19.0402"
    /latitude:\s*([-+]?\d+(\.\d+)?),\s*longitude:\s*([-+]?\d+(\.\d+)?)/i,
    
    // Format: "47.4979, 19.0402"
    /([-+]?\d+(\.\d+)?),\s*([-+]?\d+(\.\d+)?)/,
    
    // Format: "47.4979 N, 19.0402 E"
    /([-+]?\d+(\.\d+)?)\s*[NS],\s*([-+]?\d+(\.\d+)?)\s*[EW]/i
  ];
  
  for (const pattern of patterns) {
    const match = location.match(pattern);
    if (match) {
      // Different patterns have coordinates in different capture groups
      let lat, lng;
      
      if (pattern.source.includes('latitude')) {
        lat = parseFloat(match[1]);
        lng = parseFloat(match[3]);
      } else if (pattern.source.includes('[NS]')) {
        lat = parseFloat(match[1]);
        lng = parseFloat(match[3]);
        // Adjust for N/S and E/W
        if (location.includes('S')) lat = -lat;
        if (location.includes('W')) lng = -lng;
      } else {
        lat = parseFloat(match[1]);
        lng = parseFloat(match[3]);
      }
      
      // Validate coordinates
      if (!isNaN(lat) && !isNaN(lng) && 
          lat >= -90 && lat <= 90 && 
          lng >= -180 && lng <= 180) {
        return { lat, lng };
      }
    }
  }
  
  return null;
};

/**
 * Extracts coordinates from text description
 * This is a simplified version that looks for coordinate patterns in text
 */
export const extractCoordinatesFromText = (text?: string): { lat: number; lng: number } | null => {
  if (!text) return null;
  return extractCoordinates(text);
};
