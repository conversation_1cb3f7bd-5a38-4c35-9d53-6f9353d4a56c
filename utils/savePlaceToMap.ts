import { doc, getDoc, setDoc, arrayUnion, updateDoc, serverTimestamp, collection, query, where, getDocs } from 'firebase/firestore';
import { db } from '../services/firebase/firebase-core';
import { SavedPlace, PlaceCategory } from '../types/SavedPlace';

/**
 * Interface for the place data to be saved to Firestore
 */
export interface PlaceToSave {
  title: string;
  description?: string;
  location: {
    lat: number;
    lng: number;
  };
  type?: PlaceCategory | string;
}

/**
 * Interface for map marker in Firestore
 */
export interface MapMarker {
  title: string;
  description: string;
  location: {
    lat: number;
    lng: number;
  };
  type: PlaceCategory | string;
  savedAt: string;
  id?: string;
}

/**
 * Save a place to the map markers collection in Firestore
 * @param userId User ID
 * @param tripId Trip ID
 * @param place Place data to save
 * @returns Promise resolving to true if successful, false otherwise
 */
/**
 * Check if a place is already saved in the user's map markers
 */
export const isPlaceSavedInMap = async (
  userId: string,
  title: string
): Promise<boolean> => {
  try {
    if (!userId || !title) return false;
    
    // Query all documents that start with userId_ using secure query
    const markersRef = collection(db, 'mapMarkers');
    const q = query(
      markersRef,
      where('__name__', '>=', `${userId}_`),
      where('__name__', '<', `${userId}_\uf8ff`)
    );
    const markersSnapshot = await getDocs(q);

    // All documents returned are already filtered for this user
    const userMarkerDocs = markersSnapshot.docs;
    
    // Check each document for a place with the given title
    for (const markerDoc of userMarkerDocs) {
      const data = markerDoc.data();
      
      if (!data.places || !Array.isArray(data.places)) continue;
      
      const placeExists = data.places.some(
        (p: any) => p.title && p.title.toLowerCase() === title.toLowerCase()
      );
      
      if (placeExists) return true;
    }
    
    return false;
  } catch (error) {
    console.error('Error checking if place is saved:', error);
    return false;
  }
};

/**
 * Save a place to the map markers collection in Firestore
 */
export const savePlaceToMap = async (
  userId: string,
  tripId: string,
  place: PlaceToSave
): Promise<boolean> => {
  try {
    if (!userId || !tripId) {
      console.error('savePlaceToMap: Missing userId or tripId');
      return false;
    }

    if (!place || !place.title) {
      console.error('savePlaceToMap: Invalid place data');
      return false;
    }

    // Validate location data
    if (!place.location || 
        typeof place.location.lat !== 'number' || 
        typeof place.location.lng !== 'number' ||
        isNaN(place.location.lat) || 
        isNaN(place.location.lng)) {
      console.error('savePlaceToMap: Invalid location data', place.location);
      return false;
    }
    
    // Check if place is already saved
    const alreadySaved = await isPlaceSavedInMap(userId, place.title);
    if (alreadySaved) {
      console.log(`Place "${place.title}" is already saved in user's map markers`);
      return true; // Return true since the place is already saved
    }

    // Create document ID using userId_tripId format
    const docId = `${userId}_${tripId}`;
    const docRef = doc(db, 'mapMarkers', docId);
    
    // Check if document exists
    const docSnap = await getDoc(docRef);
    
    // Prepare the place object to save with a unique ID
    const uniqueId = `place-${Date.now()}-${Math.floor(Math.random() * 10000)}`;
    
    const placeToSave = {
      id: uniqueId,
      title: place.title,
      description: place.description || '',
      location: {
        lat: place.location.lat,
        lng: place.location.lng
      },
      type: place.type || 'other',
      savedAt: new Date().toISOString()
    };

    if (docSnap.exists()) {
      // Document exists, update it with the new place
      await updateDoc(docRef, {
        places: arrayUnion(placeToSave),
        lastUpdated: serverTimestamp()
      });
      console.log(`Place "${place.title}" added to existing document ${docId}`);
    } else {
      // Document doesn't exist, create it with the place
      await setDoc(docRef, {
        userId,
        tripId,
        places: [placeToSave],
        createdAt: serverTimestamp(),
        lastUpdated: serverTimestamp()
      });
      console.log(`Created new document ${docId} with place "${place.title}"`);
    }

    // Double-check that the place was saved
    const verifyDocSnap = await getDoc(docRef);
    if (!verifyDocSnap.exists()) {
      console.error(`Failed to verify document ${docId} exists after saving`);
      return false;
    }
    
    const data = verifyDocSnap.data();
    if (!data.places || !Array.isArray(data.places) || data.places.length === 0) {
      console.error(`Document ${docId} exists but has no places array or it is empty`);
      return false;
    }
    
    // Check if the place with the same title exists
    const placeExists = data.places.some(
      (p: any) => p.title && p.title === place.title
    );
    
    if (!placeExists) {
      console.error(`Place "${place.title}" not found in document after saving`);
      return false;
    }
    
    console.log(`Successfully verified place "${place.title}" was saved to document ${docId}`);
    return true;
  } catch (error) {
    console.error('Error saving place to map:', error);
    return false;
  }
};

/**
 * Check if a place is already saved to the map
 * @param userId User ID
 * @param tripId Trip ID
 * @param placeTitle Title of the place to check
 * @returns Promise resolving to true if the place is already saved, false otherwise
 */
export const isPlaceSavedToMap = async (
  userId: string,
  tripId: string,
  placeTitle: string
): Promise<boolean> => {
  try {
    if (!userId || !tripId || !placeTitle) {
      return false;
    }

    const docId = `${userId}_${tripId}`;
    const docRef = doc(db, 'mapMarkers', docId);
    const docSnap = await getDoc(docRef);

    if (!docSnap.exists()) {
      return false;
    }

    const data = docSnap.data();
    if (!data.places || !Array.isArray(data.places)) {
      return false;
    }

    // Check if a place with the same title exists
    return data.places.some(
      (place: any) => 
        place.title && 
        place.title.toLowerCase() === placeTitle.toLowerCase()
    );
  } catch (error) {
    console.error('Error checking if place is saved:', error);
    return false;
  }
};
