import { SavedPlace, PlaceCategory } from '../types/SavedPlace';

/**
 * Interface representing raw place data from Firestore
 */
interface RawFirestorePlace {
  id?: string;
  title?: string;
  name?: string;
  description?: string;
  location?: {
    lat?: number | string;
    lng?: number | string;
    latitude?: number | string;
    longitude?: number | string;
  };
  type?: string;
  category?: string;
  tripId?: string;
  savedAt?: string;
  [key: string]: any; // Allow for additional fields
}

/**
 * Normalize a category string to a valid PlaceCategory
 */
const normalizeCategory = (category?: string): PlaceCategory => {
  if (!category) return 'other';
  
  const normalizedCategory = category.toLowerCase();
  
  if (normalizedCategory.includes('restaurant') || 
      normalizedCategory.includes('food') || 
      normalizedCategory.includes('cafe') || 
      normalizedCategory.includes('dining')) {
    return 'restaurant';
  }
  
  if (normalizedCategory.includes('hotel') || 
      normalizedCategory.includes('lodging') || 
      normalizedCategory.includes('accommodation')) {
    return 'hotel';
  }
  
  if (normalizedCategory.includes('attraction') || 
      normalizedCategory.includes('sight') || 
      normalizedCategory.includes('visit')) {
    return 'attraction';
  }
  
  if (normalizedCategory.includes('event') || 
      normalizedCategory.includes('show') || 
      normalizedCategory.includes('concert') || 
      normalizedCategory.includes('evening')) {
    return 'event';
  }
  
  return 'other';
};

/**
 * Convert raw Firestore data to a SavedPlace object
 * @param raw Raw place data from Firestore
 * @param tripId Optional trip ID to associate with the place
 * @returns A SavedPlace object or null if conversion fails
 */
export const convertToSavedPlace = (raw: RawFirestorePlace, tripId?: string): SavedPlace | null => {
  try {
    // Skip if raw data is null or undefined
    if (!raw) {
      console.warn('convertToSavedPlace: Raw data is null or undefined');
      return null;
    }
    
    // Extract location data, handling different possible structures
    const location = raw.location || {};
    
    // Try to get coordinates from different possible structures
    let latitude: number | undefined;
    let longitude: number | undefined;
    
    // First check if location object has lat/lng or latitude/longitude
    if (location) {
      if (typeof location.lat !== 'undefined') {
        latitude = typeof location.lat === 'string' ? parseFloat(location.lat) : Number(location.lat);
      } else if (typeof location.latitude !== 'undefined') {
        latitude = typeof location.latitude === 'string' ? parseFloat(location.latitude) : Number(location.latitude);
      }
      
      if (typeof location.lng !== 'undefined') {
        longitude = typeof location.lng === 'string' ? parseFloat(location.lng) : Number(location.lng);
      } else if (typeof location.longitude !== 'undefined') {
        longitude = typeof location.longitude === 'string' ? parseFloat(location.longitude) : Number(location.longitude);
      }
    }
    
    // If not found in location object, try direct properties
    if (typeof latitude === 'undefined') {
      if (typeof raw.lat !== 'undefined') {
        latitude = typeof raw.lat === 'string' ? parseFloat(raw.lat) : Number(raw.lat);
      } else if (typeof raw.latitude !== 'undefined') {
        latitude = typeof raw.latitude === 'string' ? parseFloat(raw.latitude) : Number(raw.latitude);
      }
    }
    
    if (typeof longitude === 'undefined') {
      if (typeof raw.lng !== 'undefined') {
        longitude = typeof raw.lng === 'string' ? parseFloat(raw.lng) : Number(raw.lng);
      } else if (typeof raw.longitude !== 'undefined') {
        longitude = typeof raw.longitude === 'string' ? parseFloat(raw.longitude) : Number(raw.longitude);
      }
    }
    
    // Validate coordinates
    if (typeof latitude !== 'number' || typeof longitude !== 'number' || 
        isNaN(latitude) || isNaN(longitude)) {
      console.warn(`convertToSavedPlace: Invalid coordinates for place "${raw.title || raw.name}"`, { 
        raw, location, latitude, longitude 
      });
      return null;
    }
    
    // Use either title or name for the place name
    const name = raw.name || raw.title || 'Unnamed Place';
    
    // Normalize the category
    const category = normalizeCategory(raw.category || raw.type);
    
    // Use provided tripId or the one from raw data
    const placeTripId = tripId || raw.tripId || 'unknown';
    
    // Generate a unique ID if none exists
    const id = raw.id || `place-${Date.now()}-${Math.floor(Math.random() * 10000)}`;
    
    // Create the SavedPlace object
    return {
      id,
      name,
      latitude,
      longitude,
      category,
      description: raw.description,
      tripId: placeTripId,
      savedAt: raw.savedAt || new Date().toISOString()
    };
  } catch (error) {
    console.error('Error converting to SavedPlace:', error);
    return null;
  }
};
