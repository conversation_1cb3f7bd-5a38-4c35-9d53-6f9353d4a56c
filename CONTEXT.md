# 🌍 AI-Powered Travel App

## Tech Stack
-Frontend: React Native with -TypeScript, Expo, and Expo Router
-Backend/Database: Firebase
-UI Framework: React Native Paper
-AI Processing: OpenAI

## App Overview

This travel app leverages artificial intelligence to help users plan, organize, and manage their trips in a personalized and cost-effective way. The app includes a welcome screen, a main dashboard, fast planning features, a map interface with saved locations, and a user profile section.

## 🚀 Onboarding Flow

### 1. Welcome Screen
- Clean, minimalist UI
- App name and logo
- CTA buttons:
  - "Sign Up with Email"
  - "Log In"

### 2. Sign Up / Login
- Email & password input fields
- Firebase Authentication integration
- Validation and error handling
- On success → redirect to Main Dashboard

## 📱 App Features

### Main Dashboard (Tab 1: "Dashboard")

**Components:**
- **Travel Planner Entry Section**
  - CTA: "Plan a New Trip"
  - Takes user to travel planner wizard (AI integration)
- **Saved Trips List**
  - Card view showing:
    - Trip name / destination
    - Dates
    - View / Edit / Delete buttons
- **Travel News Section**
  - Latest country restrictions / travel news
  - Pulled from travel API or manually updated Firestore content

### Fast Planning (Tab 2: "Quick Plan")

**Use Cases:**
- Find a restaurant nearby
- Recommend an activity in a city

**Features:**
- Single-input AI assistant chat box
- Users type requests like:
  - "Find a sushi place near my hotel in Tokyo"
  - "What can I do in Prague for an afternoon?"
- AI (via GPT-3.5) returns short, specific suggestions

### Map View (Tab 3: "Map")

**Features:**
- Interactive map (Google Maps or Leaflet)
- Pins:
  - Restaurants
  - Activities
  - Custom saved locations
- **Extras:**
  - Filter by category (food, attractions, notes)
  - Tap a pin to view saved note, add to trip

### Profile Page (Tab 4: "Profile")

**Features:**
- User info (name, email, avatar)
- List of visited countries
  - Visual map highlight or list
- Subscription status (free/premium)
- Logout button

## 🤖 AI Integration

### OpenAI GPT-4 Turbo
**Used for:**
- Full trip generation (3–10 days)
- Daily plans with activities and food suggestions

### OpenAI GPT-3.5 Turbo
**Used for:**
- Fast responses in Quick Plan tab
- Lightweight activity or restaurant suggestions

**Prompt Optimization:**
- Token limits set per request (800–1200 max)
- Caching identical requests
- Fallback to pre-generated popular plans

## 💾 Data Storage

### Firebase Firestore Collections:
- `users` – user profile data and preferences
- `trips` – trip plans with date and AI response
- `places` – saved pins from the map
- `ai_logs` – usage tracking for cost control
- `news` – travel updates and alerts

**Authentication:**
- Firebase Auth for email-based login/signup

## 📊 Database Schema

### `users` Collection
```
{
  userId: string,                 // Primary key, matches Firebase Auth UID
  email: string,                  // User's email address
  displayName: string,            // User's display name
  photoURL: string,               // Profile picture URL
  createdAt: timestamp,           // Account creation date
  lastLogin: timestamp,           // Last login timestamp
  subscription: {
    type: string,                 // "free" or "premium"
    expiresAt: timestamp,         // Subscription expiration date
    stripeCustomerId: string      // For premium users
  },
  preferences: {
    currency: string,             // Preferred currency (USD, EUR, etc.)
    notifications: boolean,       // Notification preferences
    language: string,             // App language preference
    darkMode: boolean             // UI theme preference
  },
  visitedCountries: [             // Array of visited countries
    {
      countryCode: string,        // ISO country code
      visitedAt: timestamp        // When they marked as visited
    }
  ]
}
```

### `trips` Collection
```
{
  tripId: string,                 // Primary key
  userId: string,                 // Foreign key to users collection
  name: string,                   // Trip name
  destination: {
    city: string,                 // Primary city/location
    country: string,              // Country
    coordinates: {                // Geographic coordinates
      latitude: number,
      longitude: number
    }
  },
  dateRange: {
    startDate: timestamp,         // Trip start date
    endDate: timestamp            // Trip end date
  },
  createdAt: timestamp,           // When trip was created
  updatedAt: timestamp,           // When trip was last updated
  status: string,                 // "planning", "active", "completed", "cancelled"
  aiGenerated: {
    prompt: string,               // User's original prompt to AI
    fullResponse: string,         // Complete AI response
    model: string,                // AI model used
    generatedAt: timestamp        // When the plan was generated
  },
  days: [                         // Array of daily plans
    {
      dayNumber: number,          // Day 1, Day 2, etc.
      date: timestamp,            // Actual calendar date
      activities: [               // Array of planned activities
        {
          activityId: string,     // Unique ID for this activity
          name: string,           // Activity name
          description: string,    // Activity description
          category: string,       // "sightseeing", "food", "transport", etc.
          location: {             // Location details
            name: string,         // Place name
            address: string,      // Physical address
            coordinates: {        // Geographic coordinates
              latitude: number,
              longitude: number
            }
          },
          timeSlot: {             // When activity is scheduled
            startTime: timestamp,
            endTime: timestamp
          },
          cost: {                 // Optional cost information
            amount: number,
            currency: string
          },
          notes: string,          // User notes
          completed: boolean      // Marked as done?
        }
      ]
    }
  ],
  totalBudget: number,            // Optional overall trip budget
  accommodation: {                // Accommodation details
    name: string,                 // Hotel/Airbnb name
    address: string,              // Physical address
    coordinates: {                // Geographic coordinates
      latitude: number,
      longitude: number
    },
    checkIn: timestamp,           // Check-in time
    checkOut: timestamp,          // Check-out time
    confirmationNumber: string,   // Booking reference
    notes: string                 // Additional information
  }
}
```

### `places` Collection
```
{
  placeId: string,                // Primary key
  userId: string,                 // Foreign key to users collection
  tripId: string,                 // Optional foreign key to trips collection
  name: string,                   // Place name
  category: string,               // "restaurant", "attraction", "hotel", "note", etc.
  coordinates: {                  // Geographic coordinates
    latitude: number,
    longitude: number
  },
  address: string,                // Physical address
  description: string,            // User description/notes
  photos: [string],               // Array of photo URLs
  rating: number,                 // User rating (1-5)
  savedAt: timestamp,             // When place was saved
  visitedAt: timestamp,           // When place was visited (if applicable)
  source: string                  // "user", "ai", "google", etc.
}
```

### `ai_logs` Collection
```
{
  logId: string,                  // Primary key
  userId: string,                 // Foreign key to users collection
  timestamp: timestamp,           // When request was made
  model: string,                  // AI model used
  prompt: string,                 // User prompt
  tokensUsed: {                   // Token usage metrics
    prompt: number,               // Input tokens
    completion: number,           // Output tokens
    total: number                 // Total tokens
  },
  status: string,                 // "success", "error"
  errorMessage: string,           // If status is "error"
  responseTime: number,           // Time to generate response (ms)
  cost: number,                   // Calculated cost of request
  cache: {                        // Caching information
    wasCached: boolean,           // Was this a cached response?
    cacheKey: string              // Cache identifier
  }
}
```

### `news` Collection
```
{
  newsId: string,                 // Primary key
  title: string,                  // News headline
  content: string,                // Full article text
  summary: string,                // Short summary for cards
  countryCode: string,            // ISO country code (if country-specific)
  category: string,               // "restriction", "alert", "update", etc.
  severity: string,               // "info", "warning", "alert"
  publishedAt: timestamp,         // Publication date
  expiresAt: timestamp,           // Expiration date (if applicable)
  source: {                       // Source information
    name: string,                 // Source name
    url: string                   // Original URL
  },
  imageUrl: string                // Optional feature image
}
```

## 📁 Folder Structure

```
travel-app/
├── app/                           # Main app folder (Expo Router)
│   ├── _layout.tsx                # Root layout with tab navigation
│   ├── index.tsx                  # Entry redirect to onboarding or dashboard
│   ├── (auth)/                    # Authentication screens
│   │   ├── login.tsx              # Login screen
│   │   ├── signup.tsx             # Signup screen
│   │   └── forgot-password.tsx    # Password recovery
│   ├── (main)/                    # Main app screens (post-auth)
│   │   ├── _layout.tsx            # Main app tab layout
│   │   ├── dashboard/             # Dashboard tab
│   │   │   ├── index.tsx          # Dashboard main screen
│   │   │   └── [tripId].tsx       # Trip details screen
│   │   ├── quick-plan/            # Quick Plan tab
│   │   │   └── index.tsx          # AI quick planning screen
│   │   ├── map/                   # Map tab
│   │   │   ├── index.tsx          # Map main screen
│   │   │   └── place/[placeId].tsx # Place details
│   │   └── profile/               # Profile tab
│   │       ├── index.tsx          # Profile main screen
│   │       ├── visited-countries.tsx # Visited countries screen
│   │       └── settings.tsx       # App settings
│   ├── trip/                      # Trip-related screens
│   │   ├── create.tsx             # Create trip screen
│   │   ├── [tripId]/              # Trip-specific screens
│   │   │   ├── edit.tsx           # Edit trip
│   │   │   ├── day/[dayNum].tsx   # Day details view
│   │   │   └── share.tsx          # Share trip
│   │   └── wizard/                # Trip creation wizard
│   │       ├── index.tsx          # Wizard start
│   │       ├── destination.tsx    # Set destination
│   │       ├── dates.tsx          # Set dates
│   │       └── generate.tsx       # AI plan generation
│   └── modal/                     # Modal screens
│       ├── add-place.tsx          # Add place modal
│       └── activity-details.tsx   # Activity details modal
├── assets/                        # Static assets
│   ├── fonts/                     # Custom fonts
│   ├── images/                    # App images
│   └── animations/                # Lottie animations
├── components/                    # Reusable components
│   ├── common/                    # Common UI components
│   │   ├── Button.tsx             # Custom button component
│   │   ├── Card.tsx               # Custom card component
│   │   └── ...                    # Other common components
│   ├── trip/                      # Trip-related components
│   │   ├── TripCard.tsx           # Trip card component
│   │   ├── ActivityItem.tsx       # Activity list item
│   │   └── ...                    # Other trip components
│   ├── map/                       # Map-related components
│   │   ├── MapView.tsx            # Custom map view
│   │   ├── PlaceMarker.tsx        # Map marker component
│   │   └── ...                    # Other map components
│   └── profile/                   # Profile-related components
│       ├── UserInfo.tsx           # User info component
│       └── ...                    # Other profile components
├── constants/                     # App constants
│   ├── Colors.ts                  # Color definitions
│   ├── Layout.ts                  # Layout constants
│   └── Config.ts                  # App configuration
├── hooks/                         # Custom React hooks
│   ├── useAuth.ts                 # Authentication hook
│   ├── useTrips.ts                # Trips data hook
│   └── useAI.ts                   # AI integration hook
├── services/                      # Service integrations
│   ├── firebase/                  # Firebase services
│   │   ├── config.ts              # Firebase configuration
│   │   ├── auth.ts                # Authentication methods
│   │   ├── firestore.ts           # Firestore methods
│   │   └── storage.ts             # Firebase storage methods
│   ├── ai/                        # AI services
│   │   ├── openai.ts              # OpenAI API integration
│   │   ├── promptTemplates.ts     # AI prompt templates
│   │   └── caching.ts             # Response caching logic
│   └── maps/                      # Map services
│       └── googleMaps.ts          # Google Maps integration
├── utils/                         # Utility functions
│   ├── dateUtils.ts               # Date manipulation utilities
│   ├── formatters.ts              # Text/currency formatters
│   └── validators.ts              # Form validation functions
├── contexts/                      # React contexts
│   ├── AuthContext.tsx            # Authentication context
│   ├── ThemeContext.tsx           # Theme context
│   └── TripContext.tsx            # Trip data context
├── navigation/                    # Navigation helpers
│   └── linkingConfiguration.ts    # Deep linking config
├── types/                         # TypeScript type definitions
│   ├── index.ts                   # Export of all types
│   ├── trip.ts                    # Trip-related types
│   ├── user.ts                    # User-related types
│   └── api.ts                     # API response types
├── app.json                       # Expo configuration
├── babel.config.js                # Babel configuration
├── tsconfig.json                  # TypeScript configuration
├── package.json                   # Project dependencies
└── README.md                      # Project documentation
```

## 🧭 Navigation Structure

| Tab | Name | Purpose |
|-----|------|---------|
| 1 | Dashboard | Home screen for trip planning and news |
| 2 | Quick Plan | Lightweight planning assistant |
| 3 | Map | Visual trip map and saved locations |
| 4 | Profile | User info, history, and subscription |

## Summary

This app is designed to make travel planning personalized, fast, and AI-assisted. With a smooth onboarding, intuitive dashboard, real-time recommendations, and interactive mapping, it creates a complete travel experience. The backend relies on Firebase and OpenAI APIs to deliver dynamic and cost-effective planning functionality.
