import React, { useState } from 'react';
import { StyleSheet, View, ScrollView, KeyboardAvoidingView, Platform, Alert } from 'react-native';
import { Button, Text, TextInput, Title, Paragraph, Card, Chip, ActivityIndicator, HelperText } from 'react-native-paper';
import { SafeAreaView } from 'react-native-safe-area-context';
import { useRouter } from 'expo-router';
import { MaterialIcons } from '@expo/vector-icons';
import { StatusBar } from 'expo-status-bar';
import DateTimePicker from '@react-native-community/datetimepicker';
import { generateTripPlan, AIModel } from '../../services/ai/openai';
// Import Firebase services
import { getFirestore, collection, addDoc, serverTimestamp } from 'firebase/firestore';
import { useAuth } from '../../contexts/AuthContext';
import TravelDates from '../../components/trip/TravelDates';
import { TouchableOpacity } from 'react-native';
import { useTranslation } from 'react-i18next';

// Helper functions to get detailed descriptions for activity levels and budget options
const getActivityLevelDescription = (activityOption: string | null): string => {
  switch (activityOption) {
    case 'Relax':
      return 'Prefers calm, slow-paced days with 1–2 key sights or activities per day. Daily walking limited to 1-2 miles (1.5-3 km). Late morning starts (9-10am) with ample downtime between activities. At least 2-3 hours of free time daily for relaxation. Includes rest days with no scheduled activities. Emphasis on leisurely meals, scenic views, and peaceful environments. No tight schedules or rushed transitions.';
    case 'Medium Activity':
      return 'Enjoys a balanced itinerary with 3–4 attractions or experiences daily. Comfortable with 3-5 miles (5-8 km) of walking per day. Regular start times (8-9am) with short breaks between activities. 1-2 hours of discretionary time daily. Mix of active exploration and relaxation periods. Includes a variety of experiences (cultural, historical, outdoor) without overwhelming pace. Prefers structure without feeling rushed or exhausted.';
    case 'Very Active':
      return 'Seeks full days packed with 5+ activities and minimal downtime. Capable of 6+ miles (10+ km) of walking daily, including stairs and varied terrain. Early starts (7-8am) to maximize daylight hours. Minimal breaks between activities (15-30 minutes). Comfortable with tight schedules and quick transitions between sites. Prioritizes seeing as much as possible, even if it means less time at each location. Willing to use early mornings and evenings for additional exploration.';
    default:
      return 'Activity level not specified';
  }
};

const getBudgetDescription = (budgetOption: string | null): string => {
  switch (budgetOption) {
    case 'Low Budget':
      return 'Budget-conscious traveler seeking value. Prefers hostels, budget hotels ($30-70/night), public transportation, free attractions, street food and affordable local restaurants ($5-15/meal). Prioritizes cost-saving options and free activities.';
    case 'Medium Budget':
      return 'Mid-range traveler with moderate spending. Prefers 3-star hotels ($70-150/night), mix of public and private transportation, paid attractions with occasional splurges, and varied dining options ($15-30/meal). Balances cost with quality experiences.';
    case 'Luxury Budget':
      return 'Premium traveler seeking high-end experiences. Prefers 4-5 star hotels or luxury rentals ($150-400+/night), private transportation, exclusive tours and experiences, and fine dining restaurants ($30-100+/meal). Prioritizes comfort, convenience and exceptional service.';
    default:
      return 'Budget preference not specified';
  }
};

function CreateTripScreen() {
  const router = useRouter();
  const { user } = useAuth();
  const { i18n } = useTranslation();
  
  // Basic trip info
  const [destination, setDestination] = useState('');
  const [startDate, setStartDate] = useState<Date | null>(null);
  const [endDate, setEndDate] = useState<Date | null>(null);
  const [budget, setBudget] = useState('');
  const [preferences, setPreferences] = useState('');
  const [loading, setLoading] = useState(false);
  
  // New form fields
  const [budgetOption, setBudgetOption] = useState<'Low Budget' | 'Medium Budget' | 'Luxury Budget' | null>(null);
  const [travelGroup, setTravelGroup] = useState<'Solo' | 'Couple' | 'Friends' | 'Family with kids' | null>(null);
  const [activityLevel, setActivityLevel] = useState<'Relax' | 'Medium Activity' | 'Very Active' | null>(null);
  const [specialInterests, setSpecialInterests] = useState<{
    History: boolean;
    Art: boolean;
    Gastronomy: boolean;
    Fashion: boolean;
    Nature: boolean;
    Technology: boolean;
    'Sports Events': boolean;
  }>({
    History: false,
    Art: false,
    Gastronomy: false,
    Fashion: false,
    Nature: false,
    Technology: false,
    'Sports Events': false,
  });
  
  const [tripPlan, setTripPlan] = useState('');
  const [selectedMonth, setSelectedMonth] = useState<string | null>(null);
  const [selectedYear, setSelectedYear] = useState<number | null>(null);
  const [duration, setDuration] = useState<number | null>(null);

  const formatDate = (date: Date) => {
    return date.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
    });
  };

  const calculateDuration = () => {
    const diffTime = Math.abs(endDate.getTime() - startDate.getTime());
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    return diffDays;
  };

  const handleStartDateChange = (date: Date) => {
    setStartDate(date);
    // If end date is before the new start date, update it
    if (date > endDate) {
      setEndDate(date);
    }
  };

  const handleEndDateChange = (date: Date) => {
    setEndDate(date);
  };

  const handleMonthSelect = (month: string, year: number) => {
    setSelectedMonth(month);
    setSelectedYear(year);
    
    // When month is selected, clear specific dates
    setStartDate(new Date());
    setEndDate(new Date(Date.now() + 7 * 24 * 60 * 60 * 1000));
  };

  const handleDurationSelect = (days: number) => {
    setDuration(days);
  };

  const handleCreateTrip = async () => {
    if (!destination || !startDate || !endDate) {
      Alert.alert('Missing Information', 'Please fill in destination and travel dates.');
      return;
    }

    if (!user) {
      Alert.alert('Authentication Required', 'Please sign in to create a trip.');
      return;
    }

    setLoading(true);

    try {
      // Gather all selected special interests
      const selectedInterests = Object.entries(specialInterests)
        .filter(([_, selected]) => selected)
        .map(([interest]) => interest)
        .join(', ');
      
      // Get detailed descriptions for activity level and budget
      const activityLevelDetail = getActivityLevelDescription(activityLevel);
      const budgetDetail = getBudgetDescription(budgetOption);

      // Create a more detailed prompt including all preferences and detailed descriptions
      const detailedPrompt = `
        Plan a trip to ${destination} from ${formatDate(startDate)} to ${formatDate(endDate)}.
        
        Budget: ${budgetOption || budget || 'Not specified'}
        Budget Details: ${budgetDetail}
        
        Travel Group: ${travelGroup || 'Not specified'}
        
        Activity Level: ${activityLevel || 'Not specified'}
        Activity Level Details: ${activityLevelDetail}
        
        Special Interests: ${selectedInterests.length > 0 ? selectedInterests : 'Not specified'}
        Additional Preferences: ${preferences || 'None'}
      `.trim();

      try {
        // Generate trip plan using AI (in a real app)
        // For now, we'll just use the prompt as the plan
        
        // Get Firestore instance
        const db = getFirestore();
        
        // Create the trip data
        const tripData = {
          userId: user.uid,
          name: `Trip to ${destination}`,
          destination: destination,
          dateRange: {
            startDate: startDate.toISOString(),
            endDate: endDate.toISOString(),
          },
          duration: duration || calculateDuration(),
          budget: budgetOption || budget || 'Not specified',
          budgetDescription: budgetDetail,
          travelGroup: travelGroup || 'Not specified',
          activityLevel: activityLevel || 'Not specified',
          activityLevelDescription: activityLevelDetail,
          specialInterests: Object.entries(specialInterests)
            .filter(([_, selected]) => selected)
            .map(([interest]) => interest),
          preferences: preferences,
          createdAt: serverTimestamp(),
          updatedAt: serverTimestamp(),
          status: 'planning',
          plan: detailedPrompt, // In a real app, this would be the AI-generated plan
          language: i18n.language || 'en' // Add current app language
        };
        
        // Add the trip to Firestore - use the correct path structure
        // Path: /tripPlans/{userId}/plans/{tripId}
        const plansCollection = collection(db, 'tripPlans', user.uid, 'plans');
        const docRef = await addDoc(plansCollection, tripData);
        
        console.log('Trip created with ID:', docRef.id);
        
        // Navigate to the trip details screen
        router.push(`/(main)/trip/itinerary/${docRef.id}`);
      } catch (error) {
        console.error('Error creating trip:', error);
        Alert.alert('Error', 'Failed to create trip. Please try again.');
      }
    } catch (error) {
      console.error('Error:', error);
      Alert.alert('Error', 'Something went wrong. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  return (
    <SafeAreaView edges={['top']} style={styles.container}>
      <StatusBar style="auto" />
      <KeyboardAvoidingView
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
        style={styles.keyboardAvoidingView}
      >
        <ScrollView contentContainerStyle={styles.scrollContent}>
          <View style={styles.header}>
            <MaterialIcons 
              name="arrow-back" 
              size={24} 
              onPress={() => router.back()}
              style={styles.backButton}
            />
            <Title style={styles.headerTitle}>Create a New Trip</Title>
          </View>

          <Card style={styles.card}>
            <Card.Content>
              <Title>Where would you like to go?</Title>
              <TextInput
                label="Destination"
                value={destination}
                onChangeText={setDestination}
                style={styles.input}
                placeholder="e.g., Tokyo, Japan"
              />

              <Title style={styles.sectionTitle}>When are you traveling?</Title>
              
              <TravelDates 
                startDate={startDate}
                endDate={endDate}
                onStartDateChange={handleStartDateChange}
                onEndDateChange={handleEndDateChange}
                onMonthSelect={handleMonthSelect}
                onDurationSelect={handleDurationSelect}
              />

              <Title style={styles.sectionTitle}>Trip Budget (Optional)</Title>
              <TextInput
                label="Budget"
                value={budget}
                onChangeText={setBudget}
                style={styles.input}
                placeholder="e.g., $2000, $500/day, Budget Friendly"
              />

              {/* Budget Options */}
              <Title style={styles.sectionTitle}>Budget</Title>
              <View style={styles.pillContainer}>
                {[
                  { key: 'Low Budget', icon: 'attach-money' as const },
                  { key: 'Medium Budget', icon: 'account-balance-wallet' as const },
                  { key: 'Luxury Budget', icon: 'stars' as const }
                ].map((item) => (
                  <TouchableOpacity 
                    key={item.key}
                    style={[
                      styles.pillButton,
                      budgetOption === item.key && styles.pillButtonSelected
                    ]}
                    onPress={() => setBudgetOption(item.key as any)}
                  >
                    <MaterialIcons 
                      name={item.icon}
                      size={20} 
                      color={budgetOption === item.key ? '#FFFFFF' : '#4A90E2'} 
                      style={styles.pillIcon} 
                    />
                    <Text style={[
                      styles.pillText,
                      budgetOption === item.key && styles.pillTextSelected
                    ]}>{item.key}</Text>
                  </TouchableOpacity>
                ))}
              </View>

              {/* Travel Group */}
              <Title style={styles.sectionTitle}>Travel Group</Title>
              <View style={styles.pillContainer}>
                {[
                  { key: 'Solo', icon: 'person' as const },
                  { key: 'Couple', icon: 'favorite' as const },
                  { key: 'Friends', icon: 'people' as const },
                  { key: 'Family with kids', icon: 'family-restroom' as const }
                ].map((item) => (
                  <TouchableOpacity 
                    key={item.key}
                    style={[
                      styles.pillButton,
                      travelGroup === item.key && styles.pillButtonSelected
                    ]}
                    onPress={() => setTravelGroup(item.key as any)}
                  >
                    <MaterialIcons 
                      name={item.icon}
                      size={20} 
                      color={travelGroup === item.key ? '#FFFFFF' : '#4A90E2'} 
                      style={styles.pillIcon} 
                    />
                    <Text style={[
                      styles.pillText,
                      travelGroup === item.key && styles.pillTextSelected
                    ]}>{item.key}</Text>
                  </TouchableOpacity>
                ))}
              </View>

              {/* Activity Level */}
              <Title style={styles.sectionTitle}>Activity Level</Title>
              <View style={styles.pillContainer}>
                {[
                  { key: 'Relax', icon: 'beach-access' as const },
                  { key: 'Medium Activity', icon: 'directions-walk' as const },
                  { key: 'Very Active', icon: 'directions-run' as const }
                ].map((item) => (
                  <TouchableOpacity 
                    key={item.key}
                    style={[
                      styles.pillButton,
                      activityLevel === item.key && styles.pillButtonSelected
                    ]}
                    onPress={() => setActivityLevel(item.key as any)}
                  >
                    <MaterialIcons 
                      name={item.icon}
                      size={20} 
                      color={activityLevel === item.key ? '#FFFFFF' : '#4A90E2'} 
                      style={styles.pillIcon} 
                    />
                    <Text style={[
                      styles.pillText,
                      activityLevel === item.key && styles.pillTextSelected
                    ]}>{item.key}</Text>
                  </TouchableOpacity>
                ))}
              </View>

              {/* Special Interests */}
              <Title style={styles.sectionTitle}>Special Interests</Title>
              <View style={styles.pillContainer}>
                {[
                  { key: 'History', icon: 'account-balance' as const },
                  { key: 'Art', icon: 'palette' as const },
                  { key: 'Gastronomy', icon: 'restaurant' as const },
                  { key: 'Fashion', icon: 'shopping-bag' as const },
                  { key: 'Nature', icon: 'park' as const },
                  { key: 'Technology', icon: 'devices' as const },
                  { key: 'Sports Events', icon: 'sports-basketball' as const }
                ].map((item) => (
                  <TouchableOpacity 
                    key={item.key}
                    style={[
                      styles.pillButton,
                      specialInterests[item.key as keyof typeof specialInterests] && styles.pillButtonSelected
                    ]}
                    onPress={() => setSpecialInterests(prev => ({
                      ...prev,
                      [item.key]: !prev[item.key as keyof typeof specialInterests]
                    }))}
                  >
                    <MaterialIcons 
                      name={item.icon}
                      size={20} 
                      color={specialInterests[item.key as keyof typeof specialInterests] ? '#FFFFFF' : '#4A90E2'} 
                      style={styles.pillIcon} 
                    />
                    <Text style={[
                      styles.pillText,
                      specialInterests[item.key as keyof typeof specialInterests] && styles.pillTextSelected
                    ]}>{item.key}</Text>
                  </TouchableOpacity>
                ))}
              </View>

              <Title style={styles.sectionTitle}>Travel Preferences (Optional)</Title>
              <TextInput
                label="Preferences"
                value={preferences}
                onChangeText={setPreferences}
                style={styles.input}
                placeholder="e.g., Food, Culture, Outdoors, Family-friendly"
                multiline
                numberOfLines={3}
              />

              <Paragraph style={styles.aiExplainer}>
                Our AI will generate a personalized trip plan based on your preferences.
              </Paragraph>
            </Card.Content>
            <Card.Actions style={styles.cardActions}>
              <Button 
                mode="outlined" 
                onPress={() => router.back()}
                style={styles.cancelButton}
              >
                Cancel
              </Button>
              <Button 
                mode="contained" 
                onPress={handleCreateTrip}
                style={styles.createButton}
                loading={loading}
                disabled={loading || !destination}
              >
                Create Trip
              </Button>
            </Card.Actions>
          </Card>

          {loading && (
            <View style={styles.loadingContainer}>
              <ActivityIndicator size="large" color="#3498db" />
              <Text style={styles.loadingText}>
                Creating your personalized trip plan...
              </Text>
              <Text style={styles.loadingSubtext}>
                This may take a minute or two.
              </Text>
            </View>
          )}
        </ScrollView>
      </KeyboardAvoidingView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  keyboardAvoidingView: {
    flex: 1,
  },
  scrollContent: {
    padding: 16,
    paddingBottom: 40,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 20,
  },
  backButton: {
    marginRight: 16,
  },
  headerTitle: {
    fontSize: 24,
    flex: 1,
  },
  card: {
    marginBottom: 16,
  },
  input: {
    marginBottom: 16,
  },
  sectionTitle: {
    fontSize: 24,
    marginTop: 16,
    marginBottom: 12,
    fontWeight: 'bold',
  },
  aiExplainer: {
    marginTop: 16,
    fontStyle: 'italic',
    opacity: 0.7,
  },
  cardActions: {
    justifyContent: 'flex-end',
    paddingTop: 8,
  },
  cancelButton: {
    marginRight: 8,
  },
  createButton: {
    minWidth: 120,
  },
  loadingContainer: {
    padding: 24,
    alignItems: 'center',
    justifyContent: 'center',
  },
  loadingText: {
    marginTop: 16,
    fontSize: 16,
    textAlign: 'center',
  },
  loadingSubtext: {
    marginTop: 8,
    fontSize: 14,
    opacity: 0.7,
    textAlign: 'center',
  },
  pillContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    marginBottom: 24,
    gap: 10,
  },
  pillButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 10,
    paddingHorizontal: 16,
    marginBottom: 8,
    borderRadius: 100, // Fully rounded
    backgroundColor: '#F0F4F8',
  },
  pillButtonSelected: {
    backgroundColor: '#4A90E2',
    borderColor: '#4A90E2',
  },
  pillIcon: {
    marginRight: 8,
  },
  pillText: {
    fontSize: 16,
    color: '#333',
  },
  pillTextSelected: {
    color: '#FFFFFF',
    fontWeight: '500',
  },
});

export default CreateTripScreen;