import React, { useState, useRef, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  TextInput,
  ScrollView,
  Modal,
  TouchableWithoutFeedback,
  Alert,
  KeyboardAvoidingView,
  Platform
} from 'react-native';
import { useRouter } from 'expo-router';
import { useTheme } from 'react-native-paper';
import { SafeAreaView } from 'react-native-safe-area-context';
import { Feather, MaterialIcons } from '@expo/vector-icons';
import { StatusBar } from 'expo-status-bar';
import { useTranslation } from 'react-i18next';
import i18n from '../../../src/i18n';
import { useAuth } from '../../../contexts/AuthContext';
import SimpleDatePicker from '../../../components/calendar/SimpleDatePicker';
// Import Firebase services
import { getFirestore, collection, addDoc, serverTimestamp, getDoc, updateDoc, doc } from 'firebase/firestore';
import { getFunctions, httpsCallable } from 'firebase/functions';

// Trip length options
const TRIP_LENGTHS = [
  { label: '3-5 days', value: '3-5' },
  { label: '1 week', value: '7' },
  { label: '10 days', value: '10' },
  { label: '2 weeks', value: '14' },
  { label: '3 weeks', value: '21' },
  { label: '1 month', value: '30' },
];

export default function SpecificAttractionsScreen() {
  const router = useRouter();
  const { colors } = useTheme();
  const { t } = useTranslation();
  const { user } = useAuth();

  // Form state
  const [destination, setDestination] = useState('');
  const [startDate, setStartDate] = useState(new Date());
  const [endDate, setEndDate] = useState(new Date(Date.now() + 7 * 24 * 60 * 60 * 1000));
  const [useDaysInstead, setUseDaysInstead] = useState(false);
  const [numberOfDays, setNumberOfDays] = useState('7');

  // Attractions and requests
  const [cityAttractions, setCityAttractions] = useState<string[]>([]);
  const [currentCityAttraction, setCurrentCityAttraction] = useState('');
  const [mustSeeItems, setMustSeeItems] = useState<string[]>([]);
  const [currentMustSee, setCurrentMustSee] = useState('');
  const [customRequests, setCustomRequests] = useState<string[]>([]);
  const [currentCustomRequest, setCurrentCustomRequest] = useState('');

  // Trip preferences
  const [budget, setBudget] = useState<string | null>(null);
  const [travelGroup, setTravelGroup] = useState<string | null>(null);
  const [activityLevel, setActivityLevel] = useState<string | null>(null);
  const [selectedInterests, setSelectedInterests] = useState<string[]>([t('specificAttractions.sightseeing')]);
  const [foodPreferences, setFoodPreferences] = useState<string[]>([t('specificAttractions.localFood')]);

  // UI state
  const [showStartCalendar, setShowStartCalendar] = useState(false);
  const [showEndCalendar, setShowEndCalendar] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);

  const handleBackPress = () => {
    router.back();
  };

  // Helper functions
  const formatDate = (date: Date) => {
    return date.toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric',
      year: 'numeric'
    });
  };

  const handleStartDateSelect = (date: Date) => {
    setStartDate(date);
    setShowStartCalendar(false);

    // Auto-adjust end date if it's before start date
    if (endDate <= date) {
      const newEndDate = new Date(date);
      newEndDate.setDate(date.getDate() + 7);
      setEndDate(newEndDate);
    }
  };

  const handleEndDateSelect = (date: Date) => {
    setEndDate(date);
    setShowEndCalendar(false);
  };

  // Tag management functions
  const addCityAttraction = () => {
    console.log('=== ADD CITY ATTRACTION CALLED ===');
    console.log('Current input:', currentCityAttraction);
    console.log('Current attractions array:', cityAttractions);

    if (currentCityAttraction.trim() && !cityAttractions.includes(currentCityAttraction.trim())) {
      console.log('Adding city attraction:', currentCityAttraction.trim());
      const newAttractions = [...cityAttractions, currentCityAttraction.trim()];
      console.log('New attractions array will be:', newAttractions);
      setCityAttractions(newAttractions);
      setCurrentCityAttraction('');
    } else {
      console.log('Not adding - either empty or already exists');
      console.log('Trimmed input:', currentCityAttraction.trim());
      console.log('Already includes?', cityAttractions.includes(currentCityAttraction.trim()));
    }
  };

  const removeCityAttraction = (attraction: string) => {
    setCityAttractions(cityAttractions.filter(item => item !== attraction));
  };

  const addMustSeeItem = () => {
    console.log('=== ADD MUST SEE ITEM CALLED ===');
    console.log('Current input:', currentMustSee);
    console.log('Current must see array:', mustSeeItems);

    if (currentMustSee.trim() && !mustSeeItems.includes(currentMustSee.trim())) {
      console.log('Adding must see item:', currentMustSee.trim());
      const newMustSeeItems = [...mustSeeItems, currentMustSee.trim()];
      console.log('New must see array will be:', newMustSeeItems);
      setMustSeeItems(newMustSeeItems);
      setCurrentMustSee('');
    } else {
      console.log('Not adding - either empty or already exists');
    }
  };

  const removeMustSeeItem = (item: string) => {
    setMustSeeItems(mustSeeItems.filter(mustSee => mustSee !== item));
  };

  const addCustomRequest = () => {
    console.log('=== ADD CUSTOM REQUEST CALLED ===');
    console.log('Current input:', currentCustomRequest);
    console.log('Current custom requests array:', customRequests);

    if (currentCustomRequest.trim() && !customRequests.includes(currentCustomRequest.trim())) {
      console.log('Adding custom request:', currentCustomRequest.trim());
      const newCustomRequests = [...customRequests, currentCustomRequest.trim()];
      console.log('New custom requests array will be:', newCustomRequests);
      setCustomRequests(newCustomRequests);
      setCurrentCustomRequest('');
    } else {
      console.log('Not adding - either empty or already exists');
    }
  };

  const removeCustomRequest = (request: string) => {
    setCustomRequests(customRequests.filter(req => req !== request));
  };

  // Interest selection
  const toggleInterest = (interest: string) => {
    if (selectedInterests.includes(interest)) {
      setSelectedInterests(selectedInterests.filter(item => item !== interest));
    } else {
      setSelectedInterests([...selectedInterests, interest]);
    }
  };

  // Food preferences selection
  const toggleFoodPreference = (preference: string) => {
    if (foodPreferences.includes(preference)) {
      setFoodPreferences(foodPreferences.filter(item => item !== preference));
    } else {
      setFoodPreferences([...foodPreferences, preference]);
    }
  };

  // Form validation
  const validateForm = () => {
    if (!destination.trim()) {
      Alert.alert(t('specificAttractions.missingInformation'), t('specificAttractions.enterDestination'));
      return false;
    }
    if (!useDaysInstead && (!startDate || !endDate)) {
      Alert.alert(t('specificAttractions.missingInformation'), t('specificAttractions.selectTravelDates'));
      return false;
    }
    if (useDaysInstead && (!numberOfDays || parseInt(numberOfDays) < 1)) {
      Alert.alert(t('specificAttractions.missingInformation'), t('specificAttractions.enterValidDays'));
      return false;
    }
    return true;
  };

  // Form submission
  const handleSubmit = async () => {
    console.log('=== FORM SUBMISSION STARTED ===');
    console.log('User:', user ? 'Authenticated' : 'Not authenticated');
    console.log('User UID:', user?.uid);

    const formValid = validateForm();
    console.log('Form validation result:', formValid);

    if (!formValid || !user) {
      console.log('Form submission stopped - validation failed or user not authenticated');
      return;
    }

    console.log('Form validation passed, proceeding with submission...');
    setIsSubmitting(true);

    try {
      const db = getFirestore();

      // Capture any text currently in the input fields and add to arrays
      const finalCityAttractions = [...cityAttractions];
      const finalMustSeeItems = [...mustSeeItems];
      const finalCustomRequests = [...customRequests];

      // Add current city attraction input if it has content (handle comma-separated values)
      if (currentCityAttraction.trim()) {
        const cityAttractionItems = currentCityAttraction.split(',').map(item => item.trim()).filter(item => item.length > 0);
        cityAttractionItems.forEach(item => {
          if (!finalCityAttractions.includes(item)) {
            finalCityAttractions.push(item);
            console.log('Added city attraction:', item);
          }
        });
      }

      // Add current must-see input if it has content (handle comma-separated values)
      if (currentMustSee.trim()) {
        const mustSeeItemsList = currentMustSee.split(',').map(item => item.trim()).filter(item => item.length > 0);
        mustSeeItemsList.forEach(item => {
          if (!finalMustSeeItems.includes(item)) {
            finalMustSeeItems.push(item);
            console.log('Added must-see item:', item);
          }
        });
      }

      // Add current custom request input if it has content (handle comma-separated values)
      if (currentCustomRequest.trim()) {
        const customRequestItems = currentCustomRequest.split(',').map(item => item.trim()).filter(item => item.length > 0);
        customRequestItems.forEach(item => {
          if (!finalCustomRequests.includes(item)) {
            finalCustomRequests.push(item);
            console.log('Added custom request:', item);
          }
        });
      }

      // Debug: Log final array contents before saving
      console.log('=== FINAL ARRAY STATES AT SUBMISSION ===');
      console.log('Final city attractions:', finalCityAttractions);
      console.log('Final must see items:', finalMustSeeItems);
      console.log('Final custom requests:', finalCustomRequests);
      console.log('Selected interests:', selectedInterests);
      console.log('Food preferences:', foodPreferences);

      // Prepare travel dates data (fix timezone issue by using local date formatting)
      const formatDateForStorage = (date: Date) => {
        const year = date.getFullYear();
        const month = String(date.getMonth() + 1).padStart(2, '0');
        const day = String(date.getDate()).padStart(2, '0');
        return `${year}-${month}-${day}`;
      };

      const travelDates = useDaysInstead
        ? { number_of_days: parseInt(numberOfDays) }
        : {
            start_date: formatDateForStorage(startDate),
            end_date: formatDateForStorage(endDate)
          };

      // Prepare trip data using final arrays (including current input values)
      const tripData = {
        destination: destination.trim(),
        travel_dates: travelDates,
        city_attractions: finalCityAttractions,
        must_see: finalMustSeeItems,
        custom_requests: finalCustomRequests,
        budget: budget,
        travel_group: travelGroup,
        activity_level: activityLevel,
        interests: selectedInterests || [],
        food_preferences: foodPreferences || [],
        trip_type: 'specific_attractions',
        user_id: user.uid,
        timestamp: serverTimestamp(),
        status: 'planning',
        // Add fields to match regular trip structure for consistency
        userId: user.uid,
        userName: user.displayName || user.email,
        createdAt: serverTimestamp(),
        updatedAt: serverTimestamp(),
        saved: true,
        visible: true,
        // Add current app language
        language: 'en', // Default to English for now
        // Itinerary generation tracking
        itinerary: null,
        itinerary_status: 'generating'
      };

      // Save to Firebase - using separate collection for specific attractions plans
      console.log('=== FIREBASE SAVE OPERATION ===');
      console.log('About to save to Firebase...');
      console.log('User authenticated:', !!user);
      console.log('User ID:', user.uid);
      console.log('Database instance:', !!db);
      console.log('Collection path: specificAttractionPlans/' + user.uid + '/plans');

      // Test Firebase connection with a simple write first
      console.log('Testing Firebase connection...');
      try {
        const testCollection = collection(db, 'test');
        const testDoc = await addDoc(testCollection, { test: 'connection', timestamp: new Date() });
        console.log('✅ Firebase connection test successful, test doc ID:', testDoc.id);
      } catch (testError) {
        console.error('❌ Firebase connection test failed:', testError);
        throw new Error('Firebase connection failed: ' + testError.message);
      }

      let docRef;
      try {
        const specificPlansCollection = collection(db, 'specificAttractionPlans', user.uid, 'plans');
        console.log('Collection reference created successfully:', specificPlansCollection.path);

        console.log('Trip data to save:', JSON.stringify(tripData, null, 2));
        console.log('Attempting to add document...');

        docRef = await addDoc(specificPlansCollection, tripData);

        console.log('=== FIREBASE SAVE SUCCESS ===');
        console.log('Document created with ID:', docRef.id);
        console.log('Document path:', docRef.path);
        console.log('Collection should now exist at: specificAttractionPlans/' + user.uid + '/plans');

        // Verify the document was created by reading it back
        console.log('Verifying document creation...');
        const savedDoc = await getDoc(docRef);
        if (savedDoc.exists()) {
          console.log('✅ Document verification successful');
          console.log('Saved data:', savedDoc.data());
        } else {
          console.log('❌ Document verification failed - document does not exist');
        }

        // Generate itinerary using your working Cloud Function
        console.log('=== GENERATING ITINERARY ===');
        try {
          // Call the specific attractions Cloud Function (using europe-west1 region like working functions)
          const functions = getFunctions(undefined, 'europe-west1');
          const generateItineraryFromSpecificAttractionPlans = httpsCallable(functions, 'generateItineraryFromSpecificAttractionPlans');

          // Prepare the data to send to the function
          await generateItineraryFromSpecificAttractionPlans({
            tripId: docRef.id,
            userId: user.uid
          });

          console.log('✅ Cloud Function called successfully');

          // Set status to generating - the Cloud Function will update it when complete
          await updateDoc(docRef, {
            itinerary_status: 'generating'
          });

        } catch (itineraryError) {
          console.log('Note: Cloud Function may still be processing in background');
          // Don't treat this as a failure since the itinerary is actually being generated
          await updateDoc(docRef, {
            itinerary_status: 'generating'
          });
        }

        // Navigate to itinerary or success screen
        router.push(`/(main)/trip/itinerary/${docRef.id}`);

      } catch (saveError) {
        console.error('=== FIREBASE SAVE ERROR ===');
        console.error('Save error:', saveError);
        throw saveError;
      }

    } catch (error) {
      console.error('=== ERROR SAVING TRIP PLAN ===');
      console.error('Error type:', typeof error);
      console.error('Error message:', error?.message);
      console.error('Error code:', error?.code);
      console.error('Full error object:', error);

      Alert.alert(
        t('specificAttractions.error'),
        `${t('specificAttractions.failedToSave')}\n\nError: ${error?.message || 'Unknown error'}`
      );
    } finally {
      console.log('Form submission completed, setting isSubmitting to false');
      setIsSubmitting(false);
    }
  };

  // Available interests
  const availableInterests = [
    t('specificAttractions.sightseeing'), t('specificAttractions.history'), t('specificAttractions.art'),
    t('specificAttractions.gastronomy'), t('specificAttractions.fashion'), t('specificAttractions.nature'),
    t('specificAttractions.sportsEvents'), t('specificAttractions.nightlife'), t('specificAttractions.relaxation')
  ];

  // Budget options
  const budgetOptions = [
    { label: t('specificAttractions.lowBudget'), value: 'lowBudget', icon: 'attach-money' },
    { label: t('specificAttractions.mediumBudget'), value: 'mediumBudget', icon: 'credit-card' },
    { label: t('specificAttractions.luxuryBudget'), value: 'luxuryBudget', icon: 'stars' },
  ];

  // Travel group options
  const travelGroupOptions = [
    { label: t('specificAttractions.solo'), value: 'solo', icon: 'person' },
    { label: t('specificAttractions.couple'), value: 'couple', icon: 'favorite' },
    { label: t('specificAttractions.friends'), value: 'friends', icon: 'people' },
    { label: t('specificAttractions.familyWithKids'), value: 'familyWithKids', icon: 'family-restroom' }
  ];

  // Activity level options
  const activityLevelOptions = [
    { label: t('specificAttractions.relax'), value: 'relax', icon: 'beach-access' },
    { label: t('specificAttractions.mediumActivity'), value: 'mediumActivity', icon: 'directions-walk' },
    { label: t('specificAttractions.veryActive'), value: 'veryActive', icon: 'directions-run' }
  ];

  // Food preference options
  const foodPreferenceOptions = [
    t('specificAttractions.localFood'), t('specificAttractions.vegetarian'),
    t('specificAttractions.streetFood'), t('specificAttractions.fineDining')
  ];

  return (
    <SafeAreaView style={styles.container}>
      <StatusBar style="auto" />

      {/* Start Date Modal */}
      <Modal
        transparent
        visible={showStartCalendar}
        animationType="fade"
        onRequestClose={() => setShowStartCalendar(false)}
      >
        <View style={styles.modalOverlay}>
          <View style={styles.calendarModal}>
            <View style={styles.calendarHeader}>
              <Text style={styles.calendarTitle}>Select Start Date</Text>
              <TouchableOpacity
                onPress={() => setShowStartCalendar(false)}
                hitSlop={{ top: 10, right: 10, bottom: 10, left: 10 }}
              >
                <Feather name="x" size={24} color="#3A7BF8" />
              </TouchableOpacity>
            </View>
            <SimpleDatePicker
              onDateChange={handleStartDateSelect}
              selectedStartDate={startDate}
              minDate={new Date()}
            />
          </View>
        </View>
      </Modal>

      {/* End Date Modal */}
      <Modal
        transparent
        visible={showEndCalendar}
        animationType="fade"
        onRequestClose={() => setShowEndCalendar(false)}
      >
        <View style={styles.modalOverlay}>
          <View style={styles.calendarModal}>
            <View style={styles.calendarHeader}>
              <Text style={styles.calendarTitle}>Select End Date</Text>
              <TouchableOpacity
                onPress={() => setShowEndCalendar(false)}
                hitSlop={{ top: 10, right: 10, bottom: 10, left: 10 }}
              >
                <Feather name="x" size={24} color="#3A7BF8" />
              </TouchableOpacity>
            </View>
            <SimpleDatePicker
              onDateChange={handleEndDateSelect}
              selectedStartDate={endDate}
              minDate={startDate}
            />
          </View>
        </View>
      </Modal>

      <KeyboardAvoidingView
        style={styles.keyboardAvoidingView}
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
      >
        <ScrollView style={styles.scrollView} showsVerticalScrollIndicator={false}>
          {/* Header */}
          <View style={styles.header}>
            <TouchableOpacity onPress={handleBackPress} style={styles.backButton}>
              <Feather name="arrow-left" size={24} color="#3A7BF8" />
            </TouchableOpacity>
            <Text style={styles.headerTitle}>Tripzy</Text>
            <View style={styles.placeholderRight} />
          </View>

          {/* Main Title */}
          <Text style={styles.mainTitle}>{t('specificAttractions.title')}</Text>
          <Text style={styles.subtitle}>{t('specificAttractions.subtitle')}</Text>

          {/* Destination Section */}
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>{t('specificAttractions.destination')}</Text>
            <View style={styles.inputContainer}>
              <MaterialIcons name="place" size={24} color="#607D8B" style={styles.inputIcon} />
              <TextInput
                style={styles.input}
                placeholder={t('specificAttractions.destinationPlaceholder')}
                placeholderTextColor="#607D8B"
                value={destination}
                onChangeText={setDestination}
              />
            </View>
          </View>

          {/* Travel Dates Section */}
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>{t('specificAttractions.travelDates')}</Text>

            {/* Toggle between exact dates and number of days */}
            <View style={styles.dateToggleContainer}>
              <TouchableOpacity
                style={[styles.toggleButton, !useDaysInstead && styles.toggleButtonActive]}
                onPress={() => setUseDaysInstead(false)}
              >
                <Text style={[styles.toggleButtonText, !useDaysInstead && styles.toggleButtonTextActive]}>
                  {t('specificAttractions.exactDates')}
                </Text>
              </TouchableOpacity>
              <TouchableOpacity
                style={[styles.toggleButton, useDaysInstead && styles.toggleButtonActive]}
                onPress={() => setUseDaysInstead(true)}
              >
                <Text style={[styles.toggleButtonText, useDaysInstead && styles.toggleButtonTextActive]}>
                  {t('specificAttractions.numberOfDays')}
                </Text>
              </TouchableOpacity>
            </View>

            {!useDaysInstead ? (
              <View style={styles.datesContainer}>
                <TouchableOpacity
                  style={styles.dateInput}
                  onPress={() => setShowStartCalendar(true)}
                >
                  <Feather name="calendar" size={22} color="#607D8B" style={styles.inputIcon} />
                  <Text style={styles.selectedDateText}>
                    {formatDate(startDate)}
                  </Text>
                </TouchableOpacity>

                <TouchableOpacity
                  style={styles.dateInput}
                  onPress={() => setShowEndCalendar(true)}
                >
                  <Feather name="calendar" size={22} color="#607D8B" style={styles.inputIcon} />
                  <Text style={styles.selectedDateText}>
                    {formatDate(endDate)}
                  </Text>
                </TouchableOpacity>
              </View>
            ) : (
              <View style={styles.inputContainer}>
                <MaterialIcons name="schedule" size={24} color="#607D8B" style={styles.inputIcon} />
                <TextInput
                  style={styles.input}
                  placeholder={t('specificAttractions.numberOfDaysPlaceholder')}
                  placeholderTextColor="#607D8B"
                  value={numberOfDays}
                  onChangeText={setNumberOfDays}
                  keyboardType="numeric"
                />
              </View>
            )}
          </View>

          {/* City Attractions Section */}
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>{t('specificAttractions.cityAttractions')}</Text>
            <Text style={styles.sectionSubtitle}>
              {t('specificAttractions.cityAttractionsSubtitle', { destination: destination || t('specificAttractions.destination').toLowerCase() })}
              {'\n'}You can add multiple items separated by commas.
            </Text>

            <View style={styles.inputContainer}>
              <MaterialIcons name="location-on" size={24} color="#607D8B" style={styles.inputIcon} />
              <TextInput
                style={styles.input}
                placeholder="e.g. Eiffel Tower, Louvre Museum, Arc de Triomphe"
                placeholderTextColor="#607D8B"
                value={currentCityAttraction}
                onChangeText={setCurrentCityAttraction}
                returnKeyType="done"
                multiline={true}
                numberOfLines={2}
                textAlignVertical="top"
              />
            </View>

            {cityAttractions.length > 0 && (
              <View style={styles.tagsContainer}>
                {cityAttractions.map((attraction, index) => (
                  <View key={index} style={styles.tag}>
                    <Text style={styles.tagText}>{attraction}</Text>
                    <TouchableOpacity onPress={() => removeCityAttraction(attraction)}>
                      <MaterialIcons name="close" size={16} color="#3A7BF8" />
                    </TouchableOpacity>
                  </View>
                ))}
              </View>
            )}
          </View>

          {/* Must-See Items Section */}
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>{t('specificAttractions.mustSeeItems')}</Text>
            <Text style={styles.sectionSubtitle}>{t('specificAttractions.mustSeeItemsSubtitle')}</Text>

            <View style={styles.inputContainer}>
              <MaterialIcons name="star" size={24} color="#607D8B" style={styles.inputIcon} />
              <TextInput
                style={styles.input}
                placeholder="e.g. Sunset at Trocadéro, Seine River cruise, Montmartre district"
                placeholderTextColor="#607D8B"
                value={currentMustSee}
                onChangeText={setCurrentMustSee}
                returnKeyType="done"
                multiline={true}
                numberOfLines={2}
                textAlignVertical="top"
              />
            </View>

            {mustSeeItems.length > 0 && (
              <View style={styles.tagsContainer}>
                {mustSeeItems.map((item, index) => (
                  <View key={index} style={styles.tag}>
                    <Text style={styles.tagText}>{item}</Text>
                    <TouchableOpacity onPress={() => removeMustSeeItem(item)}>
                      <MaterialIcons name="close" size={16} color="#3A7BF8" />
                    </TouchableOpacity>
                  </View>
                ))}
              </View>
            )}
          </View>

          {/* Custom Requests Section */}
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>{t('specificAttractions.customRequests')}</Text>
            <Text style={styles.sectionSubtitle}>{t('specificAttractions.customRequestsSubtitle')}</Text>

            <View style={styles.inputContainer}>
              <MaterialIcons name="edit" size={24} color="#607D8B" style={styles.inputIcon} />
              <TextInput
                style={styles.input}
                placeholder="e.g. Visit during golden hour, Avoid crowded times, Include photo spots"
                placeholderTextColor="#607D8B"
                value={currentCustomRequest}
                onChangeText={setCurrentCustomRequest}
                returnKeyType="done"
                multiline
                numberOfLines={2}
              />
            </View>

            {customRequests.length > 0 && (
              <View style={styles.tagsContainer}>
                {customRequests.map((request, index) => (
                  <View key={index} style={styles.tag}>
                    <Text style={styles.tagText}>{request}</Text>
                    <TouchableOpacity onPress={() => removeCustomRequest(request)}>
                      <MaterialIcons name="close" size={16} color="#3A7BF8" />
                    </TouchableOpacity>
                  </View>
                ))}
              </View>
            )}
          </View>

          {/* Trip Preferences Section */}
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>{t('specificAttractions.tripPreferences')}</Text>
            <Text style={styles.sectionSubtitle}>{t('specificAttractions.tripPreferencesSubtitle')}</Text>

            {/* Budget */}
            <View style={styles.preferenceSection}>
              <Text style={styles.preferenceSectionTitle}>{t('specificAttractions.budget')}</Text>
              <View style={styles.budgetOptionsContainer}>
                {budgetOptions.map((option) => (
                  <TouchableOpacity
                    key={option.value}
                    style={[
                      styles.budgetOption,
                      budget === option.value && styles.budgetOptionSelected
                    ]}
                    onPress={() => setBudget(option.value)}
                  >
                    <MaterialIcons
                      name={option.icon}
                      size={24}
                      color={budget === option.value ? "#FFFFFF" : "#3A7BF8"}
                    />
                    <Text
                      style={[
                        styles.budgetOptionText,
                        budget === option.value && styles.budgetOptionTextSelected
                      ]}
                    >
                      {option.label}
                    </Text>
                  </TouchableOpacity>
                ))}
              </View>
            </View>

            {/* Travel Group */}
            <View style={styles.preferenceSection}>
              <Text style={styles.preferenceSectionTitle}>{t('specificAttractions.travelGroup')}</Text>
              <View style={styles.travelGroupContainer}>
                {travelGroupOptions.map((option) => (
                  <TouchableOpacity
                    key={option.value}
                    style={[
                      styles.travelGroupOption,
                      travelGroup === option.value && styles.travelGroupOptionSelected
                    ]}
                    onPress={() => setTravelGroup(option.value)}
                  >
                    <MaterialIcons
                      name={option.icon}
                      size={24}
                      color={travelGroup === option.value ? "#FFFFFF" : "#3A7BF8"}
                    />
                    <Text
                      style={[
                        styles.travelGroupOptionText,
                        travelGroup === option.value && styles.travelGroupOptionTextSelected
                      ]}
                    >
                      {option.label}
                    </Text>
                  </TouchableOpacity>
                ))}
              </View>
            </View>

            {/* Activity Level */}
            <View style={styles.preferenceSection}>
              <Text style={styles.preferenceSectionTitle}>{t('specificAttractions.activityLevel')}</Text>
              <View style={styles.activityLevelContainer}>
                {activityLevelOptions.map((option) => (
                  <TouchableOpacity
                    key={option.value}
                    style={[
                      styles.activityLevelOption,
                      activityLevel === option.value && styles.activityLevelOptionSelected
                    ]}
                    onPress={() => setActivityLevel(option.value)}
                  >
                    <MaterialIcons
                      name={option.icon}
                      size={24}
                      color={activityLevel === option.value ? "#FFFFFF" : "#3A7BF8"}
                    />
                    <Text
                      style={[
                        styles.activityLevelOptionText,
                        activityLevel === option.value && styles.activityLevelOptionTextSelected
                      ]}
                    >
                      {option.label}
                    </Text>
                  </TouchableOpacity>
                ))}
              </View>
            </View>

            {/* Special Interests */}
            <View style={styles.preferenceSection}>
              <Text style={styles.preferenceSectionTitle}>{t('specificAttractions.specialInterests')}</Text>
              <Text style={styles.preferenceSectionSubtitle}>{t('specificAttractions.specialInterestsSubtitle')}</Text>
              <View style={styles.interestsContainer}>
                {availableInterests.map((interest) => (
                  <TouchableOpacity
                    key={interest}
                    style={[
                      styles.interestTag,
                      selectedInterests.includes(interest) && styles.interestTagSelected
                    ]}
                    onPress={() => toggleInterest(interest)}
                  >
                    <Text
                      style={[
                        styles.interestTagText,
                        selectedInterests.includes(interest) && styles.interestTagTextSelected
                      ]}
                    >
                      {interest}
                    </Text>
                  </TouchableOpacity>
                ))}
              </View>
            </View>

            {/* Food Preferences */}
            <View style={styles.preferenceSection}>
              <Text style={styles.preferenceSectionTitle}>{t('specificAttractions.foodPreferences')}</Text>
              <View style={styles.foodPreferencesContainer}>
                <View style={styles.foodPreferencesRow}>
                  {foodPreferenceOptions.slice(0, 2).map((preference) => (
                    <TouchableOpacity
                      key={preference}
                      style={styles.foodPreferenceOption}
                      onPress={() => toggleFoodPreference(preference)}
                    >
                      {foodPreferences.includes(preference) ? (
                        <View style={styles.foodCheckboxSelected}>
                          <Feather name="check" size={16} color="#FFFFFF" />
                        </View>
                      ) : (
                        <View style={styles.foodCheckboxUnselected} />
                      )}
                      <Text style={styles.foodPreferenceOptionText}>{preference}</Text>
                    </TouchableOpacity>
                  ))}
                </View>
                <View style={styles.foodPreferencesRow}>
                  {foodPreferenceOptions.slice(2).map((preference) => (
                    <TouchableOpacity
                      key={preference}
                      style={styles.foodPreferenceOption}
                      onPress={() => toggleFoodPreference(preference)}
                    >
                      {foodPreferences.includes(preference) ? (
                        <View style={styles.foodCheckboxSelected}>
                          <Feather name="check" size={16} color="#FFFFFF" />
                        </View>
                      ) : (
                        <View style={styles.foodCheckboxUnselected} />
                      )}
                      <Text style={styles.foodPreferenceOptionText}>{preference}</Text>
                    </TouchableOpacity>
                  ))}
                </View>
              </View>
            </View>
          </View>

          {/* Submit Button */}
          <View style={styles.submitContainer}>
            <TouchableOpacity
              style={[styles.submitButton, isSubmitting && styles.submitButtonDisabled]}
              onPress={handleSubmit}
              disabled={isSubmitting}
            >
              <Text style={styles.submitButtonText}>
                {isSubmitting ? t('specificAttractions.creatingTripPlan') : t('specificAttractions.createTripPlan')}
              </Text>
            </TouchableOpacity>
          </View>
        </ScrollView>
      </KeyboardAvoidingView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#FFFFFF',
  },
  keyboardAvoidingView: {
    flex: 1,
  },
  scrollView: {
    flex: 1,
    paddingHorizontal: 16,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginTop: 10,
    marginBottom: 20,
  },
  backButton: {
    padding: 5,
  },
  headerTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#3A7BF8',
    textAlign: 'center',
  },
  placeholderRight: {
    width: 30,
  },
  mainTitle: {
    fontSize: 32,
    fontWeight: 'bold',
    color: '#0F172A',
    marginBottom: 8,
  },
  subtitle: {
    fontSize: 16,
    color: '#64748B',
    marginBottom: 32,
    lineHeight: 24,
  },
  section: {
    marginBottom: 32,
  },
  sectionTitle: {
    fontSize: 20,
    fontWeight: '600',
    color: '#0F172A',
    marginBottom: 8,
  },
  sectionSubtitle: {
    fontSize: 14,
    color: '#64748B',
    marginBottom: 16,
    lineHeight: 20,
  },
  inputContainer: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    backgroundColor: '#F8FAFC',
    borderRadius: 12,
    paddingHorizontal: 16,
    paddingVertical: 16,
    borderWidth: 1,
    borderColor: '#E2E8F0',
    minHeight: 80,
    width: '100%',
  },

  inputIcon: {
    marginRight: 12,
    marginTop: 4,
    alignSelf: 'flex-start',
  },

  input: {
    flex: 1,
    fontSize: 16,
    color: '#0F172A',
    minHeight: 32,
    textAlignVertical: 'center',
    paddingRight: 8,
    lineHeight: 22,
  },

  dateToggleContainer: {
    flexDirection: 'row',
    backgroundColor: '#F1F5F9',
    borderRadius: 8,
    padding: 4,
    marginBottom: 16,
  },
  toggleButton: {
    flex: 1,
    paddingVertical: 8,
    paddingHorizontal: 16,
    borderRadius: 6,
    alignItems: 'center',
  },
  toggleButtonActive: {
    backgroundColor: '#3A7BF8',
  },
  toggleButtonText: {
    fontSize: 14,
    fontWeight: '500',
    color: '#64748B',
  },
  toggleButtonTextActive: {
    color: '#FFFFFF',
  },
  datesContainer: {
    flexDirection: 'row',
    gap: 12,
  },
  dateInput: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#F8FAFC',
    borderRadius: 12,
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderWidth: 1,
    borderColor: '#E2E8F0',
  },
  selectedDateText: {
    fontSize: 16,
    color: '#0F172A',
    fontWeight: '500',
  },
  tagsContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 8,
    marginTop: 12,
  },
  tag: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#EBF2FF',
    borderRadius: 20,
    paddingHorizontal: 12,
    paddingVertical: 6,
    gap: 6,
  },
  tagText: {
    fontSize: 14,
    color: '#3A7BF8',
    fontWeight: '500',
  },
  preferenceSection: {
    marginBottom: 28,
  },
  preferenceSectionTitle: {
    fontSize: 20,
    fontWeight: '600',
    color: '#0F172A',
    marginBottom: 8,
  },
  preferenceSectionSubtitle: {
    fontSize: 14,
    color: '#64748B',
    marginBottom: 16,
    lineHeight: 20,
  },
  // Budget styles
  budgetOptionsContainer: {
    flexDirection: 'row',
    gap: 12,
  },
  budgetOption: {
    flex: 1,
    backgroundColor: '#F5F8FF',
    borderRadius: 12,
    paddingVertical: 16,
    paddingHorizontal: 8,
    borderWidth: 1,
    borderColor: '#E2E8F0',
    alignItems: 'center',
    flexDirection: 'column',
    justifyContent: 'center',
    gap: 6,
    minHeight: 70,
  },
  budgetOptionSelected: {
    backgroundColor: '#3A7BF8',
    borderColor: '#3A7BF8',
  },
  budgetOptionText: {
    fontSize: 12,
    fontWeight: '600',
    color: '#3A7BF8',
    textAlign: 'center',
  },
  budgetOptionTextSelected: {
    color: '#FFFFFF',
  },
  // Travel Group styles
  travelGroupContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 12,
  },
  travelGroupOption: {
    backgroundColor: '#F5F8FF',
    borderRadius: 12,
    paddingVertical: 14,
    paddingHorizontal: 16,
    borderWidth: 1,
    borderColor: '#E2E8F0',
    alignItems: 'center',
    flexDirection: 'row',
    gap: 8,
    minWidth: '47%',
  },
  travelGroupOptionSelected: {
    backgroundColor: '#3A7BF8',
    borderColor: '#3A7BF8',
  },
  travelGroupOptionText: {
    fontSize: 14,
    fontWeight: '600',
    color: '#3A7BF8',
  },
  travelGroupOptionTextSelected: {
    color: '#FFFFFF',
  },
  // Activity Level styles
  activityLevelContainer: {
    flexDirection: 'row',
    gap: 12,
  },
  activityLevelOption: {
    flex: 1,
    backgroundColor: '#F5F8FF',
    borderRadius: 12,
    paddingVertical: 16,
    paddingHorizontal: 8,
    borderWidth: 1,
    borderColor: '#E2E8F0',
    alignItems: 'center',
    flexDirection: 'column',
    justifyContent: 'center',
    gap: 6,
    minHeight: 70,
  },
  activityLevelOptionSelected: {
    backgroundColor: '#3A7BF8',
    borderColor: '#3A7BF8',
  },
  activityLevelOptionText: {
    fontSize: 12,
    fontWeight: '600',
    color: '#3A7BF8',
    textAlign: 'center',
  },
  activityLevelOptionTextSelected: {
    color: '#FFFFFF',
  },
  // Interests styles
  interestsContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 10,
  },
  interestTag: {
    backgroundColor: '#F5F8FF',
    borderRadius: 20,
    paddingVertical: 8,
    paddingHorizontal: 16,
    borderWidth: 1,
    borderColor: '#E2E8F0',
  },
  interestTagSelected: {
    backgroundColor: '#3A7BF8',
    borderColor: '#3A7BF8',
  },
  interestTagText: {
    fontSize: 14,
    fontWeight: '500',
    color: '#3A7BF8',
  },
  interestTagTextSelected: {
    color: '#FFFFFF',
  },
  submitContainer: {
    paddingVertical: 24,
    paddingBottom: 40,
  },
  submitButton: {
    backgroundColor: '#3A7BF8',
    borderRadius: 12,
    paddingVertical: 16,
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  submitButtonDisabled: {
    backgroundColor: '#94A3B8',
  },
  submitButtonText: {
    color: '#FFFFFF',
    fontSize: 18,
    fontWeight: '600',
  },
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  calendarModal: {
    backgroundColor: '#FFFFFF',
    borderRadius: 16,
    margin: 20,
    maxHeight: '80%',
    width: '90%',
  },
  calendarHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 20,
    borderBottomWidth: 1,
    borderBottomColor: '#E2E8F0',
  },
  calendarTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#0F172A',
  },
  // Food Preferences styles
  foodPreferencesContainer: {
    gap: 12,
  },
  foodPreferencesRow: {
    flexDirection: 'row',
    gap: 12,
  },
  foodPreferenceOption: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#F8FAFC',
    borderRadius: 12,
    paddingVertical: 14,
    paddingHorizontal: 16,
    borderWidth: 1,
    borderColor: '#E2E8F0',
    gap: 10,
  },
  foodCheckboxSelected: {
    width: 20,
    height: 20,
    borderRadius: 4,
    backgroundColor: '#3A7BF8',
    alignItems: 'center',
    justifyContent: 'center',
  },
  foodCheckboxUnselected: {
    width: 20,
    height: 20,
    borderRadius: 4,
    borderWidth: 2,
    borderColor: '#E2E8F0',
    backgroundColor: '#FFFFFF',
  },
  foodPreferenceOptionText: {
    fontSize: 14,
    color: '#0F172A',
    fontWeight: '500',
    flex: 1,
  },
});
