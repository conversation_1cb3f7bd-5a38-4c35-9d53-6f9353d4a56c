import React from 'react';
import { View, Text, StyleSheet, TouchableOpacity } from 'react-native';
import { useRouter } from 'expo-router';
import { useTheme } from 'react-native-paper';
import { SafeAreaView } from 'react-native-safe-area-context';
import { Feather, MaterialIcons } from '@expo/vector-icons';
import { StatusBar } from 'expo-status-bar';
import { useTranslation } from 'react-i18next';
import { CopilotStep, walkthroughable } from 'react-native-copilot';
import { TUTORIAL_STEPS } from '../../../components/tutorial/TutorialWrapper';

// Create walkthroughable components
const CopilotTouchableOpacity = walkthroughable(TouchableOpacity);

export default function PlanningChoiceScreen() {
  const router = useRouter();
  const { colors } = useTheme();
  const { t } = useTranslation();

  const handleYesPress = () => {
    // Navigate to specific attractions screen (blank page for now)
    router.push('/trip/specific-attractions');
  };

  const handleNoPress = () => {
    // Navigate to existing trip wizard
    router.push('/trip/wizard');
  };

  const handleBackPress = () => {
    router.back();
  };

  return (
    <SafeAreaView style={styles.container}>
      <StatusBar style="auto" />
      
      {/* Header */}
      <View style={styles.header}>
        <CopilotStep
          text="You can go back anytime by tapping here."
          order={4}
          name={TUTORIAL_STEPS.BACK_BUTTON}
        >
          <CopilotTouchableOpacity onPress={handleBackPress} style={styles.backButton}>
            <Feather name="arrow-left" size={24} color="#3A7BF8" />
          </CopilotTouchableOpacity>
        </CopilotStep>
        <Text style={styles.headerTitle}>Tripzy</Text>
        <View style={styles.placeholderRight} />
      </View>

      {/* Main Content */}
      <View style={styles.content}>
        <Text style={styles.questionText}>
          {t('tripPlanning.questionText')}
        </Text>

        <View style={styles.buttonsContainer}>
          {/* Yes Button */}
          <CopilotStep
            text="Choose this if you already know which places you want to visit."
            order={8}
            name={TUTORIAL_STEPS.YES_SPECIFIC_ATTRACTIONS}
          >
            <CopilotTouchableOpacity
              style={styles.choiceButton}
              onPress={handleYesPress}
              activeOpacity={0.8}
            >
              <View style={styles.buttonContent}>
                <MaterialIcons
                  name="location-on"
                  size={32}
                  color="white"
                  style={styles.buttonIcon}
                />
                <Text style={styles.choiceButtonTitle}>
                  {t('tripPlanning.yesOption')}
                </Text>
              </View>
            </CopilotTouchableOpacity>
          </CopilotStep>

          {/* No Button */}
          <CopilotStep
            text="Choose this if you want AI to generate a personalized itinerary for you."
            order={3}
            name={TUTORIAL_STEPS.NO_BUILD_ITINERARY}
          >
            <CopilotTouchableOpacity
              style={styles.choiceButton}
              onPress={handleNoPress}
              activeOpacity={0.8}
            >
              <View style={styles.buttonContent}>
                <MaterialIcons
                  name="explore"
                  size={32}
                  color="white"
                  style={styles.buttonIcon}
                />
                <Text style={styles.choiceButtonTitle}>
                  {t('tripPlanning.noOption')}
                </Text>
              </View>
            </CopilotTouchableOpacity>
          </CopilotStep>
        </View>
      </View>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#FFFFFF',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 20,
    marginTop: 10,
    marginBottom: 20,
  },
  backButton: {
    padding: 5,
  },
  headerTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#3A7BF8',
    textAlign: 'center',
  },
  placeholderRight: {
    width: 30,
  },
  content: {
    flex: 1,
    paddingHorizontal: 20,
    justifyContent: 'center',
  },
  questionText: {
    fontSize: 28,
    fontWeight: 'bold',
    color: '#0F172A',
    textAlign: 'center',
    marginBottom: 60,
    lineHeight: 36,
    paddingHorizontal: 10,
  },
  buttonsContainer: {
    gap: 20,
  },
  choiceButton: {
    backgroundColor: '#3A7BF8',
    borderRadius: 12,
    padding: 24,
    alignItems: 'center',
    justifyContent: 'center',
    minHeight: 80,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  choiceButtonTitle: {
    color: 'white',
    fontSize: 18,
    fontWeight: '600',
    textAlign: 'center',
    lineHeight: 24,
    flex: 1,
  },
  buttonContent: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    width: '100%',
  },
  buttonIcon: {
    marginRight: 12,
  },
});
