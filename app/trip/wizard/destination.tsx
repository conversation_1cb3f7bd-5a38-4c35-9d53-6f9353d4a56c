import React, { useState } from 'react';
import { StyleSheet, View, Text, TouchableOpacity, TextInput, ScrollView } from 'react-native';
import { useTheme } from 'react-native-paper';
import { router } from 'expo-router';
import { StatusBar } from 'expo-status-bar';
import { SafeAreaView } from 'react-native-safe-area-context';
import { Feather, MaterialIcons } from '@expo/vector-icons';

export default function DestinationScreen() {
  const { colors } = useTheme();
  
  return (
    <SafeAreaView style={styles.container}>
      <StatusBar style="dark" />
      <ScrollView style={styles.scrollView}>
        {/* Header */}
        <View style={styles.header}>
          <TouchableOpacity onPress={() => router.back()} style={styles.backButton}>
            <Feather name="arrow-left" size={24} color="#3A7BF8" />
          </TouchableOpacity>
          <Text style={styles.headerTitle}>Tripzy</Text>
          <View style={styles.placeholderRight} />
        </View>

        {/* Main Title */}
        <Text style={styles.mainTitle}>Plan a New Trip</Text>
        
        {/* Progress Indicator */}
        <View style={styles.progressContainer}>
          <Text style={styles.stepText}>Step 2 of 3</Text>
          <View style={styles.progressBar}>
            <View style={styles.progressFilled} />
            <View style={styles.progressEmpty} />
          </View>
        </View>

        {/* Coming Soon Message */}
        <View style={styles.comingSoonContainer}>
          <Text style={styles.comingSoonText}>More options coming soon!</Text>
          <TouchableOpacity 
            style={styles.backToFirstStep}
            onPress={() => router.push('/trip/wizard')}
          >
            <Text style={styles.backButtonText}>Back to Step 1</Text>
          </TouchableOpacity>
        </View>
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#FFFFFF',
  },
  scrollView: {
    flex: 1,
    paddingHorizontal: 20,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginTop: 10,
    marginBottom: 10,
  },
  backButton: {
    padding: 5,
  },
  headerTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#3A7BF8',
    textAlign: 'center',
  },
  placeholderRight: {
    width: 30,
  },
  mainTitle: {
    fontSize: 38,
    fontWeight: 'bold',
    color: '#0F172A',
    marginBottom: 20,
  },
  progressContainer: {
    marginBottom: 30,
  },
  stepText: {
    fontSize: 18,
    color: '#0F172A',
    marginBottom: 10,
  },
  progressBar: {
    flexDirection: 'row',
    height: 4,
  },
  progressFilled: {
    flex: 2,
    backgroundColor: '#3A7BF8',
    borderRadius: 2,
  },
  progressEmpty: {
    flex: 1,
    backgroundColor: '#E5E7EB',
    borderRadius: 2,
  },
  comingSoonContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    marginTop: 100,
  },
  comingSoonText: {
    fontSize: 20,
    color: '#475569',
    marginBottom: 20,
  },
  backToFirstStep: {
    backgroundColor: '#3A7BF8',
    borderRadius: 50,
    paddingVertical: 16,
    paddingHorizontal: 32,
    alignItems: 'center',
    marginTop: 20,
  },
  backButtonText: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#FFFFFF',
  },
}); 