import React, { useState, useEffect } from 'react';
import { StyleSheet, View, Text, TouchableOpacity, TextInput, ScrollView, Platform, Dimensions, Modal, TouchableWithoutFeedback, Alert } from 'react-native';
import { useTheme } from 'react-native-paper';
import { router } from 'expo-router';
import { StatusBar } from 'expo-status-bar';
import { SafeAreaView } from 'react-native-safe-area-context';
import { Feather, MaterialIcons, Ionicons } from '@expo/vector-icons';
import { useTranslation } from 'react-i18next';
// Use our custom SimpleDatePicker instead of react-native-calendar-picker
import SimpleDatePicker from '../../../components/calendar/SimpleDatePicker';
import { useAuth } from '../../../contexts/AuthContext';
// Import Firebase services
import { getFirestore, collection, addDoc, serverTimestamp } from 'firebase/firestore';

// Get screen dimensions
const { width: screenWidth } = Dimensions.get('window');

// Month options for flexible dates
const MONTHS = [
  { label: 'January', value: 'January' },
  { label: 'February', value: 'February' },
  { label: 'March', value: 'March' },
  { label: 'April', value: 'April' },
  { label: 'May', value: 'May' },
  { label: 'June', value: 'June' },
  { label: 'July', value: 'July' },
  { label: 'August', value: 'August' },
  { label: 'September', value: 'September' },
  { label: 'October', value: 'October' },
  { label: 'November', value: 'November' },
  { label: 'December', value: 'December' },
];

// Trip length options
const TRIP_LENGTHS = [
  { label: '3-5 days', value: '3-5' },
  { label: '1 week', value: '7' },
  { label: '10 days', value: '10' },
  { label: '2 weeks', value: '14' },
  { label: '3 weeks', value: '21' },
  { label: '1 month', value: '30' },
];

// Helper: Validate required fields
const validateTripData = (data: {
  destination: string;
  startDate: Date;
  endDate: Date;
}) => {
  if (!data.destination || !data.startDate || !data.endDate) {
    return false;
  }
  return true;
};

// Helper functions to get explanations for each selection
const getBudgetExplanation = (budgetOption) => {
  switch (budgetOption) {
    case 'lowBudget':
      return 'Budget: $30-70/night accommodations, public transport, free/cheap attractions, $5-15 meals, prioritizes value.';
    case 'mediumBudget':
      return 'Budget: $70-150/night 3-star hotels, mixed transport, paid attractions, $15-30 meals, balances cost/quality.';
    case 'luxuryBudget':
      return 'Budget: $150-400+/night luxury accommodations, private transport, exclusive experiences, $30-100+ fine dining.';
    default:
      return '';
  }
};

const getTravelGroupExplanation = (groupOption) => {
  switch (groupOption) {
    case 'solo':
      return 'Group: Solo traveler, prefers independent activities, flexible schedule.';
    case 'couple':
      return 'Group: Couple traveling together, seeks romantic settings and shared experiences.';
    case 'friends':
      return 'Group: Friend group, prefers social activities, group-friendly venues and shared accommodations.';
    case 'familyWithKids':
      return 'Group: Family with children, requires kid-friendly activities, safe accommodations, flexible scheduling.';
    default:
      return '';
  }
};

const getActivityLevelExplanation = (activityOption) => {
  switch (activityOption) {
    case 'relax':
      return 'Activity: Low-intensity, 1-2 activities daily, 1-2 miles walking, late starts (9-10am), ample downtime, leisurely pace.';
    case 'mediumActivity':
      return 'Activity: Moderate, 3-4 attractions daily, 3-5 miles walking, regular starts (8-9am), balanced exploration/rest.';
    case 'veryActive':
      return 'Activity: High-intensity, 5+ activities daily, 6+ miles walking, early starts (7-8am), minimal breaks, maximizes sightseeing.';
    default:
      return '';
  }
};

const getFoodPreferencesExplanation = (preferences) => {
  let explanation = 'Food preferences: ';
  if (preferences.includes('Local food')) {
    explanation += 'Interested in authentic local cuisine. ';
  }
  if (preferences.includes('Vegetarian')) {
    explanation += 'Requires vegetarian food options. ';
  }
  if (preferences.includes('Street food')) {
    explanation += 'Enjoys casual street food experiences. ';
  }
  if (preferences.includes('Fine dining')) {
    explanation += 'Appreciates upscale dining experiences. ';
  }
  return explanation.trim() || 'No specific food preferences mentioned.';
};

const getInterestsExplanation = (interests: string[] | null): string => {
  console.log('getInterestsExplanation called with:', interests);
  
  // Safety check to prevent crashes
  if (!interests || !Array.isArray(interests) || interests.length === 0) {
    console.log('No interests or invalid interests array:', interests);
    return 'No specific interests mentioned.';
  }
  
  try {
    // Updated to include all possible interests from the app
    const interestDetails: Record<string, string> = {
      'History': 'Enthusiastic about historical sites, museums, and cultural heritage.',
      'Art': 'Appreciates galleries, art museums, and creative cultural experiences.',
      'Gastronomy': 'Passionate about culinary experiences, local dishes, and food culture.',
      'Fashion': 'Interested in shopping districts, fashion houses, and style-focused attractions.',
      'Nature': 'Drawn to natural landscapes, parks, wildlife, and outdoor experiences.',
      'Sports Events': 'Eager to attend or participate in sporting activities and events.',
      'Sightseeing': 'Excited to visit famous landmarks, monuments, and tourist attractions.',
      'Nightlife': 'Interested in experiencing bars, clubs, and evening entertainment.',
      'Relaxation': 'Values spa visits, peaceful settings, and rejuvenating experiences.'
    };
    
    // Add additional details as needed
    let detailedExplanation = '';
    
    // Track the interests we've described
    const describedInterests: string[] = [];
    
    interests.forEach(interest => {
      if (interestDetails[interest]) {
        detailedExplanation += `${interest}: ${interestDetails[interest]} `;
        describedInterests.push(interest);
      } else {
        console.log(`No explanation found for interest: "${interest}"`);
      }
    });
    
    console.log('Described interests:', describedInterests);
    
    // If no descriptions were found, use a generic format
    if (detailedExplanation.trim() === '') {
      console.log('No matching explanations found, using generic format');
      return `Primary interests include: ${interests.join(', ')}.`;
    }
    
    return detailedExplanation.trim();
  } catch (error) {
    console.error('Error in getInterestsExplanation:', error);
    return `Interests include: ${Array.isArray(interests) ? interests.join(', ') : 'none specified'}.`;
  }
};

export default function TripWizardScreen() {
  // Get user from our AuthContext
  const { user } = useAuth();
  const { colors } = useTheme();
  const { t, i18n } = useTranslation();
  const [destination, setDestination] = useState('');
  const [selectedInterests, setSelectedInterests] = useState<string[]>(['Sightseeing']);
  const [foodPreferences, setFoodPreferences] = useState<string[]>(['Local food']);
  
  // New state variables for additional fields
  const [budget, setBudget] = useState<string | null>(null);
  const [travelGroup, setTravelGroup] = useState<string | null>(null);
  const [activityLevel, setActivityLevel] = useState<string | null>(null);
  
  // Special Interests state
  const [specialInterests, setSpecialInterests] = useState<{
    History: boolean;
    Art: boolean;
    Gastronomy: boolean;
    Fashion: boolean;
    Nature: boolean;
    'Sports Events': boolean;
    'Nightlife': boolean;
    'Sightseeing': boolean;
    'Relaxation': boolean;
  }>({
    History: false,
    Art: false,
    Gastronomy: false,
    Fashion: false,
    Nature: false,
    'Sports Events': false,
    'Nightlife': false,
    'Sightseeing': true, // Default to true since selectedInterests initializes with 'Sightseeing'
    'Relaxation': false,
  });
  
  // Date picker states
  const [startDate, setStartDate] = useState(new Date());
  const [endDate, setEndDate] = useState(new Date(Date.now() + 7 * 24 * 60 * 60 * 1000)); // 1 week from now
  
  // Calendar visibility states
  const [showStartCalendar, setShowStartCalendar] = useState(false);
  const [showEndCalendar, setShowEndCalendar] = useState(false);
  
  // Flexible date options
  const [useFlexibleDates, setUseFlexibleDates] = useState(false);
  const [selectedMonth, setSelectedMonth] = useState(MONTHS[new Date().getMonth()].value);
  const [selectedTripLength, setSelectedTripLength] = useState('7');
  
  // Dropdown states
  const [showMonthDropdown, setShowMonthDropdown] = useState(false);
  const [showLengthDropdown, setShowLengthDropdown] = useState(false);

  // Refs for measuring positions
  const [monthInputPosition, setMonthInputPosition] = useState({ x: 0, y: 0, width: 0, height: 0 });
  const [lengthInputPosition, setLengthInputPosition] = useState({ x: 0, y: 0, width: 0, height: 0 });
  const monthInputRef = React.useRef(null);
  const lengthInputRef = React.useRef(null);

  // Date format for display
  const formatDate = (date: Date) => {
    if (!date) return '';
    
    const options: Intl.DateTimeFormatOptions = { month: 'long', day: 'numeric', year: 'numeric' };
    return date.toLocaleDateString(undefined, options);
  };

  // Handle toggling between exact and flexible dates
  const toggleDateMode = () => {
    setUseFlexibleDates(!useFlexibleDates);
  };

  // Select a month from dropdown
  const handleSelectMonth = (month: string) => {
    setSelectedMonth(month);
    setShowMonthDropdown(false);
  };

  // Select trip length from dropdown
  const handleSelectTripLength = (length: string) => {
    setSelectedTripLength(length);
    setShowLengthDropdown(false);
  };

  const handleInterestToggle = (interest: string) => {
    // Update selectedInterests array using functional update to avoid race conditions
    setSelectedInterests(prev => {
      if (prev.includes(interest)) {
        return prev.filter(item => item !== interest);
      } else {
        return [...prev, interest];
      }
    });
    
    // Also update the specialInterests object with a functional update
    setSpecialInterests(prev => {
      // Make sure the interest exists in our structure before trying to toggle it
      if (interest in prev) {
        return {
          ...prev,
          [interest]: !prev[interest]
        };
      }
      // If it's a new interest not in our structure yet, add it
      return {
        ...prev,
        [interest]: true
      };
    });
    
    // Log after the update is scheduled (won't reflect immediate changes due to React's batching)
    console.log(`Interest '${interest}' toggle scheduled.`);
  };

  const handleFoodToggle = (preference: string) => {
    if (foodPreferences.includes(preference)) {
      setFoodPreferences(foodPreferences.filter(item => item !== preference));
    } else {
      setFoodPreferences([...foodPreferences, preference]);
    }
  };

  // Handler for budget selection
  const handleBudgetSelect = (value: string) => {
    setBudget(value);
  };

  // Handler for travel group selection
  const handleTravelGroupSelect = (value: string) => {
    setTravelGroup(value);
  };

  // Handler for activity level selection
  const handleActivityLevelSelect = (value: string) => {
    setActivityLevel(value);
  };

  const handleStartDateSelect = (date: any) => {
    try {
      const selectedDate = new Date(date);
      setStartDate(selectedDate);
      
      // If end date is before new start date, adjust end date
      if (selectedDate > endDate) {
        setEndDate(selectedDate);
      }
      
      // Hide calendar after selection
      setShowStartCalendar(false);
    } catch (error) {
      console.log('Error selecting date:', error);
      setShowStartCalendar(false);
    }
  };

  const handleEndDateSelect = (date: any) => {
    try {
      const selectedDate = new Date(date);
      
      // Ensure end date is not before start date
      if (selectedDate >= startDate) {
        setEndDate(selectedDate);
      } else {
        setEndDate(startDate);
      }
      
      // Hide calendar after selection
      setShowEndCalendar(false);
    } catch (error) {
      console.log('Error selecting date:', error);
      setShowEndCalendar(false);
    }
  };

  // Close dropdowns when clicking outside
  const closeDropdowns = () => {
    setShowMonthDropdown(false);
    setShowLengthDropdown(false);
  };

  // Measure dropdown positions
  const measureMonthInput = () => {
    if (monthInputRef.current) {
      monthInputRef.current.measure((x, y, width, height, pageX, pageY) => {
        setMonthInputPosition({ x: pageX, y: pageY, width, height });
      });
    }
  };

  const measureLengthInput = () => {
    if (lengthInputRef.current) {
      lengthInputRef.current.measure((x, y, width, height, pageX, pageY) => {
        setLengthInputPosition({ x: pageX, y: pageY, width, height });
      });
    }
  };

  // Toggle dropdowns with position measurement
  const toggleMonthDropdown = () => {
    measureMonthInput();
    setShowMonthDropdown(!showMonthDropdown);
    setShowLengthDropdown(false);
  };

  const toggleLengthDropdown = () => {
    measureLengthInput();
    setShowLengthDropdown(!showLengthDropdown);
    setShowMonthDropdown(false);
  };

  const handleSubmit = async () => {
    console.log('=== handleSubmit started ===');
    try {
      // User is already available from the component level
      console.log('Current user:', user ? user.uid : 'Not authenticated');
      
      if (!user) {
        console.error('No authenticated user. Cannot save trip plan.');
        Alert.alert('Error', 'You must be logged in to save a trip plan.');
        return;
      }

      // Validate required fields
      console.log('Validating fields - destination:', destination, 'startDate:', startDate, 'endDate:', endDate);
      if (!destination || !startDate || !endDate) {
        console.error('Missing required fields: destination, startDate, or endDate.');
        Alert.alert('Missing Information', 'Please fill in all required fields: destination, start date, and end date.');
        return;
      }

      // Make a safe copy of selectedInterests to avoid mutation issues
      const safeSelectedInterests = [...selectedInterests];
      console.log('Current selectedInterests (safe copy):', safeSelectedInterests);
      
      // Create the interests explanation safely
      let interestsDetailedDescription = '';
      try {
        interestsDetailedDescription = getInterestsExplanation(safeSelectedInterests);
        console.log('Special interests explanation built:', interestsDetailedDescription);
      } catch (error) {
        console.error('Error building interests explanation:', error);
        interestsDetailedDescription = `Interests include: ${safeSelectedInterests.join(', ')}`;
      }
      
      // Build the final trip data object with safety checks
      const tripData = {
        destination: destination || '',
        startDate: startDate ? startDate.toISOString() : new Date().toISOString(),
        endDate: endDate ? endDate.toISOString() : new Date().toISOString(),
        
        // Include both the array and explanation for interests (with safety)
        specialInterests: safeSelectedInterests,
        specialInterestsDetails: interestsDetailedDescription || 'No specific interests mentioned.',
        
        // Include the rest of the fields with null safety
        budget: budget || '',
        budgetDescription: getBudgetExplanation(budget) || '',
        travelGroup: travelGroup || '',
        travelGroupExplanation: getTravelGroupExplanation(travelGroup) || '',
        activityLevel: activityLevel || '',
        activityLevelDescription: getActivityLevelExplanation(activityLevel) || '',
        foodPreferences: [...foodPreferences], // Safe copy
        foodPreferencesExplanation: getFoodPreferencesExplanation(foodPreferences) || '',
        createdAt: new Date().toISOString(),
      };
      
      // Don't stringify the entire object as it might contain circular references
      console.log('Trip data prepared with destination:', tripData.destination);
      console.log('Trip dates:', tripData.startDate, 'to', tripData.endDate);
      
      // Use Firebase to create the trip
      try {
        console.log('Creating trip in Firebase...');
        // Get Firestore instance
        const db = getFirestore();
        
        // Add user ID to trip data and additional fields for dashboard display
        const tripDataWithUser = {
          ...tripData,
          userId: user.uid,
          userName: user.displayName || user.email,
          status: 'planning',
          createdAt: serverTimestamp(),
          updatedAt: serverTimestamp(),
          // Add these fields to make the trip appear in the dashboard
          saved: true,
          visible: true,
          // Calculate trip duration for display
          duration: Math.ceil((endDate.getTime() - startDate.getTime()) / (1000 * 60 * 60 * 24)) + 1,
          // Add current app language
          language: i18n.language || 'en'
        };
        
        // Add the trip to the tripPlans database with the specified path structure
        // Path: /tripPlans/felhasználóazonosító/plans/tripazonosító
        const plansCollection = collection(db, 'tripPlans', user.uid, 'plans');
        const docRef = await addDoc(plansCollection, tripDataWithUser);
        
        console.log('Trip plan created successfully with ID:', docRef.id);
        
        // Navigate to the new itinerary screen
        router.push(`/(main)/trip/itinerary/${docRef.id}`);
      } catch (error) {
        console.error('Error creating trip:', error);
        throw error; // Re-throw to be caught by outer catch
      }
    } catch (error) {
      console.error('Error saving trip plan:', error);
    }
  };

  return (
    <SafeAreaView style={styles.container}>
      <StatusBar style="auto" />
      
      {/* Start Date Modal */}
      <Modal
        transparent
        visible={showStartCalendar}
        animationType="fade"
        onRequestClose={() => setShowStartCalendar(false)}
      >
        <View style={styles.modalOverlay}>
          <View style={styles.calendarModal}>
            <View style={styles.calendarHeader}>
              <Text style={styles.calendarTitle}>Select Start Date</Text>
              <TouchableOpacity 
                onPress={() => setShowStartCalendar(false)}
                hitSlop={{ top: 10, right: 10, bottom: 10, left: 10 }}
              >
                <Feather name="x" size={24} color="#3A7BF8" />
              </TouchableOpacity>
            </View>
            <SimpleDatePicker
              onDateChange={handleStartDateSelect}
              selectedStartDate={startDate}
              minDate={new Date()}
            />
          </View>
        </View>
      </Modal>
      
      {/* End Date Modal */}
      <Modal
        transparent
        visible={showEndCalendar}
        animationType="fade"
        onRequestClose={() => setShowEndCalendar(false)}
      >
        <View style={styles.modalOverlay}>
          <View style={styles.calendarModal}>
            <View style={styles.calendarHeader}>
              <Text style={styles.calendarTitle}>Select End Date</Text>
              <TouchableOpacity 
                onPress={() => setShowEndCalendar(false)}
                hitSlop={{ top: 10, right: 10, bottom: 10, left: 10 }}
              >
                <Feather name="x" size={24} color="#3A7BF8" />
              </TouchableOpacity>
            </View>
            <SimpleDatePicker
              onDateChange={handleEndDateSelect}
              selectedStartDate={endDate}
              minDate={startDate}
            />
          </View>
        </View>
      </Modal>

      {/* Month Dropdown Overlay */}
      {showMonthDropdown && (
        <TouchableWithoutFeedback onPress={closeDropdowns}>
          <View style={styles.dropdownOverlay}>
            <View 
              style={[
                styles.dropdownMenu, 
                { 
                  position: 'absolute',
                  top: monthInputPosition.y + monthInputPosition.height,
                  left: monthInputPosition.x,
                  width: monthInputPosition.width,
                }
              ]}
            >
              <ScrollView style={{ maxHeight: 250 }} nestedScrollEnabled={true}>
                {MONTHS.map((month) => (
                  <TouchableOpacity
                    key={month.value}
                    style={styles.dropdownItem}
                    onPress={() => handleSelectMonth(month.value)}
                  >
                    <Text style={[
                      styles.dropdownItemText,
                      selectedMonth === month.value && styles.dropdownItemTextSelected
                    ]}>
                      {month.label}
                    </Text>
                  </TouchableOpacity>
                ))}
              </ScrollView>
            </View>
          </View>
        </TouchableWithoutFeedback>
      )}

      {/* Trip Length Dropdown Overlay */}
      {showLengthDropdown && (
        <TouchableWithoutFeedback onPress={closeDropdowns}>
          <View style={styles.dropdownOverlay}>
            <View 
              style={[
                styles.dropdownMenu, 
                { 
                  position: 'absolute',
                  top: lengthInputPosition.y + lengthInputPosition.height,
                  left: lengthInputPosition.x,
                  width: lengthInputPosition.width,
                }
              ]}
            >
              <ScrollView style={{ maxHeight: 250 }} nestedScrollEnabled={true}>
                {TRIP_LENGTHS.map((length) => (
                  <TouchableOpacity
                    key={length.value}
                    style={styles.dropdownItem}
                    onPress={() => handleSelectTripLength(length.value)}
                  >
                    <Text style={[
                      styles.dropdownItemText,
                      selectedTripLength === length.value && styles.dropdownItemTextSelected
                    ]}>
                      {length.label}
                    </Text>
                  </TouchableOpacity>
                ))}
              </ScrollView>
            </View>
          </View>
        </TouchableWithoutFeedback>
      )}

      <ScrollView style={styles.scrollView} scrollEnabled={!showMonthDropdown && !showLengthDropdown}>
        {/* Header */}
        <View style={styles.header}>
          <TouchableOpacity onPress={() => router.back()} style={styles.backButton}>
            <Feather name="arrow-left" size={24} color="#3A7BF8" />
          </TouchableOpacity>
          <Text style={styles.headerTitle}>Tripzy</Text>
          <View style={styles.placeholderRight} />
        </View>

        {/* Main Title */}
        <Text style={styles.mainTitle}>{t('tripWizard.planNewTrip')}</Text>
        
        {/* Progress Indicator */}
        <View style={styles.progressContainer}>
          <Text style={styles.stepText}>{t('tripWizard.step1of3')}</Text>
          <View style={styles.progressBar}>
            <View style={styles.progressFilled} />
            <View style={styles.progressEmpty} />
          </View>
        </View>

        {/* Destination Section */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>{t('tripWizard.destination')}</Text>
          <View style={styles.inputContainer}>
            <MaterialIcons name="place" size={24} color="#607D8B" style={styles.inputIcon} />
            <TextInput
              style={styles.input}
              placeholder={t('tripWizard.enterCityOrCountry')}
              placeholderTextColor="#607D8B"
              value={destination}
              onChangeText={setDestination}
            />
          </View>
          <Text style={styles.helperText}>{t('tripWizard.selectTravelDates')}</Text>
        </View>

        {/* Travel Dates Section */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>{t('tripWizard.travelDates')}</Text>
          
          {/* Date selection mode toggle */}
          <View style={styles.dateToggleContainer}>
            <TouchableOpacity 
              style={[
                styles.dateToggleButton, 
                !useFlexibleDates && styles.dateToggleButtonActive
              ]}
              onPress={() => setUseFlexibleDates(false)}
            >
              <Text 
                style={[
                  styles.dateToggleText,
                  !useFlexibleDates && styles.dateToggleTextActive
                ]}
              >
                {t('tripWizard.exactDates')}
              </Text>
            </TouchableOpacity>
            
            <TouchableOpacity 
              style={[
                styles.dateToggleButton, 
                useFlexibleDates && styles.dateToggleButtonActive
              ]}
              onPress={() => setUseFlexibleDates(true)}
            >
              <Text 
                style={[
                  styles.dateToggleText,
                  useFlexibleDates && styles.dateToggleTextActive
                ]}
              >
                {t('tripWizard.flexibleDates')}
              </Text>
            </TouchableOpacity>
          </View>
          
          {/* Exact dates selection */}
          {!useFlexibleDates ? (
            <View style={styles.datesContainer}>
              {/* Start Date Box */}
              <TouchableOpacity 
                style={styles.dateInput}
                onPress={() => {
                  setShowStartCalendar(true);
                }}
              >
                <Feather name="calendar" size={22} color="#607D8B" style={styles.inputIcon} />
                <Text style={styles.selectedDateText}>
                  {formatDate(startDate)}
                </Text>
              </TouchableOpacity>
              
              {/* End Date Box */}
              <TouchableOpacity 
                style={styles.dateInput}
                onPress={() => {
                  setShowEndCalendar(true);
                }}
              >
                <Feather name="calendar" size={22} color="#607D8B" style={styles.inputIcon} />
                <Text style={styles.selectedDateText}>
                  {formatDate(endDate)}
                </Text>
              </TouchableOpacity>
            </View>
          ) : (
            <View style={styles.flexibleDatesContainer}>
              {/* Month Dropdown */}
              <View style={styles.dropdownContainer}>
                <Text style={styles.dropdownLabel}>{t('tripWizard.month')}</Text>
                <TouchableOpacity 
                  ref={monthInputRef}
                  style={styles.dropdownButton}
                  onPress={toggleMonthDropdown}
                >
                  <Text style={styles.dropdownButtonText}>{selectedMonth}</Text>
                  <Feather 
                    name={showMonthDropdown ? "chevron-up" : "chevron-down"} 
                    size={18} 
                    color="#0F172A" 
                  />
                </TouchableOpacity>
              </View>
              
              {/* Trip Length Dropdown */}
              <View style={styles.dropdownContainer}>
                <Text style={styles.dropdownLabel}>{t('tripWizard.tripLength')}</Text>
                <TouchableOpacity 
                  ref={lengthInputRef}
                  style={styles.dropdownButton}
                  onPress={toggleLengthDropdown}
                >
                  <Text style={styles.dropdownButtonText}>
                    {TRIP_LENGTHS.find(item => item.value === selectedTripLength)?.label || '1 week'}
                  </Text>
                  <Feather 
                    name={showLengthDropdown ? "chevron-up" : "chevron-down"} 
                    size={18} 
                    color="#0F172A" 
                  />
                </TouchableOpacity>
              </View>
            </View>
          )}
        </View>

        {/* Special Interests Section */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>{t('tripWizard.specialInterests')}</Text>
          <Text style={styles.sectionSubtitle}>{t('tripWizard.interestsSubtitle')}</Text>
          
          <View style={styles.optionsContainer}>
            <TouchableOpacity
              style={[
                styles.optionButton,
                selectedInterests.includes('History') && styles.optionButtonSelected
              ]}
              onPress={() => handleInterestToggle('History')}
            >
              <MaterialIcons 
                name="account-balance" 
                size={24} 
                color={selectedInterests.includes('History') ? "#FFFFFF" : "#3A7BF8"}
              />
              <Text
                style={[
                  styles.optionText,
                  selectedInterests.includes('History') && styles.optionTextSelected
                ]}
              >
                {t('tripWizard.interests.history')}
              </Text>
            </TouchableOpacity>

            <TouchableOpacity
              style={[
                styles.optionButton,
                selectedInterests.includes('Art') && styles.optionButtonSelected
              ]}
              onPress={() => handleInterestToggle('Art')}
            >
              <MaterialIcons 
                name="palette" 
                size={24}
                color={selectedInterests.includes('Art') ? "#FFFFFF" : "#3A7BF8"}
              />
              <Text
                style={[
                  styles.optionText,
                  selectedInterests.includes('Art') && styles.optionTextSelected
                ]}
              >
                {t('tripWizard.interests.art')}
              </Text>
            </TouchableOpacity>

            <TouchableOpacity
              style={[
                styles.optionButton,
                selectedInterests.includes('Gastronomy') && styles.optionButtonSelected
              ]}
              onPress={() => handleInterestToggle('Gastronomy')}
            >
              <MaterialIcons 
                name="restaurant" 
                size={24}
                color={selectedInterests.includes('Gastronomy') ? "#FFFFFF" : "#3A7BF8"}
              />
              <Text
                style={[
                  styles.optionText,
                  selectedInterests.includes('Gastronomy') && styles.optionTextSelected
                ]}
              >
                {t('tripWizard.interests.gastronomy')}
              </Text>
            </TouchableOpacity>

            <TouchableOpacity
              style={[
                styles.optionButton,
                selectedInterests.includes('Fashion') && styles.optionButtonSelected
              ]}
              onPress={() => handleInterestToggle('Fashion')}
            >
              <MaterialIcons 
                name="shopping-bag" 
                size={24}
                color={selectedInterests.includes('Fashion') ? "#FFFFFF" : "#3A7BF8"}
              />
              <Text
                style={[
                  styles.optionText,
                  selectedInterests.includes('Fashion') && styles.optionTextSelected
                ]}
              >
                {t('tripWizard.interests.fashion')}
              </Text>
            </TouchableOpacity>

            <TouchableOpacity
              style={[
                styles.optionButton,
                selectedInterests.includes('Nature') && styles.optionButtonSelected
              ]}
              onPress={() => handleInterestToggle('Nature')}
            >
              <MaterialIcons 
                name="park" 
                size={24}
                color={selectedInterests.includes('Nature') ? "#FFFFFF" : "#3A7BF8"}
              />
              <Text
                style={[
                  styles.optionText,
                  selectedInterests.includes('Nature') && styles.optionTextSelected
                ]}
              >
                {t('tripWizard.interests.nature')}
              </Text>
            </TouchableOpacity>

            <TouchableOpacity
              style={[
                styles.optionButton,
                selectedInterests.includes('Sports Events') && styles.optionButtonSelected
              ]}
              onPress={() => handleInterestToggle('Sports Events')}
            >
              <MaterialIcons 
                name="sports-basketball" 
                size={24}
                color={selectedInterests.includes('Sports Events') ? "#FFFFFF" : "#3A7BF8"}
              />
              <Text
                style={[
                  styles.optionText,
                  selectedInterests.includes('Sports Events') && styles.optionTextSelected
                ]}
              >
                {t('tripWizard.interests.sportsEvents')}
              </Text>
            </TouchableOpacity>
            
            <TouchableOpacity
              style={[
                styles.optionButton,
                selectedInterests.includes('Sightseeing') && styles.optionButtonSelected
              ]}
              onPress={() => handleInterestToggle('Sightseeing')}
            >
              <MaterialIcons 
                name="attractions" 
                size={24} 
                color={selectedInterests.includes('Sightseeing') ? "#FFFFFF" : "#3A7BF8"}
              />
              <Text
                style={[
                  styles.optionText,
                  selectedInterests.includes('Sightseeing') && styles.optionTextSelected
                ]}
              >
                {t('tripWizard.interests.sightseeing')}
              </Text>
            </TouchableOpacity>

            <TouchableOpacity
              style={[
                styles.optionButton,
                selectedInterests.includes('Nightlife') && styles.optionButtonSelected
              ]}
              onPress={() => handleInterestToggle('Nightlife')}
            >
              <MaterialIcons 
                name="nightlife" 
                size={24}
                color={selectedInterests.includes('Nightlife') ? "#FFFFFF" : "#3A7BF8"}
              />
              <Text
                style={[
                  styles.optionText,
                  selectedInterests.includes('Nightlife') && styles.optionTextSelected
                ]}
              >
                Nightlife
              </Text>
            </TouchableOpacity>

            <TouchableOpacity
              style={[
                styles.optionButton,
                selectedInterests.includes('Relaxation') && styles.optionButtonSelected
              ]}
              onPress={() => handleInterestToggle('Relaxation')}
            >
              <MaterialIcons 
                name="spa" 
                size={24}
                color={selectedInterests.includes('Relaxation') ? "#FFFFFF" : "#3A7BF8"}
              />
              <Text
                style={[
                  styles.optionText,
                  selectedInterests.includes('Relaxation') && styles.optionTextSelected
                ]}
              >
                Relaxation
              </Text>
            </TouchableOpacity>
          </View>
        </View>

        {/* Budget Section */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>{t('tripWizard.budget.title')}</Text>
          
          <View style={styles.optionsContainer}>
            <TouchableOpacity
              style={[
                styles.optionButton,
                budget === 'lowBudget' && styles.optionButtonSelected
              ]}
              onPress={() => handleBudgetSelect('lowBudget')}
            >
              <MaterialIcons 
                name="attach-money" 
                size={24} 
                color={budget === 'lowBudget' ? "#FFFFFF" : "#3A7BF8"}
              />
              <Text
                style={[
                  styles.optionText,
                  budget === 'lowBudget' && styles.optionTextSelected
                ]}
              >
                {t('tripWizard.budget.lowBudget')}
              </Text>
            </TouchableOpacity>

            <TouchableOpacity
              style={[
                styles.optionButton,
                budget === 'mediumBudget' && styles.optionButtonSelected
              ]}
              onPress={() => handleBudgetSelect('mediumBudget')}
            >
              <MaterialIcons 
                name="account-balance-wallet" 
                size={24}
                color={budget === 'mediumBudget' ? "#FFFFFF" : "#3A7BF8"}
              />
              <Text
                style={[
                  styles.optionText,
                  budget === 'mediumBudget' && styles.optionTextSelected
                ]}
              >
                {t('tripWizard.budget.mediumBudget')}
              </Text>
            </TouchableOpacity>

            <TouchableOpacity
              style={[
                styles.optionButton,
                budget === 'luxuryBudget' && styles.optionButtonSelected
              ]}
              onPress={() => handleBudgetSelect('luxuryBudget')}
            >
              <MaterialIcons 
                name="stars" 
                size={24}
                color={budget === 'luxuryBudget' ? "#FFFFFF" : "#3A7BF8"}
              />
              <Text
                style={[
                  styles.optionText,
                  budget === 'luxuryBudget' && styles.optionTextSelected
                ]}
              >
                {t('tripWizard.budget.luxuryBudget')}
              </Text>
            </TouchableOpacity>
          </View>
        </View>

        {/* Travel Group Section */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>{t('tripWizard.travelGroup.title')}</Text>
          
          <View style={styles.optionsContainer}>
            <TouchableOpacity
              style={[
                styles.optionButton,
                travelGroup === 'solo' && styles.optionButtonSelected
              ]}
              onPress={() => handleTravelGroupSelect('solo')}
            >
              <MaterialIcons 
                name="person" 
                size={24} 
                color={travelGroup === 'solo' ? "#FFFFFF" : "#3A7BF8"}
              />
              <Text
                style={[
                  styles.optionText,
                  travelGroup === 'solo' && styles.optionTextSelected
                ]}
              >
                {t('tripWizard.travelGroup.solo')}
              </Text>
            </TouchableOpacity>

            <TouchableOpacity
              style={[
                styles.optionButton,
                travelGroup === 'couple' && styles.optionButtonSelected
              ]}
              onPress={() => handleTravelGroupSelect('couple')}
            >
              <MaterialIcons 
                name="favorite" 
                size={24}
                color={travelGroup === 'couple' ? "#FFFFFF" : "#3A7BF8"}
              />
              <Text
                style={[
                  styles.optionText,
                  travelGroup === 'couple' && styles.optionTextSelected
                ]}
              >
                {t('tripWizard.travelGroup.couple')}
              </Text>
            </TouchableOpacity>

            <TouchableOpacity
              style={[
                styles.optionButton,
                travelGroup === 'friends' && styles.optionButtonSelected
              ]}
              onPress={() => handleTravelGroupSelect('friends')}
            >
              <MaterialIcons 
                name="people" 
                size={24}
                color={travelGroup === 'friends' ? "#FFFFFF" : "#3A7BF8"}
              />
              <Text
                style={[
                  styles.optionText,
                  travelGroup === 'friends' && styles.optionTextSelected
                ]}
              >
                {t('tripWizard.travelGroup.friends')}
              </Text>
            </TouchableOpacity>

            <TouchableOpacity
              style={[
                styles.optionButton,
                travelGroup === 'familyWithKids' && styles.optionButtonSelected
              ]}
              onPress={() => handleTravelGroupSelect('familyWithKids')}
            >
              <MaterialIcons 
                name="family-restroom" 
                size={24}
                color={travelGroup === 'familyWithKids' ? "#FFFFFF" : "#3A7BF8"}
              />
              <Text
                style={[
                  styles.optionText,
                  travelGroup === 'familyWithKids' && styles.optionTextSelected
                ]}
              >
                {t('tripWizard.travelGroup.familyWithKids')}
              </Text>
            </TouchableOpacity>
          </View>
        </View>

        {/* Activity Level Section */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>{t('tripWizard.activityLevel.title')}</Text>
          
          <View style={styles.optionsContainer}>
            <TouchableOpacity
              style={[
                styles.optionButton,
                activityLevel === 'relax' && styles.optionButtonSelected
              ]}
              onPress={() => handleActivityLevelSelect('relax')}
            >
              <MaterialIcons 
                name="beach-access" 
                size={24} 
                color={activityLevel === 'relax' ? "#FFFFFF" : "#3A7BF8"}
              />
              <Text
                style={[
                  styles.optionText,
                  activityLevel === 'relax' && styles.optionTextSelected
                ]}
              >
                {t('tripWizard.activityLevel.relax')}
              </Text>
            </TouchableOpacity>

            <TouchableOpacity
              style={[
                styles.optionButton,
                activityLevel === 'mediumActivity' && styles.optionButtonSelected
              ]}
              onPress={() => handleActivityLevelSelect('mediumActivity')}
            >
              <MaterialIcons 
                name="directions-walk" 
                size={24}
                color={activityLevel === 'mediumActivity' ? "#FFFFFF" : "#3A7BF8"}
              />
              <Text
                style={[
                  styles.optionText,
                  activityLevel === 'mediumActivity' && styles.optionTextSelected
                ]}
              >
                {t('tripWizard.activityLevel.mediumActivity')}
              </Text>
            </TouchableOpacity>

            <TouchableOpacity
              style={[
                styles.optionButton,
                activityLevel === 'veryActive' && styles.optionButtonSelected
              ]}
              onPress={() => handleActivityLevelSelect('veryActive')}
            >
              <MaterialIcons 
                name="directions-run" 
                size={24}
                color={activityLevel === 'veryActive' ? "#FFFFFF" : "#3A7BF8"}
              />
              <Text
                style={[
                  styles.optionText,
                  activityLevel === 'veryActive' && styles.optionTextSelected
                ]}
              >
                {t('tripWizard.activityLevel.veryActive')}
              </Text>
            </TouchableOpacity>
          </View>
        </View>

        {/* Food Preferences Section */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>{t('tripWizard.foodPreferences')}</Text>
          
          <View style={styles.foodPreferencesContainer}>
            <View style={styles.foodRow}>
              <TouchableOpacity
                style={styles.foodCheckbox}
                onPress={() => handleFoodToggle('Local food')}
              >
                {foodPreferences.includes('Local food') ? (
                  <View style={styles.checkboxSelected}>
                    <Feather name="check" size={16} color="#FFFFFF" />
                  </View>
                ) : (
                  <View style={styles.checkboxUnselected} />
                )}
                <Text style={styles.foodOptionText}>{t('tripWizard.food.localFood')}</Text>
              </TouchableOpacity>

              <TouchableOpacity
                style={styles.foodCheckbox}
                onPress={() => handleFoodToggle('Vegetarian')}
              >
                {foodPreferences.includes('Vegetarian') ? (
                  <View style={styles.checkboxSelected}>
                    <Feather name="check" size={16} color="#FFFFFF" />
                  </View>
                ) : (
                  <View style={styles.checkboxUnselected} />
                )}
                <Text style={styles.foodOptionText}>{t('tripWizard.food.vegetarian')}</Text>
              </TouchableOpacity>
            </View>

            <View style={styles.foodRow}>
              <TouchableOpacity
                style={styles.foodCheckbox}
                onPress={() => handleFoodToggle('Street food')}
              >
                {foodPreferences.includes('Street food') ? (
                  <View style={styles.checkboxSelected}>
                    <Feather name="check" size={16} color="#FFFFFF" />
                  </View>
                ) : (
                  <View style={styles.checkboxUnselected} />
                )}
                <Text style={styles.foodOptionText}>{t('tripWizard.food.streetFood')}</Text>
              </TouchableOpacity>

              <TouchableOpacity
                style={styles.foodCheckbox}
                onPress={() => handleFoodToggle('Fine dining')}
              >
                {foodPreferences.includes('Fine dining') ? (
                  <View style={styles.checkboxSelected}>
                    <Feather name="check" size={16} color="#FFFFFF" />
                  </View>
                ) : (
                  <View style={styles.checkboxUnselected} />
                )}
                <Text style={styles.foodOptionText}>{t('tripWizard.food.fineDining')}</Text>
              </TouchableOpacity>
            </View>
          </View>
        </View>

        {/* Generate Button */}
        <TouchableOpacity 
          style={styles.generateButton}
          onPress={handleSubmit}
        >
          <Text style={styles.generateButtonText}>{t('tripWizard.generateItinerary')}</Text>
        </TouchableOpacity>
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#FFFFFF',
  },
  scrollView: {
    flex: 1,
    paddingHorizontal: 20,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginTop: 10,
    marginBottom: 10,
  },
  backButton: {
    padding: 5,
  },
  headerTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#3A7BF8',
    textAlign: 'center',
  },
  placeholderRight: {
    width: 30,
  },
  mainTitle: {
    fontSize: 38,
    fontWeight: 'bold',
    color: '#0F172A',
    marginBottom: 20,
  },
  progressContainer: {
    marginBottom: 30,
  },
  stepText: {
    fontSize: 18,
    color: '#0F172A',
    marginBottom: 10,
  },
  progressBar: {
    flexDirection: 'row',
    height: 4,
  },
  progressFilled: {
    flex: 1,
    backgroundColor: '#3A7BF8',
    borderRadius: 2,
  },
  progressEmpty: {
    flex: 2,
    backgroundColor: '#E5E7EB',
    borderRadius: 2,
  },
  section: {
    marginBottom: 30,
  },
  sectionTitle: {
    fontSize: 28,
    fontWeight: 'bold',
    color: '#0F172A',
    marginBottom: 16,
  },
  sectionSubtitle: {
    fontSize: 16,
    color: '#475569',
    marginBottom: 16,
  },
  inputContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    borderWidth: 1,
    borderColor: '#E2E8F0',
    borderRadius: 12,
    paddingHorizontal: 16,
    height: 60,
    marginBottom: 10,
  },
  inputIcon: {
    marginRight: 10,
  },
  input: {
    flex: 1,
    fontSize: 18,
    color: '#0F172A',
  },
  helperText: {
    fontSize: 16,
    color: '#475569',
    marginTop: 8,
  },
  datesContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    gap: 12,
  },
  dateInput: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    borderWidth: 1,
    borderColor: '#E2E8F0',
    borderRadius: 12,
    paddingHorizontal: 16,
    height: 60,
    backgroundColor: '#F8FAFC',
  },
  selectedDateText: {
    fontSize: 18,
    color: '#0F172A',
  },
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0,0,0,0.5)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  calendarModal: {
    width: screenWidth - 40,
    maxWidth: 350,
    backgroundColor: 'white',
    borderRadius: 16,
    padding: 0,
    paddingVertical: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 5,
  },
  calendarHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 12,
  },
  calendarTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#0F172A',
  },
  interestsContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 16,
  },
  interestButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: '#EFF6FF',
    borderRadius: 12,
    paddingVertical: 16,
    paddingHorizontal: 16,
    flex: 1,
    marginHorizontal: 4,
  },
  interestButtonSelected: {
    backgroundColor: '#DBEAFE',
    borderWidth: 1,
    borderColor: '#3A7BF8',
  },
  interestText: {
    fontSize: 16,
    fontWeight: '500',
    color: '#3A7BF8',
    marginLeft: 8,
  },
  interestTextSelected: {
    color: '#3A7BF8',
  },
  foodPreferencesContainer: {
    marginTop: 10,
  },
  foodRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 20,
  },
  foodCheckbox: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  checkboxUnselected: {
    width: 24,
    height: 24,
    borderWidth: 1,
    borderColor: '#94A3B8',
    borderRadius: 4,
    marginRight: 10,
  },
  checkboxSelected: {
    width: 24,
    height: 24,
    backgroundColor: '#3A7BF8',
    borderRadius: 4,
    marginRight: 10,
    alignItems: 'center',
    justifyContent: 'center',
  },
  foodOptionText: {
    fontSize: 16,
    color: '#0F172A',
  },
  generateButton: {
    backgroundColor: '#3A7BF8',
    borderRadius: 50,
    paddingVertical: 16,
    alignItems: 'center',
    marginBottom: 40,
  },
  generateButtonText: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#FFFFFF',
  },
  dateToggleContainer: {
    flexDirection: 'row',
    backgroundColor: '#F1F5F9',
    borderRadius: 8,
    padding: 4,
    marginBottom: 16,
  },
  dateToggleButton: {
    flex: 1,
    paddingVertical: 8,
    paddingHorizontal: 12,
    borderRadius: 6,
    alignItems: 'center',
  },
  dateToggleButtonActive: {
    backgroundColor: 'white',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.05,
    shadowRadius: 2,
    elevation: 1,
  },
  dateToggleText: {
    fontSize: 14,
    fontWeight: '500',
    color: '#64748B',
  },
  dateToggleTextActive: {
    color: '#0F172A',
    fontWeight: '600',
  },
  flexibleDatesContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    gap: 12,
  },
  dropdownContainer: {
    flex: 1,
    position: 'relative',
  },
  dropdownLabel: {
    fontSize: 14,
    color: '#64748B',
    marginBottom: 8,
  },
  dropdownButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    borderWidth: 1,
    borderColor: '#E2E8F0',
    borderRadius: 12,
    paddingHorizontal: 16,
    height: 60,
    backgroundColor: '#F8FAFC',
  },
  dropdownButtonText: {
    fontSize: 16,
    color: '#0F172A',
  },
  dropdownOverlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: 'transparent',
    zIndex: 1000,
  },
  dropdownMenu: {
    backgroundColor: 'white',
    borderWidth: 1,
    borderColor: '#E2E8F0',
    borderRadius: 12,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 5,
    zIndex: 1000,
  },
  dropdownItem: {
    paddingVertical: 12,
    paddingHorizontal: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#F1F5F9',
  },
  dropdownItemText: {
    fontSize: 16,
    color: '#0F172A',
  },
  dropdownItemTextSelected: {
    color: '#3A7BF8',
    fontWeight: '600',
  },
  optionsContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'flex-start',
    gap: 10,
    marginTop: 10,
    marginBottom: 16,
  },
  optionButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: '#F5F8FF',
    borderRadius: 100,
    paddingVertical: 12,
    paddingHorizontal: 16,
    marginBottom: 8,
  },
  optionButtonSelected: {
    backgroundColor: '#4A90E2',
  },
  optionText: {
    fontSize: 16,
    fontWeight: '500',
    color: '#3A7BF8',
    marginLeft: 8,
  },
  optionTextSelected: {
    color: '#FFFFFF',
  },
});

// No need to add hooks to the component as it's now a default export