import React, { useEffect } from 'react';
import { Redirect } from 'expo-router';
import { useAuth } from '../contexts/AuthContext';
import { ActivityIndicator, View, StyleSheet } from 'react-native';
import { initializeFirestore } from '../services/firebase';

function Index() {
  // This is the root index page - just redirect to login
  // The ProtectedRouteProvider in _layout.tsx will handle authentication
  console.log('Root index.tsx - Redirecting to login');
  return <Redirect href="/(auth)/login" />;
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
});

export default Index;