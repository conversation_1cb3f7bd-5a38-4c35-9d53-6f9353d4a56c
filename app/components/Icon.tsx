import React from 'react';
import { Entypo } from '@expo/vector-icons';
import { AntDesign } from '@expo/vector-icons';
import { EvilIcons } from '@expo/vector-icons';
import { Feather } from '@expo/vector-icons';
import { FontAwesome } from '@expo/vector-icons';
import { FontAwesome5 } from '@expo/vector-icons';
import { Fontisto } from '@expo/vector-icons';
import { Foundation } from '@expo/vector-icons';
import { Ionicons } from '@expo/vector-icons';
import { MaterialCommunityIcons } from '@expo/vector-icons';
import { MaterialIcons } from '@expo/vector-icons';
import { Octicons } from '@expo/vector-icons';
import { SimpleLineIcons } from '@expo/vector-icons';
import { Zocial } from '@expo/vector-icons';

// Make sure the fonts are properly loaded
import { IconFontPaths } from '../../config/icons';

// Map of icon sets
const IconMap = {
  Entypo,
  AntDesign,
  Evil<PERSON><PERSON><PERSON>,
  <PERSON>ather,
  FontAwesome,
  FontAwesome5,
  Fontisto,
  Foundation,
  Ionicons,
  MaterialCommunityIcons,
  MaterialIcons,
  Octicons,
  SimpleLineIcons,
  Zocial,
};

type IconProps = {
  name: string;
  size?: number;
  color?: string;
  family?: keyof typeof IconMap;
  style?: any;
};

/**
 * A universal icon component that supports all icon families from @expo/vector-icons
 */
export const Icon: React.FC<IconProps> = ({
  name,
  size = 24,
  color = '#000',
  family = 'Ionicons',
  style,
}) => {
  // Use the specified icon family
  const IconComponent = IconMap[family];
  
  if (!IconComponent) {
    console.warn(`Icon family "${family}" is not supported.`);
    return null;
  }

  return (
    <IconComponent name={name} size={size} color={color} style={style} />
  );
};

export default Icon; 