import React, { useEffect } from 'react';
import { Slot, Stack, useRouter, Redirect } from 'expo-router';
import { View, StyleSheet, ActivityIndicator, Text } from 'react-native';
import CustomTabBar from '../../components/ui/CustomTabBar';
import { useAuth } from '../../contexts/AuthContext';
import TutorialWrapper from '../../components/tutorial/TutorialWrapper';

function MainLayout() {
  const { user, loading } = useAuth();
  const router = useRouter();
  
  // Immediately redirect to login if not authenticated
  useEffect(() => {
    console.log('MainLayout - Auth check:', { user: user?.email || 'no user', loading });
    
    if (!loading && !user) {
      console.log('MainLayout - Not authenticated, forcing redirect to login');
      // Use immediate redirect
      router.replace('/(auth)/login');
    }
  }, [user, loading, router]);
  
  // Show loading indicator while checking auth
  if (loading) {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="large" color="#3498db" />
        <Text style={styles.loadingText}>Loading...</Text>
      </View>
    );
  }
  
  // If not authenticated, don't render anything and redirect
  if (!user) {
    console.log('MainLayout - Rendering redirect to login');
    return <Redirect href="/(auth)/login" />;
  }
  
  // Only render the main layout if authenticated
  console.log('MainLayout - User authenticated, rendering main layout');
  return (
    <TutorialWrapper>
      <View style={styles.container}>
        <Stack screenOptions={{
          headerShown: false,
          animation: 'none' // Disable animations when switching screens
        }}>
          <Slot />
        </Stack>
        <CustomTabBar />
      </View>
    </TutorialWrapper>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#FFFFFF',
  },
  loadingText: {
    marginTop: 12,
    fontSize: 16,
    color: '#555555',
  },
});

export default MainLayout;