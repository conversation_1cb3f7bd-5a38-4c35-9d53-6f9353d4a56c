import React, { useState, useEffect } from 'react';
import { View, Text, StyleSheet, FlatList, TouchableOpacity, ActivityIndicator } from 'react-native';
import { useRouter } from 'expo-router';
import { SafeAreaView } from 'react-native-safe-area-context';
import { Feather } from '@expo/vector-icons';
import { useAuth } from '../../../contexts/AuthContext';
import { getFirestore, collection, query, getDocs, orderBy, onSnapshot } from 'firebase/firestore';
import TripCard from '../../../components/ui/TripCard';

// Interface for Trip data
interface Trip {
  id: string;
  destination: string;
  startDate: string;
  endDate: string;
  status: string;
}

// Format date range for display
const formatDateRange = (startDate: string, endDate: string): string => {
  try {
    const start = new Date(startDate);
    const end = new Date(endDate);
    
    const startMonth = start.toLocaleString('default', { month: 'short' });
    const endMonth = end.toLocaleString('default', { month: 'short' });
    
    const startDay = start.getDate();
    const endDay = end.getDate();
    const startYear = start.getFullYear();
    const endYear = end.getFullYear();
    
    if (startYear !== endYear) {
      return `${startMonth} ${startDay}, ${startYear} - ${endMonth} ${endDay}, ${endYear}`;
    } else {
      return `${startMonth} ${startDay} - ${endMonth} ${endDay}, ${endYear}`;
    }
  } catch (error) {
    console.error('Error formatting date range:', error);
    return 'Date not available';
  }
};

export default function AllTripsScreen() {
  const router = useRouter();
  const { user } = useAuth();
  const [trips, setTrips] = useState<Trip[]>([]);
  const [loading, setLoading] = useState(true);

  // Fetch all trips for the user from both collections
  useEffect(() => {
    const fetchAllTrips = async () => {
      if (!user) {
        setLoading(false);
        return;
      }

      try {
        setLoading(true);
        const db = getFirestore();

        console.log('Setting up trip listeners for both collections...');

        // Set up listeners for both collections
        const unsubscribers: (() => void)[] = [];
        let regularTrips: Trip[] = [];
        let specificAttractionTrips: Trip[] = [];

        // Function to combine and update trips
        const updateCombinedTrips = () => {
          const allTrips = [...regularTrips, ...specificAttractionTrips];
          // Sort by start date (most recent first)
          allTrips.sort((a, b) => {
            const dateA = a.startDate || '';
            const dateB = b.startDate || '';
            return dateB.localeCompare(dateA);
          });

          console.log(`Combined trips: ${regularTrips.length} regular + ${specificAttractionTrips.length} specific attractions = ${allTrips.length} total`);
          setTrips(allTrips);
          setLoading(false);
        };

        // 1. Listen to regular trips collection
        const regularTripsRef = collection(db, 'tripPlans', user.uid, 'plans');
        const regularTripsQuery = query(regularTripsRef, orderBy('startDate', 'desc'));

        const unsubscribeRegular = onSnapshot(regularTripsQuery, (snapshot) => {
          regularTrips = [];
          snapshot.forEach((doc) => {
            const tripData = doc.data();
            if (tripData.status !== 'completed') {
              regularTrips.push({
                id: doc.id,
                destination: tripData.destination || 'Unknown Destination',
                startDate: tripData.startDate || '',
                endDate: tripData.endDate || '',
                status: tripData.status || 'planning'
              });
            }
          });
          console.log(`Fetched ${regularTrips.length} regular trips`);
          updateCombinedTrips();
        }, (error) => {
          console.error('Error fetching regular trips:', error);
          setLoading(false);
        });

        unsubscribers.push(unsubscribeRegular);

        // 2. Listen to specific attractions plans collection
        const specificTripsRef = collection(db, 'specificAttractionPlans', user.uid, 'plans');

        const unsubscribeSpecific = onSnapshot(specificTripsRef, (snapshot) => {
          specificAttractionTrips = [];
          snapshot.forEach((doc) => {
            const tripData = doc.data();

            // Convert travel_dates to startDate/endDate format for consistency
            let startDate = '';
            let endDate = '';

            if (tripData.travel_dates) {
              if (tripData.travel_dates.start_date && tripData.travel_dates.end_date) {
                startDate = tripData.travel_dates.start_date;
                endDate = tripData.travel_dates.end_date;
              } else if (tripData.travel_dates.number_of_days) {
                // For number of days, we'll use today as start date
                const start = new Date();
                startDate = start.toISOString().split('T')[0];
                const end = new Date(start);
                end.setDate(start.getDate() + parseInt(tripData.travel_dates.number_of_days));
                endDate = end.toISOString().split('T')[0];
              }
            }

            // Only include trips that are not completed
            if (tripData.status !== 'completed') {
              specificAttractionTrips.push({
                id: doc.id,
                destination: tripData.destination || 'Unknown Destination',
                startDate: startDate,
                endDate: endDate,
                status: tripData.status || 'planning'
              });
            }
          });
          console.log(`Fetched ${specificAttractionTrips.length} specific attraction trips`);
          updateCombinedTrips();
        }, (error) => {
          console.error('Error fetching specific attraction trips:', error);
          setLoading(false);
        });

        unsubscribers.push(unsubscribeSpecific);

        // Clean up all listeners when component unmounts
        return () => {
          unsubscribers.forEach(unsubscribe => unsubscribe());
        };
      } catch (error) {
        console.error('Error setting up trips listeners:', error);
        setLoading(false);
      }
    };

    fetchAllTrips();
  }, [user]);

  const handleViewTrip = (tripId: string) => {
    // Navigate to the trip itinerary screen
    router.push(`/trip/itinerary/${tripId}`);
  };

  const renderTripItem = ({ item }: { item: Trip }) => (
    <TripCard
      key={item.id}
      destination={item.destination}
      date={formatDateRange(item.startDate, item.endDate)}
      onPress={() => handleViewTrip(item.id)}
      compact={false}
      fullWidth={true}
    />
  );

  return (
    <SafeAreaView style={styles.container}>
      {/* Header */}
      <View style={styles.header}>
        <TouchableOpacity onPress={() => router.back()} style={styles.backButton}>
          <Feather name="arrow-left" size={24} color="#3A7BF8" />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>All Trips</Text>
        <View style={styles.placeholderRight} />
      </View>

      {loading ? (
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color="#3A7BF8" />
          <Text style={styles.loadingText}>Loading your trips...</Text>
        </View>
      ) : trips.length > 0 ? (
        <FlatList
          data={trips}
          renderItem={renderTripItem}
          keyExtractor={(item) => item.id}
          contentContainerStyle={styles.listContent}
          showsVerticalScrollIndicator={false}
        />
      ) : (
        <View style={styles.emptyStateContainer}>
          <Feather name="map" size={60} color="#CCCCCC" />
          <Text style={styles.emptyStateText}>No trips found</Text>
          <Text style={styles.emptyStateSubtext}>Start planning your first adventure!</Text>
          <TouchableOpacity 
            style={styles.planTripButton}
            onPress={() => router.push('/trip/wizard')}
          >
            <Text style={styles.planTripButtonText}>Plan a New Trip</Text>
          </TouchableOpacity>
        </View>
      )}
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#FFFFFF',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#F0F0F0',
  },
  backButton: {
    padding: 8,
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#333333',
  },
  placeholderRight: {
    width: 40,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    marginTop: 12,
    fontSize: 16,
    color: '#666666',
  },
  listContent: {
    padding: 16,
    paddingBottom: 100, // Extra padding at bottom for better scrolling
  },
  emptyStateContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  emptyStateText: {
    fontSize: 20,
    fontWeight: '600',
    color: '#333333',
    marginTop: 16,
  },
  emptyStateSubtext: {
    fontSize: 16,
    color: '#666666',
    marginTop: 8,
    marginBottom: 24,
  },
  planTripButton: {
    backgroundColor: '#3A7BF8',
    paddingVertical: 12,
    paddingHorizontal: 24,
    borderRadius: 8,
    marginTop: 16,
  },
  planTripButtonText: {
    color: '#FFFFFF',
    fontSize: 16,
    fontWeight: '600',
  },
});
