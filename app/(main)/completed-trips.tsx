import React, { useState, useEffect } from 'react';
import { StyleSheet, View, FlatList, TouchableOpacity, ActivityIndicator, Dimensions } from 'react-native';
import { Text } from 'react-native-paper';
import { SafeAreaView } from 'react-native-safe-area-context';
import { StatusBar } from 'expo-status-bar';
import { useRouter } from 'expo-router';
import { Feather, MaterialIcons } from '@expo/vector-icons';
import { useTranslation } from 'react-i18next';
import { useAuth } from '../../contexts/AuthContext';
import { db } from '../../services/firebase/firebase-v9';
import { collection, query, where, getDocs, DocumentData } from 'firebase/firestore';
import TripCard from '../../components/ui/TripCard';

interface CompletedTrip {
  id: string;
  destination: string;
  startDate: string;
  endDate: string;
  completedAt: string;
}

export default function CompletedTripsScreen() {
  const router = useRouter();
  const { user } = useAuth();
  const { t, i18n } = useTranslation();
  const [loading, setLoading] = useState(true);
  const [completedTrips, setCompletedTrips] = useState<CompletedTrip[]>([]);

  useEffect(() => {
    const fetchCompletedTrips = async () => {
      if (!user) {
        setLoading(false);
        return;
      }

      try {
        const trips: CompletedTrip[] = [];

        // Query both collections for completed trips
        // 1. Regular trips collection
        const regularTripsRef = collection(db, 'tripPlans', user.uid, 'plans');
        const regularQuery = query(regularTripsRef, where('status', '==', 'completed'));
        const regularSnapshot = await getDocs(regularQuery);

        regularSnapshot.forEach((doc) => {
          const data = doc.data() as DocumentData;
          trips.push({
            id: doc.id,
            destination: data.destination || 'Unknown Destination',
            startDate: data.startDate || '',
            endDate: data.endDate || '',
            completedAt: data.completedAt || '',
          });
        });

        // 2. Specific attractions plans collection
        const specificTripsRef = collection(db, 'specificAttractionPlans', user.uid, 'plans');
        const specificQuery = query(specificTripsRef, where('status', '==', 'completed'));
        const specificSnapshot = await getDocs(specificQuery);

        specificSnapshot.forEach((doc) => {
          const data = doc.data() as DocumentData;

          // Format dates for specific attraction trips (they use different format)
          let startDate = data.startDate || '';
          let endDate = data.endDate || '';

          // Convert date range format if needed
          if (data.travel_dates && typeof data.travel_dates === 'string') {
            const dates = data.travel_dates.split(' to ');
            if (dates.length === 2) {
              startDate = dates[0];
              endDate = dates[1];
            }
          }

          trips.push({
            id: doc.id,
            destination: data.destination || 'Unknown Destination',
            startDate: startDate,
            endDate: endDate,
            completedAt: data.completedAt || '',
          });
        });

        // Sort trips by completedAt date (most recent first)
        trips.sort((a, b) => {
          // Handle Firestore timestamps or string dates
          const getTimestamp = (timestamp: any) => {
            if (!timestamp) return 0;
            // If it's a Firestore timestamp with seconds property
            if (timestamp.seconds) {
              return timestamp.seconds * 1000;
            }
            // If it's a string date
            if (typeof timestamp === 'string') {
              return new Date(timestamp).getTime();
            }
            // If it's a Date object
            if (timestamp instanceof Date) {
              return timestamp.getTime();
            }
            return 0;
          };

          return getTimestamp(b.completedAt) - getTimestamp(a.completedAt);
        });

        console.log(`Found ${trips.length} completed trips (${regularSnapshot.size} regular + ${specificSnapshot.size} specific attractions)`);
        setCompletedTrips(trips);
      } catch (error) {
        console.error('Error fetching completed trips:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchCompletedTrips();
  }, [user]);

  const formatDateRange = (start: string, end: string) => {
    if (!start || !end) return '';
    
    const startDate = new Date(start);
    const endDate = new Date(end);
    
    const options: Intl.DateTimeFormatOptions = { month: 'short', day: 'numeric' };
    const currentLocale = i18n.language || 'en';
    return `${startDate.toLocaleDateString(currentLocale, options)} — ${endDate.toLocaleDateString(currentLocale, options)}`;
  };

  const renderTripItem = ({ item }: { item: CompletedTrip }) => {
    const dateRange = formatDateRange(item.startDate, item.endDate);
    
    return (
      <View style={styles.tripCardContainer}>
        <TripCard
          destination={item.destination}
          date={dateRange}
          onPress={() => router.push(`/(main)/trip/itinerary/${item.id}`)}
          fullWidth={true}
        />
        <View style={styles.completedBadgeOverlay}>
          <MaterialIcons name="check-circle" size={16} color="#34C759" style={styles.badgeIcon} />
          <Text style={styles.completedText}>{t('trips.completed')}</Text>
        </View>
      </View>
    );
  };

  return (
    <SafeAreaView style={styles.container}>
      <StatusBar style="auto" />
      
      <View style={styles.header}>
        <TouchableOpacity 
          style={styles.backButton} 
          onPress={() => router.back()}
        >
          <Feather name="chevron-left" size={24} color="#000" />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>{t('trips.completedTrips')}</Text>
        <View style={styles.spacer} />
      </View>
      
      {loading ? (
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color="#007AFF" />
          <Text style={styles.loadingText}>{t('trips.loadingTrips')}</Text>
        </View>
      ) : completedTrips.length > 0 ? (
        <FlatList
          data={completedTrips}
          renderItem={renderTripItem}
          keyExtractor={(item) => item.id}
          contentContainerStyle={styles.listContent}
          showsVerticalScrollIndicator={false}
        />
      ) : (
        <View style={styles.emptyContainer}>
          <MaterialIcons name="flight" size={64} color="#CCCCCC" />
          <Text style={styles.emptyTitle}>{t('trips.noCompletedTrips')}</Text>
          <Text style={styles.emptyText}>
            {t('trips.completedTripsDescription')}
          </Text>
        </View>
      )}
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#FFFFFF',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#F0F0F0',
  },
  backButton: {
    padding: 8,
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#000000',
  },
  spacer: {
    width: 40,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    marginTop: 12,
    fontSize: 16,
    color: '#555555',
  },
  listContent: {
    padding: 16,
  },
  // New container for TripCard
  tripCardContainer: {
    position: 'relative',
    marginBottom: 16,
  },
  // Keep old tripCard style for backward compatibility
  tripCard: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    backgroundColor: '#FFFFFF',
    borderRadius: 12,
    padding: 16,
    marginBottom: 12,
    shadowColor: '#000000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.05,
    shadowRadius: 4,
    elevation: 2,
  },
  tripContent: {
    flex: 1,
  },
  destinationText: {
    fontSize: 18,
    fontWeight: '600',
    color: '#000000',
    marginBottom: 4,
  },
  dateText: {
    fontSize: 14,
    color: '#666666',
    marginBottom: 8,
  },
  // Completed badge for the old design
  completedBadge: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#E6F9E9',
    paddingVertical: 4,
    paddingHorizontal: 8,
    borderRadius: 12,
    alignSelf: 'flex-start',
  },
  // New overlay badge for TripCard - positioned at the same level as the dates
  completedBadgeOverlay: {
    position: 'absolute',
    bottom: 16,
    right: 12,
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#E6F9E9',
    paddingVertical: 6,
    paddingHorizontal: 10,
    borderRadius: 12,
    zIndex: 10,
  },
  badgeIcon: {
    marginRight: 4,
  },
  completedText: {
    fontSize: 12,
    color: '#34C759',
    fontWeight: '500',
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 32,
  },
  emptyTitle: {
    fontSize: 20,
    fontWeight: '600',
    color: '#000000',
    marginTop: 16,
    marginBottom: 8,
  },
  emptyText: {
    fontSize: 16,
    color: '#666666',
    textAlign: 'center',
    lineHeight: 22,
  },
});
