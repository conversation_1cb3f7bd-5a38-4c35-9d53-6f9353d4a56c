import React, { useState, useEffect } from 'react';
import { View, Text, StyleSheet, ScrollView, TouchableOpacity, Image, Dimensions, ImageBackground, Alert, ActivityIndicator } from 'react-native';
import { useRouter } from 'expo-router';
import { useTheme } from 'react-native-paper';
import { SafeAreaView } from 'react-native-safe-area-context';
import { MaterialIcons, Feather, Ionicons } from '@expo/vector-icons';
import Colors from '../../../constants/Colors';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { useAuth } from '../../../contexts/AuthContext';
import { useTranslation } from 'react-i18next';
// Import Firebase services
import { getFirestore, collection, query, where, getDocs, orderBy, limit, onSnapshot, doc, getDoc } from 'firebase/firestore';
// Import TripCard component
import TripCard from '../../../components/ui/TripCard';
import PackingList from '../../../components/ui/PackingList';
import { CopilotStep, walkthroughable, useCopilot } from 'react-native-copilot';
import { TUTORIAL_STEPS } from '../../../components/tutorial/TutorialWrapper';
import AsyncStorage from '@react-native-async-storage/async-storage';

// Create walkthroughable components
const CopilotText = walkthroughable(Text);
const CopilotTouchableOpacity = walkthroughable(TouchableOpacity);
const CopilotView = walkthroughable(View);



// Format date range for display
const formatDateRange = (startDate: string, endDate: string): string => {
  if (!startDate || !endDate) return '';
  
  const start = new Date(startDate);
  const end = new Date(endDate);
  
  const options: Intl.DateTimeFormatOptions = { month: 'short', day: 'numeric', year: 'numeric' };
  return `${start.toLocaleDateString('en-US', options)} - ${end.toLocaleDateString('en-US', options)}`;
};



// Define trip interface
interface Trip {
  id: string;
  destination: string;
  startDate: string;
  endDate: string;
  image?: string;
  status?: string;
}

export default function DashboardScreen() {
  const { colors } = useTheme();
  const router = useRouter();
  const windowWidth = Dimensions.get('window').width;
  const insets = useSafeAreaInsets();
  const { user } = useAuth();
  const { t } = useTranslation();
  const [userProfile, setUserProfile] = useState<any>(null);
  const [loading, setLoading] = useState(true);
  const [username, setUsername] = useState<string>('Traveler');
  const [upcomingTrips, setUpcomingTrips] = useState<Trip[]>([]);
  const [tripsLoading, setTripsLoading] = useState(true);
  const { start } = useCopilot();

  // Check if tutorial should start when dashboard loads
  useEffect(() => {
    const checkTutorialFlag = async () => {
      try {
        const shouldStartTutorial = await AsyncStorage.getItem('start_tutorial_on_dashboard');
        if (shouldStartTutorial === 'true') {
          // Remove the flag
          await AsyncStorage.removeItem('start_tutorial_on_dashboard');

          // Start tutorial after a short delay to ensure components are mounted
          setTimeout(() => {
            console.log('🎯 Starting tutorial from dashboard...');
            start();
          }, 1500);
        }
      } catch (error) {
        console.error('Error checking tutorial flag:', error);
      }
    };

    checkTutorialFlag();
  }, [start]);

  useEffect(() => {
    const fetchUserData = async () => {
      if (!user) {
        setLoading(false);
        setTripsLoading(false);
        return;
      }

      try {
        // Get Firestore instance
        const db = getFirestore();
        
        // Query the users collection by email to find the user document
        const userRef = collection(db, 'users');
        const q = query(userRef, where('email', '==', user.email));
        const querySnapshot = await getDocs(q);
        
        console.log('User email:', user.email);
        console.log('Query snapshot empty?', querySnapshot.empty);
        
        // If no results from email query, try getting the document directly by UID
        if (querySnapshot.empty) {
          try {
            const userDoc = await getDoc(doc(db, 'users', user.uid));
            if (userDoc.exists()) {
              const userData = userDoc.data();
              console.log('Found user document by uid');
              console.log('User data by uid:', userData);
              
              // If the document has a username field, use it
              if (userData.username) {
                setUserProfile(userData);
                setUsername(userData.username);
                console.log('Setting username to:', userData.username);
                return; // Exit early if we found the user
              }
            }
          } catch (error) {
            console.log('Error getting user document by uid:', error);
          }
        }
        
        if (!querySnapshot.empty) {
          const userData = querySnapshot.docs[0].data();
          setUserProfile(userData);
          
          // Debug the user data to see what fields are available
          console.log('Firestore user data from email query:', userData);
          console.log('Username from Firestore:', userData.username);
          
          // Directly use the username field from Firestore
          if (userData.username) {
            setUsername(userData.username);
            console.log('Setting username to:', userData.username);
          } else {
            // Fallback options if username is not available
            setUsername(userData.displayName || user.displayName || 'Traveler');
            console.log('Username not found, using fallback:', userData.displayName || user.displayName || 'Traveler');
          }
        } else {
          // If no user profile exists yet, use basic info from auth user
          setUsername(user.displayName || 'Traveler');
          console.log('No user profile found, using auth user displayName:', user.displayName || 'Traveler');
        }
        
        console.log('Dashboard - Using Firebase user data');
      } catch (error) {
        console.error('Error fetching user data:', error);
      } finally {
        setLoading(false);
      }
    };
    
    fetchUserData();
  }, [user]);
  
  // Separate useEffect for fetching trips from both collections
  useEffect(() => {
    const fetchUserTrips = async () => {
      if (!user) {
        setTripsLoading(false);
        return;
      }

      try {
        setTripsLoading(true);
        const db = getFirestore();

        // Get today's date at the start of the day (midnight)
        const today = new Date();
        today.setHours(0, 0, 0, 0);
        const todayDateString = today.toISOString().split('T')[0];

        console.log('Setting up trip listeners for both collections...');

        // Set up listeners for both collections
        const unsubscribers: (() => void)[] = [];
        let regularTrips: Trip[] = [];
        let specificAttractionTrips: Trip[] = [];

        // Function to combine and update trips
        const updateCombinedTrips = () => {
          const allTrips = [...regularTrips, ...specificAttractionTrips];
          // Sort by start date
          allTrips.sort((a, b) => {
            const dateA = a.startDate || '';
            const dateB = b.startDate || '';
            return dateA.localeCompare(dateB);
          });

          console.log(`Combined trips: ${regularTrips.length} regular + ${specificAttractionTrips.length} specific attractions = ${allTrips.length} total`);
          setUpcomingTrips(allTrips);
          setTripsLoading(false);
        };

        // 1. Listen to regular trips collection
        const regularTripsRef = collection(db, 'tripPlans', user.uid, 'plans');
        const regularTripsQuery = query(
          regularTripsRef,
          orderBy('startDate', 'asc'),
          where('startDate', '>=', todayDateString)
        );

        const unsubscribeRegular = onSnapshot(regularTripsQuery, (snapshot) => {
          regularTrips = [];
          snapshot.forEach((doc) => {
            const tripData = doc.data();
            if (tripData.status !== 'completed') {
              regularTrips.push({
                id: doc.id,
                destination: tripData.destination || 'Unknown Destination',
                startDate: tripData.startDate || '',
                endDate: tripData.endDate || '',
                status: tripData.status || 'planning'
              });
            }
          });
          console.log(`Fetched ${regularTrips.length} regular trips`);
          updateCombinedTrips();
        }, (error) => {
          console.error('Error fetching regular trips:', error);
          setTripsLoading(false);
        });

        unsubscribers.push(unsubscribeRegular);

        // 2. Listen to specific attractions plans collection
        const specificTripsRef = collection(db, 'specificAttractionPlans', user.uid, 'plans');

        const unsubscribeSpecific = onSnapshot(specificTripsRef, (snapshot) => {
          specificAttractionTrips = [];
          snapshot.forEach((doc) => {
            const tripData = doc.data();

            // Convert travel_dates to startDate/endDate format for consistency
            let startDate = '';
            let endDate = '';

            if (tripData.travel_dates) {
              if (tripData.travel_dates.start_date && tripData.travel_dates.end_date) {
                startDate = tripData.travel_dates.start_date;
                endDate = tripData.travel_dates.end_date;
              } else if (tripData.travel_dates.number_of_days) {
                // For number of days, we'll use today as start date
                const start = new Date();
                startDate = start.toISOString().split('T')[0];
                const end = new Date(start);
                end.setDate(start.getDate() + parseInt(tripData.travel_dates.number_of_days));
                endDate = end.toISOString().split('T')[0];
              }
            }

            // Only include future trips that are not completed
            if (startDate >= todayDateString && tripData.status !== 'completed') {
              specificAttractionTrips.push({
                id: doc.id,
                destination: tripData.destination || 'Unknown Destination',
                startDate: startDate,
                endDate: endDate,
                status: tripData.status || 'planning'
              });
            }
          });
          console.log(`Fetched ${specificAttractionTrips.length} specific attraction trips`);
          updateCombinedTrips();
        }, (error) => {
          console.error('Error fetching specific attraction trips:', error);
          setTripsLoading(false);
        });

        unsubscribers.push(unsubscribeSpecific);

        // Clean up all listeners when component unmounts
        return () => {
          unsubscribers.forEach(unsubscribe => unsubscribe());
        };
      } catch (error) {
        console.error('Error setting up trips listeners:', error);
        setTripsLoading(false);
      }
    };

    fetchUserTrips();
  }, [user]);

  const handlePlanNewTrip = () => {
    router.push('/trip/planning-choice');
  };
  
  const handleViewTrip = (tripId: string) => {
    // Navigate to the trip itinerary screen
    router.push(`/trip/itinerary/${tripId}`);
  };
  


  return (
    <SafeAreaView edges={['top']} style={[styles.container, { backgroundColor: colors.background }]}>
      <ScrollView contentContainerStyle={[styles.scrollContent, { paddingBottom: 90 + insets.bottom }]}>
        <Text style={styles.appTitle}>Tripzy</Text>
        <Text style={styles.welcomeText}>{t('dashboard.welcome')} {username}</Text>
        
        <CopilotStep
          text="🌍 Ready to explore? Tap here to start planning your next adventure with AI-powered itineraries!"
          order={1}
          name={TUTORIAL_STEPS.PLAN_NEW_TRIP}
        >
          <CopilotTouchableOpacity
            style={styles.planTripButton}
            onPress={handlePlanNewTrip}
          >
            <Text style={styles.planTripButtonText}>{t('dashboard.planNewTrip')}</Text>
          </CopilotTouchableOpacity>
        </CopilotStep>

        {/* Upcoming Trips Section */}
        <View style={styles.sectionContainer}>
          <View style={styles.sectionHeader}>
            <Text style={styles.sectionTitle}>{t('dashboard.upcomingTrips')}</Text>
            <TouchableOpacity onPress={() => router.push('/trips')}>
              <Text style={styles.seeAllText}>{t('dashboard.seeAll')}</Text>
            </TouchableOpacity>
          </View>

          {tripsLoading ? (
            <View style={styles.loadingContainer}>
              <ActivityIndicator size="small" color="#3A7BF8" />
              <Text style={styles.loadingText}>{t('dashboard.loadingTrips')}</Text>
            </View>
          ) : upcomingTrips.length > 0 ? (
            <ScrollView horizontal showsHorizontalScrollIndicator={false} style={styles.horizontalScroll}>
              {upcomingTrips.map((trip) => (
                <TripCard
                  key={trip.id}
                  destination={trip.destination}
                  date={formatDateRange(trip.startDate, trip.endDate)}
                  onPress={() => handleViewTrip(trip.id)}
                  compact={false}
                />
              ))}
            </ScrollView>
          ) : (
            <View style={styles.emptyStateContainer}>
              <Ionicons name="map-outline" size={48} color="#999" />
              <Text style={styles.emptyStateText}>{t('dashboard.noUpcomingTrips')}</Text>
              <Text style={styles.emptyStateSubtext}>{t('dashboard.planFirstAdventure')}</Text>
            </View>
          )}
        </View>

        {/* Packing List */}
        <CopilotStep
          text="🎒 Never forget anything! Use this smart packing list to organize what you need for your trip."
          order={2}
          name={TUTORIAL_STEPS.PACKING_LIST_INPUT}
        >
          <CopilotView>
            <PackingList />
          </CopilotView>
        </CopilotStep>
        
        {/* Quick Actions section removed */}
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  loadingContainer: {
    padding: 20,
    alignItems: 'center',
    justifyContent: 'center',
    height: 150,
  },
  loadingText: {
    marginTop: 10,
    fontSize: 14,
    color: '#666',
  },
  scrollContent: {
    paddingHorizontal: 20,
    paddingTop: 10,
  },
  appTitle: {
    fontSize: 32,
    fontWeight: 'bold',
    color: '#3A7BF8',
    marginTop: 10,
    marginBottom: 5,
  },
  welcomeText: {
    fontSize: 32,
    fontWeight: 'bold',
    marginBottom: 24,
    color: '#000',
  },
  planTripButton: {
    backgroundColor: '#3A7BF8',
    borderRadius: 12,
    padding: 16,
    alignItems: 'center',
    marginBottom: 24,
  },
  planTripButtonText: {
    color: 'white',
    fontSize: 18,
    fontWeight: '600',
  },
  sectionContainer: {
    marginBottom: 24,
  },
  sectionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  sectionTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#333',
  },
  seeAllText: {
    fontSize: 14,
    fontWeight: '500',
    color: '#3A7BF8',
  },
  horizontalScroll: {
    flexDirection: 'row',
    marginLeft: -8,
  },
  tripCard: {
    width: 280,
    height: 170,
    marginRight: 15,
    borderRadius: 16,
    overflow: 'hidden',
    backgroundColor: 'white',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 4,
  },
  tripImage: {
    width: '100%',
    height: 120,
    resizeMode: 'cover',
  },
  tripInfo: {
    padding: 12,
  },
  tripName: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#333',
  },
  tripDate: {
    fontSize: 14,
    color: '#666',
    marginTop: 2,
  },
  emptyStateContainer: {
    padding: 30,
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: '#F8F9FA',
    borderRadius: 16,
  },
  emptyStateText: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#666',
    marginTop: 16,
  },
  emptyStateSubtext: {
    fontSize: 14,
    color: '#999',
    marginTop: 4,
  },

  quickActionsContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 24,
  },
  quickActionItem: {
    flex: 1,
    alignItems: 'center',
    padding: 16,
    backgroundColor: 'white',
    borderRadius: 12,
    marginHorizontal: 5,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.05,
    shadowRadius: 3,
    elevation: 2,
  },
  quickActionIcon: {
    width: 50,
    height: 50,
    borderRadius: 25,
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: 8,
  },
  quickActionText: {
    fontSize: 14,
    fontWeight: '500',
    color: '#333',
  },
});

