import React, { useState } from 'react';
import { StyleSheet, View, ScrollView, SafeAreaView } from 'react-native';
import { Text } from 'react-native-paper';
import { Stack } from 'expo-router';
import { StatusBar } from 'expo-status-bar';
import TravelDates from '../../../components/trip/TravelDates';

export default function TravelDatesDemo() {
  const [startDate, setStartDate] = useState<Date | null>(null);
  const [endDate, setEndDate] = useState<Date | null>(null);
  const [selectedMonth, setSelectedMonth] = useState<string | null>(null);
  const [selectedYear, setSelectedYear] = useState<number | null>(null);
  const [duration, setDuration] = useState<number | null>(null);

  const handleStartDateChange = (date: Date) => {
    setStartDate(date);
    // If end date is before the new start date, update it
    if (endDate && date > endDate) {
      setEndDate(date);
    }
  };

  const handleEndDateChange = (date: Date) => {
    setEndDate(date);
  };

  const handleMonthSelect = (month: string, year: number) => {
    setSelectedMonth(month);
    setSelectedYear(year);
    console.log(`Selected month: ${month} ${year}`);
  };

  const handleDurationSelect = (days: number) => {
    setDuration(days);
    console.log(`Selected duration: ${days} days`);
  };

  return (
    <SafeAreaView style={styles.container}>
      <StatusBar style="dark" />
      <Stack.Screen 
        options={{
          title: 'Travel Dates',
          headerShadowVisible: false,
        }}
      />
      
      <ScrollView style={styles.scrollView}>
        <View style={styles.card}>
          <TravelDates 
            startDate={startDate}
            endDate={endDate}
            onStartDateChange={handleStartDateChange}
            onEndDateChange={handleEndDateChange}
            onMonthSelect={handleMonthSelect}
            onDurationSelect={handleDurationSelect}
          />
        </View>

        <View style={styles.summaryCard}>
          <Text style={styles.summaryTitle}>Your Selection</Text>
          
          {startDate && endDate ? (
            <View style={styles.summaryItem}>
              <Text style={styles.summaryLabel}>Specific Dates:</Text>
              <Text style={styles.summaryValue}>
                {startDate.toLocaleDateString('en-US', { month: 'short', day: 'numeric', year: 'numeric' })} 
                {' to '} 
                {endDate.toLocaleDateString('en-US', { month: 'short', day: 'numeric', year: 'numeric' })}
              </Text>
            </View>
          ) : null}

          {selectedMonth && selectedYear ? (
            <View style={styles.summaryItem}>
              <Text style={styles.summaryLabel}>Travel Month:</Text>
              <Text style={styles.summaryValue}>{selectedMonth} {selectedYear}</Text>
            </View>
          ) : null}

          {duration ? (
            <View style={styles.summaryItem}>
              <Text style={styles.summaryLabel}>Trip Duration:</Text>
              <Text style={styles.summaryValue}>{duration} days</Text>
            </View>
          ) : null}

          {!startDate && !selectedMonth && (
            <Text style={styles.emptyState}>
              Select dates or a month and duration to see your selection here.
            </Text>
          )}
        </View>
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F8FAFC',
  },
  scrollView: {
    flex: 1,
  },
  card: {
    backgroundColor: '#FFFFFF',
    borderRadius: 16,
    margin: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.05,
    shadowRadius: 15,
    elevation: 2,
  },
  summaryCard: {
    backgroundColor: '#FFFFFF',
    borderRadius: 16,
    margin: 16,
    padding: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.05,
    shadowRadius: 15,
    elevation: 2,
  },
  summaryTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    marginBottom: 16,
    color: '#1A2C4E',
  },
  summaryItem: {
    marginBottom: 12,
  },
  summaryLabel: {
    fontSize: 16,
    color: '#667085',
    marginBottom: 4,
  },
  summaryValue: {
    fontSize: 18,
    fontWeight: '500',
    color: '#1A2C4E',
  },
  emptyState: {
    fontSize: 16,
    color: '#94A3B8',
    fontStyle: 'italic',
    textAlign: 'center',
    marginTop: 16,
  },
}); 