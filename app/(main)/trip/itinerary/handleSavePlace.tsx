// Function to handle saving a place to the map
export const handleSavePlace = async (
  activity,
  user,
  id,
  isPlaceSaved,
  setSavedPlaces,
  extractCoordinates,
  extractCoordinatesFromText,
  savePlaceToMap,
  setSavingPlace
) => {
  if (!user || !id) {
    console.log('User must be logged in to save places');
    return;
  }
  
  // Check if the place is already saved
  if (isPlaceSaved(activity)) {
    console.log(`${activity.title} is already saved to the map`);
    return;
  }
  
  // Try different methods to get coordinates
  let locationCoords = null;
  
  // 1. First try to extract coordinates from the location string
  const coordinates = extractCoordinates(activity.location || '');
  if (coordinates) {
    locationCoords = {
      lat: coordinates.latitude,
      lng: coordinates.longitude
    };
  }
  
  // 2. If that fails, try to extract coordinates from the description
  if (!locationCoords && activity.description) {
    const descriptionCoords = extractCoordinatesFromText(activity.description);
    if (descriptionCoords) {
      locationCoords = descriptionCoords;
    }
  }
  
  // 3. If both methods fail, log an error
  if (!locationCoords) {
    console.error('Could not determine location coordinates for this place');
    return;
  }
  
  // Create a unique identifier for the activity
  const placeId = `${activity.title}-${activity.type}`;
  
  try {
    // Show saving indicator if available
    if (setSavingPlace) {
      setSavingPlace(true);
    }
    
    // Prepare place object for Firestore
    const mapMarker = {
      title: activity.title,
      description: activity.description || '',
      type: activity.type || 'other',
      location: locationCoords,
    };
    
    console.log('Saving place to map:', mapMarker);
    
    // Save to Firestore with retry
    let saveSuccess = false;
    let retryCount = 0;
    const maxRetries = 3;
    
    while (!saveSuccess && retryCount < maxRetries) {
      try {
        saveSuccess = await savePlaceToMap(user.uid, id, mapMarker);
        if (!saveSuccess) {
          console.log(`Retry ${retryCount + 1} for saving place: ${activity.title}`);
          retryCount++;
          // Wait a bit before retrying
          await new Promise(resolve => setTimeout(resolve, 1000));
        }
      } catch (err) {
        console.error(`Error on save attempt ${retryCount + 1}:`, err);
        retryCount++;
        // Wait a bit before retrying
        await new Promise(resolve => setTimeout(resolve, 1000));
      }
    }
    
    if (saveSuccess) {
      // Update local state to reflect the saved place
      setSavedPlaces(prev => {
        const newSavedPlaces = new Set(prev);
        newSavedPlaces.add(placeId);
        return newSavedPlaces;
      });
      
      console.log(`Place saved to map: ${activity.title}`);
      return true;
    } else {
      throw new Error(`Failed to save place after ${maxRetries} attempts`);
    }
  } catch (error) {
    console.error('Error saving place to map:', error);
    return false;
  } finally {
    if (setSavingPlace) {
      setSavingPlace(false);
    }
  }
};
