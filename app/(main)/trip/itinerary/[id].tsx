import React, { useState, useEffect, useMemo, useRef, useCallback } from 'react';
import { 
  StyleSheet, 
  View, 
  Text, 
  TouchableOpacity, 
  ScrollView, 
  ActivityIndicator, 
  Linking, 
  Platform,
  Alert,
  Modal,
  Pressable,
  Dimensions,
  TextInput,
  Keyboard,
  KeyboardAvoidingView
} from 'react-native';
import { useLocalSearchParams, router } from 'expo-router';
import { SafeAreaView } from 'react-native-safe-area-context';
import { StatusBar } from 'expo-status-bar';
import { FontAwesome5, Feather, MaterialIcons, Ionicons } from '@expo/vector-icons';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { useTheme } from 'react-native-paper';
import CustomTabBar from '../../../../components/ui/CustomTabBar';

// Firebase imports
import { db } from '../../../../services/firebase/firebase-v9';
import { 
  doc, 
  getDoc, 
  deleteDoc,
  updateDoc,
  onSnapshot, 
  collection, 
  query, 
  where, 
  DocumentData,
  serverTimestamp
} from 'firebase/firestore';
import { getFunctions, httpsCallable } from 'firebase/functions';
import { useAuth } from '../../../../contexts/AuthContext';
import { savePlaceToMap, removePlaceFromMap, getMapMarkers, getAllMapMarkers, MapMarker } from '../../../../services/firebase/mapMarkers';
import debounce from 'lodash/debounce';
import { useTranslation } from 'react-i18next';

// Helper functions for coordinate extraction
const extractCoordinates = (text: string) => {
  if (!text) return null;
  
  // Look for patterns like "latitude: 40.7128, longitude: -74.0060"
  const coordPattern = /latitude:\s*(-?\d+\.\d+).*longitude:\s*(-?\d+\.\d+)/i;
  const match = text.match(coordPattern);
  
  if (match && match.length >= 3) {
    return {
      latitude: parseFloat(match[1]),
      longitude: parseFloat(match[2])
    };
  }
  
  // Look for patterns like "40.7128, -74.0060"
  const simplePattern = /(-?\d+\.\d+),\s*(-?\d+\.\d+)/;
  const simpleMatch = text.match(simplePattern);
  
  if (simpleMatch && simpleMatch.length >= 3) {
    return {
      latitude: parseFloat(simpleMatch[1]),
      longitude: parseFloat(simpleMatch[2])
    };
  }
  
  // Look for patterns like "Coordinates: [51.5194, -0.1269]" (array format)
  const arrayPattern = /\[\s*(-?\d+\.\d+)\s*,\s*(-?\d+\.\d+)\s*\]/;
  const arrayMatch = text.match(arrayPattern);
  
  if (arrayMatch && arrayMatch.length >= 3) {
    return {
      latitude: parseFloat(arrayMatch[1]),
      longitude: parseFloat(arrayMatch[2])
    };
  }
  
  return null;
};

const extractCoordinatesFromText = (text: string) => {
  if (!text) return null;
  
  // Look for patterns like "located at latitude: 40.7128, longitude: -74.0060"
  const coordPattern = /latitude:\s*(-?\d+\.\d+).*longitude:\s*(-?\d+\.\d+)/i;
  const match = text.match(coordPattern);
  
  if (match && match.length >= 3) {
    return {
      lat: parseFloat(match[1]),
      lng: parseFloat(match[2])
    };
  }
  
  // Look for patterns like "coordinates: 40.7128, -74.0060"
  const coordsPattern = /coordinates:\s*(-?\d+\.\d+),\s*(-?\d+\.\d+)/i;
  const coordsMatch = text.match(coordsPattern);
  
  if (coordsMatch && coordsMatch.length >= 3) {
    return {
      lat: parseFloat(coordsMatch[1]),
      lng: parseFloat(coordsMatch[2])
    };
  }
  
  // Look for patterns like "Coordinates: [51.5194, -0.1269]" (array format)
  const arrayPattern = /coordinates:\s*\[\s*(-?\d+\.\d+)\s*,\s*(-?\d+\.\d+)\s*\]/i;
  const arrayMatch = text.match(arrayPattern);
  
  if (arrayMatch && arrayMatch.length >= 3) {
    return {
      lat: parseFloat(arrayMatch[1]),
      lng: parseFloat(arrayMatch[2])
    };
  }
  
  return null;
};

// Helper function to determine place category from activity type
const activityTypeToPlaceCategory = (type: string) => {
  switch (type.toLowerCase()) {
    case 'food':
    case 'restaurant':
    case 'cafe':
    case 'dining':
      return 'restaurant';
    case 'hotel':
    case 'accommodation':
    case 'lodging':
      return 'hotel';
    case 'attraction':
    case 'sightseeing':
    case 'museum':
    case 'landmark':
      return 'attraction';
    case 'event':
    case 'show':
    case 'performance':
      return 'event';
    default:
      return 'other';
  }
};

// Types
interface Activity {
  title: string;
  time?: string;
  location?: string;
  description?: string;
  type: string;
  link?: string;
  coordinates?: {
    latitude: number;
    longitude: number;
  };
}

interface PlaceSuggestion {
  id: string;
  title: string;
  description: string;
  type: string;
  coordinates?: {
    latitude: number;
    longitude: number;
  };
  selected: boolean;
}

interface Day {
  day: string;
  label: string;
  items: Activity[];
}

interface TripData {
  destination: string;
  startDate: string;
  endDate: string;
  itinerary?: string;
  status?: string;
  completedAt?: any; // Firestore timestamp
  notes?: string; // Added notes field
}

export default function TripItineraryScreen() {
  const params = useLocalSearchParams();
  const { id } = params;
  const insets = useSafeAreaInsets();
  const { colors } = useTheme();
  const { user } = useAuth();
  const { i18n } = useTranslation();
  
  const [loading, setLoading] = useState(true);
  const [tripData, setTripData] = useState<TripData | null>(null);
  const [parsedItinerary, setParsedItinerary] = useState<Day[]>([]);
  const [localItinerary, setLocalItinerary] = useState<Day[]>([]);
  const [selectedDay, setSelectedDay] = useState<string>('Day 1');
  const [totalDays, setTotalDays] = useState(0);
  const [savedPlaces, setSavedPlaces] = useState<Set<string>>(new Set());
  const [markingCompleted, setMarkingCompleted] = useState(false);
  const [currentCollection, setCurrentCollection] = useState<string>('tripPlans');
  const [suggestionsModalVisible, setSuggestionsModalVisible] = useState(false);
  const [placeSuggestions, setPlaceSuggestions] = useState<PlaceSuggestion[]>([]);
  const [loadingSuggestions, setLoadingSuggestions] = useState(false);
  const [currentDayForSuggestions, setCurrentDayForSuggestions] = useState<number>(0);
  
  // Notes state
  const [notes, setNotes] = useState('');
  const [isSavingNotes, setIsSavingNotes] = useState(false);
  const [notesHeight, setNotesHeight] = useState<number>(80); // Initial height for the notes input
  const notesRef = useRef(null);
  const scrollViewRef = useRef(null);
  
  // Function to save notes to Firestore
  const saveNotesToFirestore = async (text: string) => {
    if (!user || !id) return;

    try {
      setIsSavingNotes(true);
      const tripRef = doc(db, currentCollection, user.uid, 'plans', id as string);
      await updateDoc(tripRef, {
        notes: text,
        lastUpdated: serverTimestamp()
      });
    } catch (error) {
      console.error('Error saving notes:', error);
    } finally {
      setIsSavingNotes(false);
    }
  };
  
  // Debounced save function to avoid too many Firestore writes
  const debouncedSaveNotes = useCallback(
    debounce((text: string) => {
      saveNotesToFirestore(text);
    }, 1000),
    [user, id]
  );

  // Function to handle Generate Itinerary button press
  const handleGenerateItinerary = async () => {
    if (!user || !id) return;

    try {
      // Get current language from i18n
      const currentLanguage = i18n.language || 'en';

      // Update the trip document with the current language
      const tripRef = doc(db, currentCollection, user.uid, 'plans', id as string);
      await updateDoc(tripRef, {
        language: currentLanguage,
        lastUpdated: serverTimestamp()
      });

      console.log(`Language ${currentLanguage} saved to trip plan ${id}`);

      // Here you can add additional logic for generating the itinerary
      // For now, we just save the language as requested

    } catch (error) {
      console.error('Error saving language to trip plan:', error);
      Alert.alert('Error', 'Failed to save language preference. Please try again.');
    }
  };
  
  // Function to fetch notes from Firestore
  const fetchNotes = async () => {
    if (!user || !id) return;

    try {
      // Try both collections to fetch notes
      let tripRef = doc(db, 'tripPlans', user.uid, 'plans', id as string);
      let tripSnap = await getDoc(tripRef);

      if (!tripSnap.exists()) {
        // Try specificAttractionPlans collection
        tripRef = doc(db, 'specificAttractionPlans', user.uid, 'plans', id as string);
        tripSnap = await getDoc(tripRef);
      }

      if (tripSnap.exists()) {
        const tripData = tripSnap.data();
        if (tripData.notes) {
          setNotes(tripData.notes);
        }
      }
    } catch (error) {
      console.error('Error fetching notes:', error);
    }
  };
  
  // Manual place entry modal state
  const [manualEntryModalVisible, setManualEntryModalVisible] = useState(false);
  const [manualPlace, setManualPlace] = useState({
    title: '',
    subtitle: '',
    description: '',
    latitude: '',
    longitude: '',
    link: '',
    type: 'attraction'
  });


  
  // Extract title from a sentence
  function extractTitleFromSentence(sentence: string): string {
    // Remove any leading phrases like "Start your day at", "Visit the", etc.
    let cleanSentence = sentence
      .replace(/^(start|begin|enjoy|visit|explore|head to|take a|go to|stop by|experience|discover)\s+(your|the|a|an)?\s+/i, '')
      .replace(/^(after|before|during|for)\s+(your|the|a|an)?\s+/i, '');
    
    // Look for specific landmarks or places
    const placePatterns = [
      // "at [Place Name]"
      /at\s+([^,.]+)/i,
      // "to [Place Name]"
      /to\s+([^,.]+)/i,
      // "the [Place Name]"
      /the\s+([^,.]+)/i,
      // Just take the first few words if nothing else matches
      /^([^,.]{3,40})/
    ];
    
    for (const pattern of placePatterns) {
      const match = cleanSentence.match(pattern);
      if (match && match[1]) {
        // Clean up the title
        let title = match[1].trim();
        // Remove trailing prepositions
        title = title.replace(/\s+(at|in|on|by|for|with)\s*$/, '');
        return title;
      }
    }
    
    // Fallback: just use the first part of the sentence (up to 40 chars)
    return cleanSentence.substring(0, 40) + (cleanSentence.length > 40 ? '...' : '');
  }
  
  // Extract link from text
  function extractLink(text: string): { link: string, cleanText: string } {
    let link = '';
    let cleanText = text;
    
    // Check for markdown link [text](url)
    const markdownLinkMatch = text.match(/\[([^\]]+)\]\(([^)]+)\)/);
    if (markdownLinkMatch) {
      link = markdownLinkMatch[2].trim();
      // Replace the markdown link with just the text
      cleanText = text.replace(markdownLinkMatch[0], markdownLinkMatch[1]).trim();
      return { link, cleanText };
    }
    
    // Check for link in brackets or parentheses
    const bracketLinkMatch = text.match(/[\[(]([^\])]*(?:viator\.com|link|http)[^\])]*)[\])]/);
    if (bracketLinkMatch) {
      // Check if what we found is actually a URL
      const urlInBrackets = bracketLinkMatch[1].match(/(https?:\/\/[^\s]+)/i);
      link = urlInBrackets ? urlInBrackets[1].trim() : bracketLinkMatch[1].trim();
      // Remove the bracketed content
      cleanText = text.replace(bracketLinkMatch[0], '').trim();
      return { link, cleanText };
    }
    
    // Check for plain URL
    const urlMatch = text.match(/(https?:\/\/[^\s]+)/i);
    if (urlMatch) {
      link = urlMatch[1].trim();
      // Remove the URL
      cleanText = text.replace(urlMatch[0], '').trim();
      return { link, cleanText };
    }
    
    return { link, cleanText };
  }
  
  // Function to parse itinerary text from Firestore
  function parseItineraryFromText(text: string): Day[] {
    if (!text) return [];

    console.log('Parsing itinerary text:', text.substring(0, 200) + '...');
    console.log('Full text length:', text.length);

    try {
      // Try different day header formats - now supporting multiple languages
      const dayFormats = [
        // English formats
        /Day (\d+): (\d{4}-\d{2}-\d{2})(.*?)(?=Day \d+:|Nap \d+:|Jour \d+:|Tag \d+:|Día \d+:|$)/gs,
        /Day (\d+) - (\d{4}-\d{2}-\d{2}):(.*?)(?=Day \d+ -|Nap \d+ -|Jour \d+ -|Tag \d+ -|Día \d+ -|$)/gs,
        /Day (\d+): ([^\n]*)(.*?)(?=Day \d+:|Nap \d+:|Jour \d+:|Tag \d+:|Día \d+:|$)/gs,
        /Day (\d+)(.*?)(?=Day \d+|Nap \d+|Jour \d+|Tag \d+|Día \d+|$)/gs,

        // Hungarian formats (Nap = Day)
        /Nap (\d+): (\d{4}-\d{2}-\d{2})(.*?)(?=Day \d+:|Nap \d+:|Jour \d+:|Tag \d+:|Día \d+:|$)/gs,
        /Nap (\d+) - (\d{4}-\d{2}-\d{2}):(.*?)(?=Day \d+ -|Nap \d+ -|Jour \d+ -|Tag \d+ -|Día \d+ -|$)/gs,
        /Nap (\d+): ([^\n]*)(.*?)(?=Day \d+:|Nap \d+:|Jour \d+:|Tag \d+:|Día \d+:|$)/gs,
        /Nap (\d+)(.*?)(?=Day \d+|Nap \d+|Jour \d+|Tag \d+|Día \d+|$)/gs,

        // French formats (Jour = Day)
        /Jour (\d+): (\d{4}-\d{2}-\d{2})(.*?)(?=Day \d+:|Nap \d+:|Jour \d+:|Tag \d+:|Día \d+:|$)/gs,
        /Jour (\d+) - (\d{4}-\d{2}-\d{2}):(.*?)(?=Day \d+ -|Nap \d+ -|Jour \d+ -|Tag \d+ -|Día \d+ -|$)/gs,
        /Jour (\d+): ([^\n]*)(.*?)(?=Day \d+:|Nap \d+:|Jour \d+:|Tag \d+:|Día \d+:|$)/gs,
        /Jour (\d+)(.*?)(?=Day \d+|Nap \d+|Jour \d+|Tag \d+|Día \d+|$)/gs,

        // German formats (Tag = Day)
        /Tag (\d+): (\d{4}-\d{2}-\d{2})(.*?)(?=Day \d+:|Nap \d+:|Jour \d+:|Tag \d+:|Día \d+:|$)/gs,
        /Tag (\d+) - (\d{4}-\d{2}-\d{2}):(.*?)(?=Day \d+ -|Nap \d+ -|Jour \d+ -|Tag \d+ -|Día \d+ -|$)/gs,
        /Tag (\d+): ([^\n]*)(.*?)(?=Day \d+:|Nap \d+:|Jour \d+:|Tag \d+:|Día \d+:|$)/gs,
        /Tag (\d+)(.*?)(?=Day \d+|Nap \d+|Jour \d+|Tag \d+|Día \d+|$)/gs,

        // Spanish formats (Día = Day)
        /Día (\d+): (\d{4}-\d{2}-\d{2})(.*?)(?=Day \d+:|Nap \d+:|Jour \d+:|Tag \d+:|Día \d+:|$)/gs,
        /Día (\d+) - (\d{4}-\d{2}-\d{2}):(.*?)(?=Day \d+ -|Nap \d+ -|Jour \d+ -|Tag \d+ -|Día \d+ -|$)/gs,
        /Día (\d+): ([^\n]*)(.*?)(?=Day \d+:|Nap \d+:|Jour \d+:|Tag \d+:|Día \d+:|$)/gs,
        /Día (\d+)(.*?)(?=Day \d+|Nap \d+|Jour \d+|Tag \d+|Día \d+|$)/gs
      ];
      
      const days: Day[] = [];
      
      // Try each format until we find matches
      for (const regex of dayFormats) {
        let match;
        let found = false;
        
        // Reset regex lastIndex
        regex.lastIndex = 0;
        
        while ((match = regex.exec(text + '\n'))) {
          found = true;
          const dayNumber = match[1];
          const dateOrTitle = match[2] || '';
          const content = (match[3] || match[2] || '').trim();
          
          console.log(`Found Day ${dayNumber}, content length: ${content.length} chars`);
          
          // Parse date if it matches YYYY-MM-DD format
          let date = '';
          let label = '';
          if (dateOrTitle.match(/^\d{4}-\d{2}-\d{2}$/)) {
            date = dateOrTitle;
            label = `Day ${dayNumber} (${new Date(date).toLocaleDateString('en-US', { month: 'short', day: 'numeric' })})`;
          } else {
            label = `Day ${dayNumber}${dateOrTitle ? ': ' + dateOrTitle : ''}`;
          }
          
          // Parse activities using multiple formats
          const activities: Activity[] = [];

          // First try the new structured format with "- Title:" blocks
          let activityBlocks = content.split(/\n(?=\s*[-•]\s*(?:Title|Cím|Titre|Titel|Título):)/g);

          console.log(`Found ${activityBlocks.length} activity blocks for Day ${dayNumber}`);

          // Process each activity block
          for (let block of activityBlocks) {
            block = block.trim();

            // Skip empty blocks
            if (!block || block.length === 0) continue;

            // Initialize activity object
            const activity: Activity = {
              title: '',
              description: '',
              type: 'attraction', // Default type
              time: '',
              location: '',
              link: '',
              coordinates: null
            };

            // Extract title - support multiple languages
            let titleMatch = block.match(/[-•]\s*(?:Title|Cím|Titre|Titel|Título):\s*([^\n]+)/i);
            if (titleMatch && titleMatch[1]) {
              activity.title = titleMatch[1].trim();
            } else {
              // Try the Hungarian inline format: "- Cím: Title Leírás: Description Koordináták: lat,lng"
              const hungarianMatch = block.match(/[-•]\s*Cím:\s*([^L]+?)(?:\s+Leírás:|$)/i);
              if (hungarianMatch && hungarianMatch[1]) {
                activity.title = hungarianMatch[1].trim();
              } else {
                // If no title format found, try to extract the first line as title
                const firstLine = block.split('\n')[0].replace(/^[-•]\s*/, '').trim();
                if (firstLine) {
                  activity.title = firstLine;
                } else {
                  // Skip blocks without a title
                  continue;
                }
              }
            }

            // Extract description - support multiple languages
            let descriptionMatch = block.match(/(?:Description|Leírás|Description|Beschreibung|Descripción):\s*([^\n]+(?:\n(?!Link:|Title:|Coordinates:|Cím:|Koordináták:)[^\n]+)*)/i);
            if (descriptionMatch && descriptionMatch[1]) {
              activity.description = descriptionMatch[1].trim();
            } else {
              // Try Hungarian inline format - keep coordinates in description
              const hungarianDescMatch = block.match(/Leírás:\s*(.+?)$/i);
              if (hungarianDescMatch && hungarianDescMatch[1]) {
                activity.description = hungarianDescMatch[1].trim();
              }
            }

            // Extract link - support multiple languages
            const linkMatch = block.match(/(?:Link|Lien|Enlace):\s*([^\n]+)/i);
            if (linkMatch && linkMatch[1]) {
              activity.link = linkMatch[1].trim();
            }

            // Extract coordinates - support multiple formats and languages
            let coordsMatch = block.match(/(?:Coordinates|Koordináták|Coordonnées|Koordinaten|Coordenadas):\s*(\[\s*(-?\d+\.\d+)\s*,\s*(-?\d+\.\d+)\s*\])/i);
            if (coordsMatch && coordsMatch.length >= 4) {
              activity.coordinates = {
                latitude: parseFloat(coordsMatch[2]),
                longitude: parseFloat(coordsMatch[3])
              };
            } else {
              // Try Hungarian inline format: "Koordináták: lat,lng"
              const hungarianCoordsMatch = block.match(/Koordináták:\s*(-?\d+\.\d+)\s*,\s*(-?\d+\.\d+)/i);
              if (hungarianCoordsMatch && hungarianCoordsMatch.length >= 3) {
                activity.coordinates = {
                  latitude: parseFloat(hungarianCoordsMatch[1]),
                  longitude: parseFloat(hungarianCoordsMatch[2])
                };
              }
            }
            

            
            // Extract location if present in the description
            const atMatch = activity.description.match(/at\s+([^,.]+)/i);
            if (atMatch) {
              activity.location = atMatch[1].trim();
            }
            
            // Add the activity to the list
            activities.push(activity);
          }
          
          // If no activities were found with the structured format, try Hungarian inline format
          if (activities.length === 0) {
            console.log('No structured activities found, trying Hungarian inline format');

            // Split by "- Cím:" pattern for Hungarian format
            const hungarianBlocks = content.split(/(?=\s*-\s*Cím:)/g).filter(block => block.trim().length > 0);

            console.log(`Found ${hungarianBlocks.length} Hungarian format blocks`);

            for (let block of hungarianBlocks) {
              block = block.trim();
              if (!block) continue;

              console.log('Processing Hungarian block:', block.substring(0, 100) + '...');

              // Parse Hungarian format: "- Cím: Title Leírás: Description Koordináták: lat,lng"
              const hungarianMatch = block.match(/^\s*-?\s*Cím:\s*(.+?)(?:\s+Leírás:\s*(.+?))?(?:\s+Koordináták:\s*(-?\d+\.\d+)\s*,\s*(-?\d+\.\d+))?$/i);

              if (hungarianMatch) {
                const title = hungarianMatch[1] ? hungarianMatch[1].trim() : '';
                const description = hungarianMatch[2] ? hungarianMatch[2].trim() : '';
                const lat = hungarianMatch[3] ? parseFloat(hungarianMatch[3]) : null;
                const lng = hungarianMatch[4] ? parseFloat(hungarianMatch[4]) : null;

                if (title) {
                  const activity: Activity = {
                    title,
                    description,
                    type: 'attraction',
                    time: '',
                    location: '',
                    link: '',
                    coordinates: lat && lng ? { latitude: lat, longitude: lng } : null
                  };

                  activities.push(activity);
                  console.log('Added Hungarian activity:', title);
                }
              }
            }

            // If still no activities found, fall back to the old method
            if (activities.length === 0) {
              console.log('No Hungarian activities found, falling back to sentence parsing');

              // Split content into bullet points or paragraphs
              const bulletPoints = content.split(/\n\s*[-•]\s*/).filter(p => p.trim().length > 0);

              for (let point of bulletPoints) {
                point = point.trim();
                if (!point) continue;

                // Extract any links
                const { link, cleanText } = extractLink(point);

                // Determine activity type
                const type = guessTypeFromKeywords(cleanText);

                // Extract title - use the first sentence or up to 50 chars
                const title = point.split('.')[0].trim() || point.substring(0, Math.min(50, point.length));

                activities.push({
                  type,
                  title,
                  description: cleanText,
                  location: '',
                  time: '',
                  link
                });
              }
            }
          }
          
          days.push({
            day: `Day ${dayNumber}`,
            label,
            items: activities
          });
        }
        
        // If we found matches with this regex, stop trying other formats
        if (found) {
          break;
        }
      }
      
      console.log(`Parsed ${days.length} days with a total of ${days.reduce((sum, day) => sum + day.items.length, 0)} activities`);
      console.log('First few activities:', days.length > 0 ? JSON.stringify(days[0].items.slice(0, 2), null, 2) : 'None');
      
      return days;
    } catch (error) {
      console.error('Error parsing itinerary text:', error);
      return [];
    }
  }

  useEffect(() => {
    if (!user || !id) return;

    console.log('Fetching trip data for ID:', id);
    setLoading(true);

    // Fetch notes for this trip
    fetchNotes();

    // Try to find the trip in both collections
    let unsubscribe: (() => void) | null = null;

    const tryCollection = (collectionName: string) => {
      const tripRef = doc(db, collectionName, user.uid, 'plans', id as string);

      return onSnapshot(tripRef, (docSnap) => {
        if (docSnap.exists()) {
          console.log(`Document found in ${collectionName}, getting data...`);
          const data = docSnap.data() as TripData;
          console.log('Trip data retrieved:', JSON.stringify(data, null, 2));
          setTripData(data);
          setCurrentCollection(collectionName);

          // Check if itinerary field exists and has content
          if (data.itinerary && data.itinerary.trim()) {
            console.log('Itinerary field found with content, parsing...');
            console.log('Raw itinerary content:', data.itinerary.substring(0, 300) + '...');
            // Use the new parseItineraryFromText function
            const parsed = parseItineraryFromText(data.itinerary);
            console.log(`Parsed ${parsed.length} days from itinerary`);
            console.log('Parsed data sample:', JSON.stringify(parsed.slice(0, 1), null, 2));

            if (parsed.length > 0) {
              setParsedItinerary(parsed);
              // Also update the localItinerary to ensure it stays in sync with Firestore
              setLocalItinerary(parsed);
              setTotalDays(parsed.length);
              setLoading(false);
            } else {
              // If parsing failed, keep showing loading state
              console.log('Parsing produced no results, showing loading state');
              setLoading(true);
            }
          } else {
            console.log('No itinerary content found in document');

            // Check if this is a specific attractions plan that needs processing
            if (data.trip_type === 'specific_attractions') {
              console.log('This is a specific attractions plan - checking itinerary status');

              // Check the itinerary generation status
              if (data.itinerary_status === 'generating' || data.itinerary === null) {
                console.log('Itinerary is being generated - showing loading state');
                setLoading(true);
                setParsedItinerary([]);
                setLocalItinerary([]);
                setTotalDays(0);
              } else if (data.itinerary_status === 'failed') {
                console.log('Itinerary generation failed');
                setLoading(false);
                setParsedItinerary([]);
                setLocalItinerary([]);
                setTotalDays(0);
              } else {
                console.log('Unknown itinerary status, showing loading state');
                setLoading(true);
              }
            } else {
              console.log('Regular trip plan without itinerary - showing loading state');
              // Show loading state when no itinerary is available for regular trips
              setLoading(true);
            }
          }
        } else {
          console.log(`No document found in ${collectionName}`);
          // If this is the regular tripPlans collection and no document found, try specificAttractionPlans
          if (collectionName === 'tripPlans') {
            console.log('Trying specificAttractionPlans collection...');
            unsubscribe = tryCollection('specificAttractionPlans');
          } else {
            // Document not found in either collection
            console.log('Document not found in any collection');
            setLoading(true);
          }
        }
      }, (error) => {
        console.error(`Error getting document from ${collectionName}:`, error);
        // If error with regular tripPlans, try specificAttractionPlans
        if (collectionName === 'tripPlans') {
          console.log('Error with tripPlans, trying specificAttractionPlans...');
          unsubscribe = tryCollection('specificAttractionPlans');
        } else {
          setLoading(true);
        }
      });
    };

    // Start with the regular tripPlans collection
    unsubscribe = tryCollection('tripPlans');

    return () => {
      if (unsubscribe) {
        unsubscribe();
      }
    };
  }, [user, id]);

  // Load saved places when component mounts
  useEffect(() => {
    if (user && id) {
      loadSavedPlaces();
    }
  }, [user, id]);

  // This function is no longer used, we're using parseItineraryFromText instead
  const parseItinerary = (itineraryText: string): Day[] => {
    console.log('Using new parsing function instead');
    return parseItineraryFromText(itineraryText);
  };

  const formatDateRange = (start: string, end: string, tripData?: any) => {
    // Handle specific attraction plans with different date format
    if (tripData?.trip_type === 'specific_attractions' && tripData?.travel_dates) {
      if (tripData.travel_dates.start_date && tripData.travel_dates.end_date) {
        const startDate = new Date(tripData.travel_dates.start_date);
        const endDate = new Date(tripData.travel_dates.end_date);

        const options: Intl.DateTimeFormatOptions = { month: 'short', day: 'numeric' };
        const startStr = startDate.toLocaleDateString('en-US', options);
        const endStr = endDate.toLocaleDateString('en-US', options);
        return `${startStr} — ${endStr}`;
      } else if (tripData.travel_dates.number_of_days) {
        return `${tripData.travel_dates.number_of_days} days`;
      }
    }

    // Handle regular trip plans
    if (!start || !end) return '';

    const startDate = new Date(start);
    const endDate = new Date(end);

    const options: Intl.DateTimeFormatOptions = { month: 'short', day: 'numeric' };
    const startStr = startDate.toLocaleDateString('en-US', options);
    const endStr = endDate.toLocaleDateString('en-US', options);
    return `${startStr} — ${endStr}`;
  };
  
  const calculateDays = (start: string, end: string, tripData?: any) => {
    // Handle specific attraction plans with different date format
    if (tripData?.trip_type === 'specific_attractions' && tripData?.travel_dates) {
      if (tripData.travel_dates.start_date && tripData.travel_dates.end_date) {
        const startDate = new Date(tripData.travel_dates.start_date);
        const endDate = new Date(tripData.travel_dates.end_date);
        const diffTime = Math.abs(endDate.getTime() - startDate.getTime());
        const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
        return diffDays + 1; // Include both start and end days
      } else if (tripData.travel_dates.number_of_days) {
        return parseInt(tripData.travel_dates.number_of_days);
      }
    }

    // Handle regular trip plans
    if (!start || !end) return 0;

    const startDate = new Date(start);
    const endDate = new Date(end);
    const diffTime = Math.abs(endDate.getTime() - startDate.getTime());
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    return diffDays + 1; // Include both start and end days
  };
  




  const openLink = (url: string) => {
    if (url) {
      Linking.openURL(url);
    }
  };
  
  // Initialize local itinerary from parsed itinerary
  useEffect(() => {
    if (parsedItinerary.length > 0) {
      // Only set local itinerary if it's empty to avoid overwriting manual changes
      if (localItinerary.length === 0) {
        setLocalItinerary(JSON.parse(JSON.stringify(parsedItinerary)));
      }
    }
  }, [parsedItinerary]);
  
  // Fetch notes when component mounts
  useEffect(() => {
    if (user && id) {
      fetchNotes();
    }
  }, [user, id]);
  
  // State to track if notes are focused
  const [isNotesFocused, setIsNotesFocused] = useState(false);
  
  // Handle keyboard events
  useEffect(() => {
    // When keyboard hides, unfocus notes
    const keyboardWillHideListener = Keyboard.addListener(
      Platform.OS === 'ios' ? 'keyboardWillHide' : 'keyboardDidHide',
      () => {
        setIsNotesFocused(false);
      }
    );

    return () => {
      keyboardWillHideListener.remove();
    };
  }, []);
  
  // Function to delete a place from the itinerary
  const deletePlaceFromItinerary = (dayIndex: number, activityIndex: number) => {
    Alert.alert(
      'Remove Place',
      'Are you sure you want to remove this place from your itinerary?',
      [
        { text: 'Cancel', style: 'cancel' },
        { 
          text: 'Delete', 
          style: 'destructive',
          onPress: async () => {
            try {
              // Create a deep copy of the current local itinerary
              const updatedItinerary = JSON.parse(JSON.stringify(localItinerary));
              
              // Find the day by its index in the array
              if (dayIndex >= 0 && dayIndex < updatedItinerary.length) {
                const dayToUpdate = updatedItinerary[dayIndex];
                
                if (dayToUpdate && Array.isArray(dayToUpdate.items)) {
                  // Check if the activity index is valid
                  if (activityIndex >= 0 && activityIndex < dayToUpdate.items.length) {
                    // Remove the activity at the specified index
                    dayToUpdate.items.splice(activityIndex, 1);
                    
                    // Update the state with the modified itinerary
                    setLocalItinerary(updatedItinerary);
                    
                    // Save the updated itinerary to Firebase
                    if (user && id) {
                      const tripRef = doc(db, currentCollection, user.uid, 'plans', id as string);
                      
                      // Convert the itinerary back to text format for storage
                      const itineraryText = updatedItinerary.map(day => {
                        const dayHeader = `${day.day}`;
                        
                        // Handle case where items might be empty
                        const activities = Array.isArray(day.items) && day.items.length > 0 
                          ? day.items.map(item => {
                              // Format each activity with proper structure
                              let activityText = `- Title: ${item.title || 'Activity'}`;
                              if (item.description) activityText += `\nDescription: ${item.description}`;
                              if (item.type) activityText += `\nType: ${item.type}`;
                              if (item.time) activityText += `\nTime: ${item.time}`;
                              if (item.location) activityText += `\nLocation: ${item.location}`;
                              if (item.link) activityText += `\nLink: ${item.link}`;
                              if (item.coordinates) {
                                activityText += `\nCoordinates: ${item.coordinates.latitude}, ${item.coordinates.longitude}`;
                              }
                              return activityText;
                            }).join('\n\n')
                          : '- No activities planned';
                          
                        return `${dayHeader}:\n${activities}`;
                      }).join('\n\n');
                      
                      // Update the document with the new itinerary text
                      await updateDoc(tripRef, {
                        itinerary: itineraryText,
                        lastUpdated: serverTimestamp()
                      });
                      
                      console.log('Updated itinerary saved to Firebase after deletion');
                    }
                  } else {
                    console.error('Invalid activity index:', activityIndex);
                    Alert.alert('Error', 'Could not find the activity to delete.');
                  }
                } else {
                  console.error('Day items is not an array:', dayToUpdate);
                  Alert.alert('Error', 'There was a problem with the day structure.');
                }
              } else {
                console.error('Invalid day index:', dayIndex);
                Alert.alert('Error', 'Could not find the day to update.');
              }
            } catch (error) {
              console.error('Error in deletePlaceFromItinerary:', error);
              Alert.alert('Error', 'Failed to update itinerary. Please try again.');
            }
          }
        }
      ]
    );
  };
  
  // Load saved places from Firestore when component mounts
  const loadSavedPlaces = async () => {
    try {
      if (!user || !id) return;
      
      // Get map markers for this trip
      const markers = await getMapMarkers(user.uid, id as string);
      
      // Create a set of saved place IDs
      const newSavedPlaces = new Set<string>();
      
      // Add each place to the set
      markers.forEach(marker => {
        const placeId = `${marker.title}-${marker.type}`;
        newSavedPlaces.add(placeId);
      });
      
      setSavedPlaces(newSavedPlaces);
      console.log(`Loaded ${newSavedPlaces.size} saved places from Firestore`);
    } catch (error) {
      console.error('Error loading saved places:', error);
    }
  };
  
  // State for tracking if a place is currently being saved or unsaved
  const [savingPlace, setSavingPlace] = useState(false);
  const [unsavingPlace, setUnsavingPlace] = useState(false);
  const [deleting, setDeleting] = useState(false);
  
  // Function to delete the trip
  const deleteTrip = async () => {
    if (!user || !id) {
      console.log('User must be logged in to delete a trip');
      return;
    }
    
    try {
      setDeleting(true);
      
      // Show confirmation dialog
      Alert.alert(
        'Delete Trip',
        'Are you sure you want to delete this trip? This action cannot be undone.',
        [
          {
            text: 'Cancel',
            style: 'cancel',
            onPress: () => setDeleting(false)
          },
          {
            text: 'Delete',
            style: 'destructive',
            onPress: async () => {
              try {
                // Delete the trip document from Firestore
                const tripDocRef = doc(db, currentCollection, user.uid, 'plans', id as string);
                await deleteDoc(tripDocRef);
                
                console.log('Trip deleted successfully');
                
                // Navigate back to the dashboard
                router.replace('/(main)');
              } catch (error) {
                console.error('Error deleting trip:', error);
                Alert.alert('Error', 'Failed to delete trip. Please try again.');
              } finally {
                setDeleting(false);
              }
            }
          }
        ]
      );
    } catch (error) {
      console.error('Error showing delete dialog:', error);
      setDeleting(false);
    }
  };

  // Function to mark trip as completed
  const markTripCompleted = async () => {
    if (!user || !id) {
      console.log('User must be logged in to mark a trip as completed');
      return;
    }
    
    try {
      setMarkingCompleted(true);
      
      // Show confirmation dialog
      Alert.alert(
        'Complete Trip',
        'Are you sure you want to mark this trip as completed?',
        [
          {
            text: 'Cancel',
            style: 'cancel',
            onPress: () => setMarkingCompleted(false)
          },
          {
            text: 'Yes, Complete',
            style: 'default',
            onPress: async () => {
              try {
                // Update the trip document in Firestore
                const tripDocRef = doc(db, currentCollection, user.uid, 'plans', id as string);
                await updateDoc(tripDocRef, {
                  status: 'completed',
                  completedAt: serverTimestamp()
                });
                
                console.log('Trip marked as completed successfully');
                // Success alert removed as requested
                
                // You could navigate back or stay on the page
                // router.back();
              } catch (error) {
                console.error('Error marking trip as completed:', error);
                Alert.alert('Error', 'Failed to mark trip as completed. Please try again.');
              } finally {
                setMarkingCompleted(false);
              }
            }
          }
        ]
      );
    } catch (error) {
      console.error('Error showing completion dialog:', error);
      setMarkingCompleted(false);
    }
  };
  
  // Function to reorder activities within a day
  const reorderActivity = async (dayIndex: number, activityIndex: number, direction: 'up' | 'down') => {
    // Create a deep copy of the current itinerary
    const updatedItinerary = JSON.parse(JSON.stringify(localItinerary));
    const dayActivities = updatedItinerary[dayIndex].items;
    
    // Calculate the new index based on direction
    const newIndex = direction === 'up' ? activityIndex - 1 : activityIndex + 1;
    
    // Check if the new index is valid
    if (newIndex < 0 || newIndex >= dayActivities.length) {
      return; // Cannot move beyond the boundaries
    }
    
    // Swap the activities
    const temp = dayActivities[activityIndex];
    dayActivities[activityIndex] = dayActivities[newIndex];
    dayActivities[newIndex] = temp;
    
    // Update the state with the new order
    setLocalItinerary(updatedItinerary);
    
    try {
      // Save the updated itinerary to Firebase
      if (user && id) {
        const tripRef = doc(db, currentCollection, user.uid, 'plans', id as string);
        
        // Convert the itinerary back to text format for storage
        const itineraryText = updatedItinerary.map(day => {
          const dayHeader = `Day ${day.day}:`;
          const activities = day.items.map(item => {
            let activityText = `- ${item.title}`;
            if (item.description) activityText += `\n  ${item.description}`;
            if (item.link) activityText += `\n  [${item.link}]`;
            return activityText;
          }).join('\n');
          return `${dayHeader}\n${activities}`;
        }).join('\n\n');
        
        // Update the document with the new itinerary text
        await updateDoc(tripRef, {
          itinerary: itineraryText,
          lastUpdated: serverTimestamp()
        });
        
        console.log('Reordered itinerary saved to Firebase successfully');
      }
    } catch (error) {
      console.error('Error saving reordered itinerary to Firebase:', error);
      // Don't show an alert for reordering to avoid disrupting the UX
    }
  };

  // Function to handle saving a place to the map
  const handleSavePlace = async (activity: Activity) => {
    if (!user || !id) {
      alert('You must be logged in to save places');
      return;
    }
    
    // Check if the place is already saved
    if (isPlaceSaved(activity)) {
      alert(`${activity.title} is already saved to your map!`);
      return;
    }
    
    // Try different methods to get coordinates
    let locationCoords = null;
    
    // 0. First check if the activity already has coordinates
    if (activity.coordinates) {
      locationCoords = {
        lat: activity.coordinates.latitude,
        lng: activity.coordinates.longitude
      };
    }
    
    // 1. If not, try to extract coordinates from the location string
    if (!locationCoords && activity.location) {
      const coordinates = extractCoordinates(activity.location);
      if (coordinates) {
        locationCoords = {
          lat: coordinates.latitude,
          lng: coordinates.longitude
        };
      }
    }
    
    // 2. If that fails, try to extract coordinates from the description
    if (!locationCoords && activity.description) {
      const descriptionCoords = extractCoordinatesFromText(activity.description);
      if (descriptionCoords) {
        locationCoords = descriptionCoords;
      }
    }
    
    // 3. If all methods fail, show an error
    if (!locationCoords) {
      alert('Could not determine location coordinates for this place');
      return;
    }
    
    // Create a unique identifier for the activity
    const placeId = `${activity.title}-${activity.type}`;
    
    try {
      // Show saving indicator
      setSavingPlace(true);
      
      // Prepare place object for Firestore
      const mapMarker = {
        title: activity.title,
        description: activity.description || '',
        type: activity.type || 'other',
        location: locationCoords,
      };
      
      console.log('Saving place to map:', mapMarker);
      
      // Save to Firestore with retry
      let saveSuccess = false;
      let retryCount = 0;
      const maxRetries = 3;
      
      while (!saveSuccess && retryCount < maxRetries) {
        try {
          saveSuccess = await savePlaceToMap(user.uid, id as string, mapMarker);
          if (!saveSuccess) {
            console.log(`Retry ${retryCount + 1} for saving place: ${activity.title}`);
            retryCount++;
            // Wait a bit before retrying
            await new Promise(resolve => setTimeout(resolve, 1000));
          }
        } catch (err) {
          console.error(`Error on save attempt ${retryCount + 1}:`, err);
          retryCount++;
          // Wait a bit before retrying
          await new Promise(resolve => setTimeout(resolve, 1000));
        }
      }
      
      if (saveSuccess) {
        // Update local state to reflect the saved place
        setSavedPlaces(prev => {
          const newSavedPlaces = new Set(prev);
          newSavedPlaces.add(placeId);
          return newSavedPlaces;
        });
        
        console.log(`Place saved to map: ${activity.title}`);
      } else {
        throw new Error(`Failed to save place after ${maxRetries} attempts`);
      }
    } catch (error) {
      console.error('Error saving place to map:', error);
    } finally {
      setSavingPlace(false);
    }
  };
  
  // Check if a place is already saved
  const isPlaceSaved = (activity: Activity) => {
    const placeId = `${activity.title}-${activity.type}`;
    return savedPlaces.has(placeId);
  };
  
  // Function to fetch place suggestions from Firebase
  const fetchPlaceSuggestions = async (dayIndex: number) => {
    if (!user || !id || !tripData) {
      Alert.alert('Error', 'Unable to fetch suggestions. Please try again later.');
      return;
    }

    setLoadingSuggestions(true);
    setCurrentDayForSuggestions(dayIndex);

    try {
      // Call the Firebase function
      const functions = getFunctions();
      const addPlaceSuggestionsToItinerary = httpsCallable(functions, 'addPlaceSuggestionsToItinerary');
      
      // Prepare the data to send to the function
      const result = await addPlaceSuggestionsToItinerary({
        tripId: id,
        userId: user.uid,
        destination: tripData.destination,
        dayIndex: dayIndex,
        existingActivities: localItinerary.length > 0 
          ? localItinerary[dayIndex].items.map(item => item.title)
          : parsedItinerary[dayIndex].items.map(item => item.title)
      });

      // Process the result
      const data = result.data as any;
      if (data && Array.isArray(data.suggestions)) {
        // Transform the suggestions to our format
        const suggestions: PlaceSuggestion[] = data.suggestions.map((suggestion: any) => ({
          id: suggestion.id || Math.random().toString(36).substring(2, 9),
          title: suggestion.title,
          description: suggestion.description || '',
          type: suggestion.type || guessTypeFromKeywords(suggestion.title),
          coordinates: suggestion.coordinates || null,
          selected: false
        }));

        setPlaceSuggestions(suggestions);
        setSuggestionsModalVisible(true);
      } else {
        Alert.alert('No Suggestions', 'No place suggestions could be generated. Please try again.');
      }
    } catch (error) {
      console.error('Error fetching place suggestions:', error);
      Alert.alert('Error', 'Failed to fetch place suggestions. Please try again.');
    } finally {
      setLoadingSuggestions(false);
    }
  };

  // Function to toggle a suggestion selection
  const toggleSuggestionSelection = (suggestionId: string) => {
    setPlaceSuggestions(prevSuggestions => 
      prevSuggestions.map(suggestion => 
        suggestion.id === suggestionId 
          ? { ...suggestion, selected: !suggestion.selected }
          : suggestion
      )
    );
  };

  // Function to handle adding a manually entered place
  const addManualPlaceToItinerary = async () => {
    // Validate required fields
    if (!manualPlace.title.trim()) {
      Alert.alert('Error', 'Title is required');
      return;
    }
    
    // Create a new activity object
    const newActivity: Activity = {
      title: manualPlace.title.trim(),
      description: manualPlace.description.trim(),
      type: manualPlace.type,
      time: manualPlace.subtitle.trim(), // Using subtitle as time
      location: manualPlace.subtitle.trim(),
      link: manualPlace.link.trim(),
    };
    
    // Add coordinates if provided
    if (manualPlace.latitude && manualPlace.longitude) {
      const lat = parseFloat(manualPlace.latitude);
      const lng = parseFloat(manualPlace.longitude);
      
      if (!isNaN(lat) && !isNaN(lng)) {
        newActivity.coordinates = {
          latitude: lat,
          longitude: lng
        };
        newActivity.location = `Coordinates: ${lat}, ${lng}`;
      }
    }
    
    // Get the current itinerary to work with
    // IMPORTANT: This ensures we're working with the most up-to-date state
    const currentItinerary = localItinerary.length > 0 ? [...localItinerary] : [...parsedItinerary];
    
    // Extract the day number from the selected day (e.g., 'Day 1' -> 1)
    const selectedDayNumber = parseInt(selectedDay.replace('Day ', ''));
    
    // Find the index of the day in the itinerary array
    let dayArrayIndex = -1;
    for (let i = 0; i < currentItinerary.length; i++) {
      const dayNumber = parseInt(currentItinerary[i].day.replace('Day ', ''));
      if (dayNumber === selectedDayNumber) {
        dayArrayIndex = i;
        break;
      }
    }
    
    if (dayArrayIndex !== -1) {
      // Create a deep copy to avoid direct state mutation
      const updatedItinerary = JSON.parse(JSON.stringify(currentItinerary));
      
      // Add the new activity to the current day
      updatedItinerary[dayArrayIndex].items.push(newActivity);
      
      // Update the local itinerary with the new state
      setLocalItinerary(updatedItinerary);
      
      try {
        // Save the updated itinerary to Firebase
        if (user && id) {
          const tripRef = doc(db, 'tripPlans', user.uid, 'plans', id as string);
          
          // Convert the itinerary back to text format for storage
          const itineraryText = updatedItinerary.map(day => {
            const dayHeader = `Day ${day.day}:`;
            const activities = day.items.map(item => {
              let activityText = `- ${item.title}`;
              if (item.description) activityText += `\n  ${item.description}`;
              if (item.link) activityText += `\n  [${item.link}]`;
              return activityText;
            }).join('\n');
            return `${dayHeader}\n${activities}`;
          }).join('\n\n');
          
          // Update the document with the new itinerary text
          await updateDoc(tripRef, {
            itinerary: itineraryText,
            lastUpdated: serverTimestamp()
          });
          
          console.log('Itinerary saved to Firebase successfully');
        }
      } catch (error) {
        console.error('Error saving itinerary to Firebase:', error);
        Alert.alert('Error', 'Failed to save place to itinerary. Please try again.');
      }
    }
    
    // Reset the form and close the modal
    setManualPlace({
      title: '',
      subtitle: '',
      description: '',
      latitude: '',
      longitude: '',
      link: '',
      type: 'attraction'
    });
    setManualEntryModalVisible(false);
  };
  
  // Function to add selected suggestions to the itinerary
  const addSelectedSuggestionsToItinerary = async () => {
    const selectedSuggestions = placeSuggestions.filter(suggestion => suggestion.selected);
    
    if (selectedSuggestions.length === 0) {
      setSuggestionsModalVisible(false);
      return;
    }

    // Convert suggestions to activities
    const newActivities: Activity[] = selectedSuggestions.map(suggestion => ({
      title: suggestion.title,
      description: suggestion.description,
      type: suggestion.type,
      coordinates: suggestion.coordinates,
      time: '',
      location: suggestion.coordinates 
        ? `Coordinates: ${suggestion.coordinates.latitude}, ${suggestion.coordinates.longitude}` 
        : ''
    }));

    // Create a deep copy of the current itinerary
    const updatedItinerary = JSON.parse(JSON.stringify(
      localItinerary.length > 0 ? localItinerary : parsedItinerary
    ));

    // Add the new activities to the current day
    updatedItinerary[currentDayForSuggestions].items = [
      ...updatedItinerary[currentDayForSuggestions].items,
      ...newActivities
    ];

    // Update the local itinerary
    setLocalItinerary(updatedItinerary);
    
    try {
      // Save the updated itinerary to Firebase
      if (user && id) {
        const tripRef = doc(db, 'tripPlans', user.uid, 'plans', id as string);
        
        // Convert the itinerary back to text format for storage
        const itineraryText = updatedItinerary.map(day => {
          const dayHeader = `Day ${day.day}:`;
          const activities = day.items.map(item => {
            let activityText = `- ${item.title}`;
            if (item.description) activityText += `\n  ${item.description}`;
            if (item.link) activityText += `\n  [${item.link}]`;
            return activityText;
          }).join('\n');
          return `${dayHeader}\n${activities}`;
        }).join('\n\n');
        
        // Update the document with the new itinerary text
        await updateDoc(tripRef, {
          itinerary: itineraryText,
          lastUpdated: serverTimestamp()
        });
        
        console.log('AI suggestions saved to Firebase successfully');
      }
    } catch (error) {
      console.error('Error saving AI suggestions to Firebase:', error);
      Alert.alert('Error', 'Failed to save suggestions to itinerary. Please try again.');
    }
    
    // Close the modal
    setSuggestionsModalVisible(false);
    setPlaceSuggestions([]);
  };

  // Function to handle unsaving a place from the map
  const handleUnsavePlace = async (activity: Activity) => {
    if (!user || !id) {
      console.log('User must be logged in to unsave places');
      return;
    }
    
    // Create a unique identifier for the activity
    const placeId = `${activity.title}-${activity.type}`;
    
    // Check if the place is saved before trying to unsave it
    if (!isPlaceSaved(activity)) {
      console.log(`${activity.title} is not saved to your map.`);
      return;
    }
    
    try {
      // Show unsaving indicator
      setUnsavingPlace(true);
      
      // Get all map markers to find the one with the matching title and coordinates
      const allMarkers = await getAllMapMarkers(user.uid);
      
      // Extract coordinates from the activity
      let coordinates = null;
      if (activity.location) {
        // Try to extract coordinates from the location text
        const coordMatch = activity.location.match(/(-?\d+\.\d+),\s*(-?\d+\.\d+)/);
        if (coordMatch && coordMatch.length >= 3) {
          coordinates = {
            lat: parseFloat(coordMatch[1]),
            lng: parseFloat(coordMatch[2])
          };
        }
      }
      
      // Find the marker with the matching title and coordinates if available
      let matchingMarker = null;
      
      if (coordinates) {
        // If we have coordinates, use them for more precise matching
        matchingMarker = allMarkers.find(marker => 
          marker.title.toLowerCase() === activity.title.toLowerCase() &&
          Math.abs(marker.location.lat - coordinates.lat) < 0.01 &&
          Math.abs(marker.location.lng - coordinates.lng) < 0.01
        );
      } else {
        // Fall back to title-only matching
        matchingMarker = allMarkers.find(marker => 
          marker.title.toLowerCase() === activity.title.toLowerCase()
        );
      }
      
      if (!matchingMarker) {
        console.error(`Could not find saved place with title: ${activity.title}`);
        throw new Error(`Could not find saved place with title: ${activity.title}`);
      }
      
      console.log(`Found matching marker with ID: ${matchingMarker.id} for place: ${activity.title}`);
      
      // Remove the place from Firestore using both ID and title for more reliable matching
      const success = await removePlaceFromMap(user.uid, matchingMarker.id, activity.title);
      
      if (success) {
        // Update local state to reflect the unsaved place
        setSavedPlaces(prev => {
          const newSavedPlaces = new Set(prev);
          newSavedPlaces.delete(placeId);
          return newSavedPlaces;
        });
        
        console.log(`Place unsaved from map: ${activity.title}`);
        // Don't show alert for successful unsave, just update the UI
      } else {
        throw new Error(`Failed to unsave place: ${activity.title}`);
      }
    } catch (error) {
      console.error('Error unsaving place from map:', error);
    } finally {
      setUnsavingPlace(false);
    }
  };

  // Get the current day's activities
  const currentDay = useMemo(() => {
    // Use localItinerary instead of parsedItinerary
    return localItinerary.length > 0 
      ? localItinerary.find(day => day.day === selectedDay)
      : parsedItinerary.find(day => day.day === selectedDay);
  }, [localItinerary, parsedItinerary, selectedDay]);
  
  // Calculate days count if we have trip data
  const daysCount = tripData ? calculateDays(tripData.startDate, tripData.endDate, tripData) : 0;

  // Full loading screen when we're waiting for the itinerary
  if (loading) {
    return (
      <SafeAreaView style={styles.loadingContainer}>
        <StatusBar style="auto" />
        <View style={styles.loadingContent}>
          <ActivityIndicator size="large" color="#3A7BF8" style={styles.loadingSpinner} />
          <Text style={styles.loadingText}>
            Generating Your Personalized Itinerary...
          </Text>
          <Text style={styles.loadingSubtext}>
            This can take a few moments
          </Text>
        </View>
      </SafeAreaView>
    );
  }
  
  // If we have no itinerary data, show empty state
  if (parsedItinerary.length === 0) {
    return (
      <SafeAreaView style={styles.loadingContainer}>
        <StatusBar style="auto" />
        <Text style={styles.loadingText}>No itinerary available yet</Text>
        <Text style={styles.loadingSubtext}>We're still working on your personalized plan</Text>
      </SafeAreaView>
    );
  }
  
  return (
    <View style={styles.container}>
      <StatusBar style="auto" />
      
      {/* Header with back button, title, and delete button */}
      <SafeAreaView edges={['top']} style={{ backgroundColor: 'white' }}>
        <View style={styles.header}>
          <TouchableOpacity 
            style={styles.backButton} 
            onPress={() => router.replace('/(main)')}
          >
            <Feather name="chevron-left" size={24} color="#000" />
          </TouchableOpacity>
          
          {/* Centered destination name */}
          <Text style={styles.headerTitle}>{tripData?.destination || 'Budapest'}</Text>
          
          {/* Trash icon button */}
          <TouchableOpacity 
            style={styles.deleteButton}
            onPress={deleteTrip}
            disabled={deleting}
          >
            <Feather name="trash-2" size={22} color="#FF3B30" />
          </TouchableOpacity>
        </View>
      </SafeAreaView>
      
      <View style={styles.contentWrapper}>
        {/* Location and date info */}
        <View style={styles.headerInfo}>
          <View style={styles.locationRow}>
            <MaterialIcons name="location-on" size={16} color="#8E8E93" style={styles.locationIcon} />
            <Text style={styles.locationText}>{tripData?.destination || 'Budapest'}</Text>
            <View style={styles.dateContainer}>
              <Text style={styles.dateText}>
                {tripData ? formatDateRange(tripData.startDate, tripData.endDate, tripData) : 'May 18 — May 21'}
              </Text>
              <Text style={styles.daysText}>{daysCount || parsedItinerary.length} days</Text>
            </View>
          </View>
          
          {/* Trip Completed Button or Status */}
          {tripData?.status === 'completed' ? (
            <View style={styles.completedBadge}>
              <Feather name="check-circle" size={18} color="#34C759" style={styles.completedBadgeIcon} />
              <Text style={styles.completedBadgeText}>Trip Completed</Text>
            </View>
          ) : (
            <TouchableOpacity 
              style={styles.completeButton}
              onPress={markTripCompleted}
              disabled={markingCompleted}
            >
              {markingCompleted ? (
                <ActivityIndicator size="small" color="#FFFFFF" />
              ) : (
                <>
                  <Feather name="check-circle" size={18} color="#FFFFFF" style={styles.completeButtonIcon} />
                  <Text style={styles.completeButtonText}>Mark Trip Completed</Text>
                </>
              )}
            </TouchableOpacity>
          )}
        </View>
        

        
        {/* Day tabs - only show if there's an itinerary */}
        {(localItinerary.length > 0 || parsedItinerary.length > 0) && (
          <ScrollView
            horizontal
            showsHorizontalScrollIndicator={false}
            style={styles.dayTabsContainer}
            contentContainerStyle={styles.dayTabsContent}
          >
            {(localItinerary.length > 0 ? localItinerary : parsedItinerary).map((day) => (
              <TouchableOpacity
                key={`day-${day.day}`}
                style={[
                  styles.dayTab,
                  selectedDay === day.day && styles.selectedDayTab
                ]}
                onPress={() => setSelectedDay(day.day)}
              >
                <Text
                  style={[
                    styles.dayTabText,
                    selectedDay === day.day && styles.selectedDayTabText
                  ]}
                >
                  {day.day}
                </Text>
              </TouchableOpacity>
            ))}
          </ScrollView>
        )}
        
        {/* Activities for selected day */}
        {currentDay && currentDay.items && currentDay.items.length > 0 ? (
          <ScrollView 
            ref={scrollViewRef}
            style={styles.activitiesScrollView}
            contentContainerStyle={[styles.activitiesContent, { paddingBottom: Platform.OS === 'ios' ? 120 : 100 }]}
            showsVerticalScrollIndicator={false}
            keyboardShouldPersistTaps="handled"
            keyboardDismissMode="interactive"
          >
            {currentDay.items.map((activity, index) => (
              <View key={`activity-${index}`} style={styles.activityCard}>

                <View style={styles.activityContent}>
                  <View style={styles.activityHeader}>
                    <Text style={styles.activityTitle}>{activity.title}</Text>
                    <View style={styles.activityHeaderContainer}>
                      <View style={styles.activityHeaderButtons}>
                        {/* Save button */}
                        <TouchableOpacity 
                          onPress={() => isPlaceSaved(activity) 
                            ? handleUnsavePlace(activity) 
                            : handleSavePlace(activity)
                          }
                          style={styles.saveButtonSmall}
                          disabled={savingPlace || unsavingPlace}
                        >
                          {savingPlace || unsavingPlace ? (
                            <ActivityIndicator size="small" color="#34C759" />
                          ) : (
                            <MaterialIcons 
                              name={isPlaceSaved(activity) ? "bookmark" : "bookmark-outline"} 
                              size={20} 
                              color={isPlaceSaved(activity) ? "#34C759" : "#8E8E93"} 
                            />
                          )}
                        </TouchableOpacity>
                      </View>
                    </View>
                  </View>
                  {activity.time && activity.time.length > 0 && (
                    <Text style={styles.activityTime}>{activity.time}</Text>
                  )}
                  {activity.location && activity.location.length > 0 && (
                    <Text style={styles.activityLocation}>{activity.location}</Text>
                  )}
                  {activity.description && activity.description.length > 0 && (
                    <Text style={styles.activityDescription}>{activity.description}</Text>
                  )}



                  <View style={styles.activityButtonsRow}>
                    {activity.link && (
                      <TouchableOpacity
                        style={styles.linkButton}
                        onPress={() => openLink(activity.link || '')}
                      >
                        <Text style={styles.linkButtonText}>View Website</Text>
                      </TouchableOpacity>
                    )}
                  </View>
                  
                  {/* Reorder buttons - positioned absolutely in bottom right */}
                  <View style={styles.reorderButtonsContainer}>
                    {/* Up button */}
                    <TouchableOpacity 
                      onPress={() => {
                        const dayArrayIndex = localItinerary.findIndex(day => day.day === selectedDay);
                        reorderActivity(dayArrayIndex, index, 'up');
                      }}
                      style={[styles.reorderButtonSmall, index === 0 && styles.reorderButtonDisabled]}
                      disabled={index === 0}
                    >
                      <Feather name="arrow-up" size={18} color={index === 0 ? "#CCCCCC" : "#007AFF"} />
                    </TouchableOpacity>
                    
                    {/* Down button */}
                    <TouchableOpacity 
                      onPress={() => {
                        const dayArrayIndex = localItinerary.findIndex(day => day.day === selectedDay);
                        reorderActivity(dayArrayIndex, index, 'down');
                      }}
                      style={[styles.reorderButtonSmall, 
                        index === (localItinerary.find(day => day.day === selectedDay)?.items.length || 0) - 1 && styles.reorderButtonDisabled
                      ]}
                      disabled={index === (localItinerary.find(day => day.day === selectedDay)?.items.length || 0) - 1}
                    >
                      <Feather name="arrow-down" size={18} color={
                        index === (localItinerary.find(day => day.day === selectedDay)?.items.length || 0) - 1 ? "#CCCCCC" : "#007AFF"
                      } />
                    </TouchableOpacity>
                  </View>
                </View>
              </View>
            ))}
            
            {/* Notes Section - Just a button when not focused */}
            {!isNotesFocused && (
              <TouchableOpacity 
                ref={notesRef} 
                style={styles.notesButton}
                onPress={() => setIsNotesFocused(true)}
              >
                <View style={styles.notesButtonContent}>
                  <Feather name="edit-3" size={20} color="#4A4A4A" />
                  <Text style={styles.notesButtonText}>
                    Trip Notes
                  </Text>
                </View>
              </TouchableOpacity>
            )}
            
            {/* Bottom padding to ensure good spacing */}
            <View style={{ height: 20 }} />
          </ScrollView>
        ) : tripData?.trip_type === 'specific_attractions' ? (
          <ScrollView style={styles.activitiesScrollView} contentContainerStyle={styles.activitiesContent}>
            <View style={styles.specificAttractionsContainer}>
              <View style={styles.specificAttractionsHeader}>
                <MaterialIcons name="stars" size={32} color="#3A7BF8" />
                <Text style={styles.specificAttractionsTitle}>Specific Attractions Plan</Text>
                <Text style={styles.specificAttractionsSubtitle}>
                  Your personalized itinerary is being generated based on your preferences
                </Text>
              </View>

              {/* Show the user's preferences */}
              {tripData.city_attractions && tripData.city_attractions.length > 0 && (
                <View style={styles.preferencesSection}>
                  <Text style={styles.preferencesSectionTitle}>City Attractions</Text>
                  <View style={styles.preferencesTagsContainer}>
                    {tripData.city_attractions.map((attraction, index) => (
                      <View key={index} style={styles.preferenceTag}>
                        <Text style={styles.preferenceTagText}>{attraction}</Text>
                      </View>
                    ))}
                  </View>
                </View>
              )}

              {tripData.must_see && tripData.must_see.length > 0 && (
                <View style={styles.preferencesSection}>
                  <Text style={styles.preferencesSectionTitle}>Must-See Items</Text>
                  <View style={styles.preferencesTagsContainer}>
                    {tripData.must_see.map((item, index) => (
                      <View key={index} style={styles.preferenceTag}>
                        <Text style={styles.preferenceTagText}>{item}</Text>
                      </View>
                    ))}
                  </View>
                </View>
              )}

              {tripData.custom_requests && tripData.custom_requests.length > 0 && (
                <View style={styles.preferencesSection}>
                  <Text style={styles.preferencesSectionTitle}>Custom Requests</Text>
                  <View style={styles.preferencesTagsContainer}>
                    {tripData.custom_requests.map((request, index) => (
                      <View key={index} style={styles.preferenceTag}>
                        <Text style={styles.preferenceTagText}>{request}</Text>
                      </View>
                    ))}
                  </View>
                </View>
              )}

              {tripData.interests && tripData.interests.length > 0 && (
                <View style={styles.preferencesSection}>
                  <Text style={styles.preferencesSectionTitle}>Interests</Text>
                  <View style={styles.preferencesTagsContainer}>
                    {tripData.interests.map((interest, index) => (
                      <View key={index} style={styles.preferenceTag}>
                        <Text style={styles.preferenceTagText}>{interest}</Text>
                      </View>
                    ))}
                  </View>
                </View>
              )}

              <View style={styles.generateItinerarySection}>
                <Text style={styles.generateItineraryText}>
                  Ready to generate your personalized itinerary?
                </Text>
                <TouchableOpacity
                  style={styles.generateItineraryButton}
                  onPress={handleGenerateItinerary}
                >
                  <Text style={styles.generateItineraryButtonText}>Generate Itinerary</Text>
                </TouchableOpacity>
              </View>
            </View>
          </ScrollView>
        ) : (
          <View style={styles.emptyStateContainer}>
            <Text style={styles.emptyStateText}>
              No activities found for this day
            </Text>
            <Text style={styles.emptyStateSubtext}>
              Please check back later
            </Text>
          </View>
        )}
      </View>
      
      {/* Bottom tab bar */}
      <CustomTabBar />
      
      {/* Floating Notes Modal - appears when focused */}
      {isNotesFocused && (
        <KeyboardAvoidingView 
          style={styles.floatingNotesOverlay}
          behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
          keyboardVerticalOffset={0}
        >
          <TouchableOpacity 
            style={styles.floatingNotesBackdrop}
            activeOpacity={1}
            onPress={() => {
              Keyboard.dismiss();
              setIsNotesFocused(false);
            }}
          />
          <View style={styles.floatingNotesContainer}>
            <View style={styles.notesHeader}>
              <Text style={styles.notesTitle}>Trip Notes</Text>
              <View style={styles.notesHeaderRight}>
                {isSavingNotes && (
                  <Text style={styles.savingIndicator}>Saving...</Text>
                )}
                <TouchableOpacity 
                  onPress={() => {
                    Keyboard.dismiss();
                    setIsNotesFocused(false);
                  }}
                >
                  <Text style={styles.doneButton}>Done</Text>
                </TouchableOpacity>
              </View>
            </View>
            <TextInput
              style={[styles.notesInput, { height: Math.max(120, notesHeight) }]}
              multiline
              placeholder="Add your travel notes here..."
              placeholderTextColor="#999"
              value={notes}
              onChangeText={(text) => {
                setNotes(text);
                debouncedSaveNotes(text);
              }}
              onContentSizeChange={(event) => {
                setNotesHeight(event.nativeEvent.contentSize.height);
              }}
              autoFocus={true}
            />
          </View>
        </KeyboardAvoidingView>
      )}
      
      {/* Manual Place Entry Modal */}
      <Modal
        animationType="slide"
        transparent={true}
        visible={manualEntryModalVisible}
        onRequestClose={() => setManualEntryModalVisible(false)}
      >
        <View style={styles.modalOverlay}>
          <View style={styles.modalContent}>
            <View style={styles.modalHeader}>
              <Text style={styles.modalTitle}>Add Place Manually</Text>
              <TouchableOpacity onPress={() => setManualEntryModalVisible(false)}>
                <Feather name="x" size={24} color="#000" />
              </TouchableOpacity>
            </View>
            
            <ScrollView style={styles.manualEntryContainer}>
              {/* Title Input */}
              <View style={styles.inputContainer}>
                <Text style={styles.inputLabel}>Title <Text style={styles.requiredStar}>*</Text></Text>
                <TextInput
                  style={styles.textInput}
                  value={manualPlace.title}
                  onChangeText={(text) => setManualPlace({...manualPlace, title: text})}
                  placeholder="Enter place title"
                  placeholderTextColor="#8E8E93"
                />
              </View>
              
              {/* Subtitle Input */}
              <View style={styles.inputContainer}>
                <Text style={styles.inputLabel}>Subtitle</Text>
                <TextInput
                  style={styles.textInput}
                  value={manualPlace.subtitle}
                  onChangeText={(text) => setManualPlace({...manualPlace, subtitle: text})}
                  placeholder="Enter subtitle or location"
                  placeholderTextColor="#8E8E93"
                />
              </View>
              
              {/* Description Input */}
              <View style={styles.inputContainer}>
                <Text style={styles.inputLabel}>Description</Text>
                <TextInput
                  style={[styles.textInput, styles.textAreaInput]}
                  value={manualPlace.description}
                  onChangeText={(text) => setManualPlace({...manualPlace, description: text})}
                  placeholder="Enter place description"
                  placeholderTextColor="#8E8E93"
                  multiline
                  numberOfLines={4}
                  textAlignVertical="top"
                />
              </View>
              
              {/* Coordinates Input */}
              <View style={styles.coordinatesContainer}>
                <View style={[styles.inputContainer, { flex: 1, marginRight: 8 }]}>
                  <Text style={styles.inputLabel}>Latitude</Text>
                  <TextInput
                    style={styles.textInput}
                    value={manualPlace.latitude}
                    onChangeText={(text) => setManualPlace({...manualPlace, latitude: text})}
                    placeholder="e.g. 45.4627"
                    placeholderTextColor="#8E8E93"
                    keyboardType="numeric"
                  />
                </View>
                
                <View style={[styles.inputContainer, { flex: 1 }]}>
                  <Text style={styles.inputLabel}>Longitude</Text>
                  <TextInput
                    style={styles.textInput}
                    value={manualPlace.longitude}
                    onChangeText={(text) => setManualPlace({...manualPlace, longitude: text})}
                    placeholder="e.g. 9.1905"
                    placeholderTextColor="#8E8E93"
                    keyboardType="numeric"
                  />
                </View>
              </View>
              
              {/* External Link Input */}
              <View style={styles.inputContainer}>
                <Text style={styles.inputLabel}>External Link</Text>
                <TextInput
                  style={styles.textInput}
                  value={manualPlace.link}
                  onChangeText={(text) => setManualPlace({...manualPlace, link: text})}
                  placeholder="https://example.com"
                  placeholderTextColor="#8E8E93"
                  autoCapitalize="none"
                  keyboardType="url"
                />
              </View>
              
              {/* Place Type Selection */}
              <View style={styles.inputContainer}>
                <Text style={styles.inputLabel}>Place Type</Text>
                <View style={styles.typeButtonsContainer}>
                  {['attraction', 'restaurant', 'hotel', 'event', 'transport', 'other'].map((type) => (
                    <TouchableOpacity
                      key={type}
                      style={[styles.typeButton, manualPlace.type === type && styles.typeButtonSelected]}
                      onPress={() => setManualPlace({...manualPlace, type})}
                    >
                      <Text style={[styles.typeButtonText, manualPlace.type === type && styles.typeButtonTextSelected]}>
                        {type.charAt(0).toUpperCase() + type.slice(1)}
                      </Text>
                    </TouchableOpacity>
                  ))}
                </View>
              </View>
            </ScrollView>
            
            <View style={styles.modalFooter}>
              <TouchableOpacity
                style={styles.cancelButton}
                onPress={() => setManualEntryModalVisible(false)}
              >
                <Text style={styles.cancelButtonText}>Cancel</Text>
              </TouchableOpacity>
              
              <TouchableOpacity
                style={styles.confirmButton}
                onPress={addManualPlaceToItinerary}
              >
                <Text style={styles.confirmButtonText}>Add Place</Text>
              </TouchableOpacity>
            </View>
          </View>
        </View>
      </Modal>
      
      {/* Place Suggestions Modal */}
      <Modal
        animationType="slide"
        transparent={true}
        visible={suggestionsModalVisible}
        onRequestClose={() => setSuggestionsModalVisible(false)}
      >
        <View style={styles.modalOverlay}>
          <View style={styles.modalContent}>
            <View style={styles.modalHeader}>
              <Text style={styles.modalTitle}>Place Suggestions</Text>
              <TouchableOpacity onPress={() => setSuggestionsModalVisible(false)}>
                <Feather name="x" size={24} color="#000" />
              </TouchableOpacity>
            </View>
            
            <ScrollView style={styles.suggestionsContainer}>
              {placeSuggestions.length > 0 ? (
                placeSuggestions.map((suggestion, index) => (
                  <Pressable 
                    key={suggestion.id} 
                    style={[styles.suggestionCard, suggestion.selected && styles.suggestionCardSelected]}
                    onPress={() => toggleSuggestionSelection(suggestion.id)}
                  >
                    <View style={styles.suggestionContent}>
                      <View style={styles.suggestionHeader}>
                        <Text style={styles.suggestionTitle}>{suggestion.title}</Text>
                        <View style={styles.suggestionTypeContainer}>
                          <Text style={styles.suggestionType}>{suggestion.type}</Text>
                        </View>
                      </View>
                      
                      <Text style={styles.suggestionDescription}>{suggestion.description}</Text>
                      
                      {suggestion.coordinates && (
                        <Text style={styles.suggestionCoordinates}>
                          Coordinates: {suggestion.coordinates.latitude}, {suggestion.coordinates.longitude}
                        </Text>
                      )}
                    </View>
                    
                    <View style={styles.checkboxContainer}>
                      <View style={[styles.checkbox, suggestion.selected && styles.checkboxSelected]}>
                        {suggestion.selected && <Feather name="check" size={16} color="#FFFFFF" />}
                      </View>
                    </View>
                  </Pressable>
                ))
              ) : (
                <View style={styles.loadingContainer}>
                  <ActivityIndicator size="large" color="#007AFF" />
                  <Text style={styles.loadingText}>Finding place suggestions...</Text>
                </View>
              )}
            </ScrollView>
            
            <View style={styles.modalFooter}>
              <TouchableOpacity 
                style={styles.cancelButton}
                onPress={() => setSuggestionsModalVisible(false)}
              >
                <Text style={styles.cancelButtonText}>Cancel</Text>
              </TouchableOpacity>
              
              <TouchableOpacity 
                style={styles.confirmButton}
                onPress={addSelectedSuggestionsToItinerary}
              >
                <Text style={styles.confirmButtonText}>Add Selected Places</Text>
              </TouchableOpacity>
            </View>
          </View>
        </View>
      </Modal>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F9F9F9',
  },
  // Manual place entry modal styles
  manualEntryContainer: {
    maxHeight: Dimensions.get('window').height * 0.6,
    paddingHorizontal: 16,
  },
  inputContainer: {
    marginBottom: 16,
  },
  inputLabel: {
    fontSize: 14,
    fontWeight: '600',
    color: '#333333',
    marginBottom: 8,
  },
  requiredStar: {
    color: '#FF3B30',
  },
  textInput: {
    backgroundColor: '#F2F2F7',
    borderRadius: 8,
    paddingHorizontal: 12,
    paddingVertical: 10,
    fontSize: 16,
    color: '#000000',
    borderWidth: 1,
    borderColor: '#E5E5EA',
  },
  textAreaInput: {
    height: 100,
    paddingTop: 12,
  },
  coordinatesContainer: {
    flexDirection: 'row',
    marginBottom: 16,
  },
  typeButtonsContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 8,
  },
  typeButton: {
    paddingVertical: 8,
    paddingHorizontal: 12,
    borderRadius: 16,
    backgroundColor: '#F2F2F7',
    borderWidth: 1,
    borderColor: '#E5E5EA',
  },
  typeButtonSelected: {
    backgroundColor: '#E3F2FD',
    borderColor: '#007AFF',
  },
  typeButtonText: {
    fontSize: 14,
    color: '#666666',
  },
  typeButtonTextSelected: {
    color: '#007AFF',
    fontWeight: '600',
  },
  contentWrapper: {
    flex: 1,
    backgroundColor: '#F9F9F9',
  },
  headerInfo: {
    backgroundColor: '#FFFFFF',
    paddingTop: 12,
    paddingBottom: 12,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#FFFFFF',
    paddingHorizontal: 20,
  },
  loadingContent: {
    alignItems: 'center',
  },
  loadingSpinner: {
    marginBottom: 20,
  },
  loadingText: {
    fontSize: 18,
    fontWeight: '600',
    color: '#333333',
    marginBottom: 8,
    textAlign: 'center',
  },
  loadingSubtext: {
    fontSize: 14,
    color: '#8E8E93',
    marginBottom: 24,
    textAlign: 'center',
    lineHeight: 20,
  },

  emptyStateContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingVertical: 40,
    minHeight: 300,
  },
  emptyStateText: {
    fontSize: 18,
    fontWeight: '600',
    marginTop: 20,
    color: '#333',
    textAlign: 'center',
  },
  emptyStateSubtext: {
    fontSize: 14,
    color: '#8E8E93',
    marginTop: 8,
    textAlign: 'center',
  },
  // Specific attractions styles
  specificAttractionsContainer: {
    padding: 20,
  },
  specificAttractionsHeader: {
    alignItems: 'center',
    marginBottom: 30,
  },
  specificAttractionsTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#333',
    marginTop: 12,
    textAlign: 'center',
  },
  specificAttractionsSubtitle: {
    fontSize: 16,
    color: '#666',
    marginTop: 8,
    textAlign: 'center',
    lineHeight: 22,
  },
  preferencesSection: {
    marginBottom: 24,
  },
  preferencesSectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#333',
    marginBottom: 12,
  },
  preferencesTagsContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 8,
  },
  preferenceTag: {
    backgroundColor: '#E3F2FD',
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 16,
    borderWidth: 1,
    borderColor: '#3A7BF8',
  },
  preferenceTagText: {
    fontSize: 14,
    color: '#3A7BF8',
    fontWeight: '500',
  },
  generateItinerarySection: {
    alignItems: 'center',
    marginTop: 20,
    padding: 20,
    backgroundColor: '#F8F9FA',
    borderRadius: 12,
  },
  generateItineraryText: {
    fontSize: 16,
    color: '#333',
    textAlign: 'center',
    marginBottom: 16,
  },
  generateItineraryButton: {
    backgroundColor: '#3A7BF8',
    paddingHorizontal: 24,
    paddingVertical: 12,
    borderRadius: 8,
  },
  generateItineraryButtonText: {
    color: '#FFFFFF',
    fontSize: 16,
    fontWeight: '600',
  },
  activitiesScrollView: {
    flex: 1,
    backgroundColor: '#F9F9F9',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 16,
    paddingVertical: 8,
    backgroundColor: 'white',
  },
  headerTitle: {
    fontSize: 22,
    fontWeight: 'bold',
    color: '#000',
    flex: 1,
    textAlign: 'center',
  },
  backButton: {
    padding: 8,
    width: 40,
  },
  deleteButton: {
    padding: 8,
    width: 40,
  },
  scrollView: {
    flex: 1,
  },
  contentContainer: {
    paddingBottom: 100, // Space for bottom tabs
  },
  cityName: {
    fontSize: 28,
    fontWeight: 'bold',
    color: '#000',
    paddingHorizontal: 20,
    paddingTop: 20,
    paddingBottom: 8,
  },
  locationRow: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 20,
    marginBottom: 16,
  },
  locationIcon: {
    marginRight: 4,
  },
  locationText: {
    fontSize: 16,
    color: '#8E8E93',
  },
  dateContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginLeft: 'auto',
  },
  dateText: {
    fontSize: 16,
    color: '#000',
    marginRight: 8,
  },
  daysText: {
    fontSize: 14,
    color: '#8E8E93',
  },
  dayTabsContainer: {
    maxHeight: 50,
    borderBottomWidth: 1,
    borderBottomColor: '#EEEEEE',
  },
  dayTabsContent: {
    paddingHorizontal: 16,
  },
  dayTab: {
    paddingVertical: 12,
    paddingHorizontal: 20,
    marginRight: 8,
    borderRadius: 8,
    backgroundColor: '#F0F0F0',
  },
  selectedDayTab: {
    backgroundColor: '#007AFF',
  },
  dayTabText: {
    fontSize: 16,
    fontWeight: '500',
    color: '#8E8E93',
  },
  selectedDayTabText: {
    color: '#FFFFFF',
  },
  activitiesContainer: {
    flex: 1,
  },
  activitiesContent: {
    paddingHorizontal: 16,
    paddingTop: 16,
    paddingBottom: 80, // Extra padding for bottom tabs
  },
  activityCard: {
    backgroundColor: '#FFFFFF',
    marginBottom: 20,
    borderRadius: 16,
    padding: 20,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.08,
    shadowRadius: 12,
    elevation: 6,
    borderWidth: 1,
    borderColor: '#F5F5F5',
  },

  activityContent: {
    flex: 1,
    paddingBottom: 40, // Added extra padding at bottom to prevent text from being hidden by reorder buttons
  },
  activityHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: 4,
  },
  activityTitle: {
    fontSize: 18,
    fontWeight: '700',
    color: '#1A1A1A',
    flex: 1,
    marginRight: 8,
    marginBottom: 8,
    lineHeight: 24,
  },
  checkCircle: {
    width: 24,
    height: 24,
    borderRadius: 12,
    borderWidth: 2,
    borderColor: '#DDDDDD',
  },
  activityTime: {
    fontSize: 14,
    color: '#8E8E93',
    marginBottom: 6,
    fontWeight: '500',
    backgroundColor: '#F8F9FA',
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 6,
    alignSelf: 'flex-start',
  },
  activityLocation: {
    fontSize: 14,
    color: '#3C3C43',
    marginBottom: 8,
    fontWeight: '500',
  },
  activityCoordinatesContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#F8F9FA',
    paddingHorizontal: 12,
    paddingVertical: 8,
    borderRadius: 8,
    marginBottom: 12,
    borderLeftWidth: 3,
    borderLeftColor: '#007AFF',
  },
  activityDescription: {
    fontSize: 15,
    color: '#4A4A4A',
    lineHeight: 22,
    marginBottom: 16,
    fontWeight: '400',
  },
  activityButtonsRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginTop: 16,
    paddingTop: 12,
    borderTopWidth: 1,
    borderTopColor: '#F0F0F0',
  },
  reorderButtonsContainer: {
    position: 'absolute',
    bottom: 12,
    right: 12,
    flexDirection: 'row',
    alignItems: 'center',
    gap: 6,
    backgroundColor: 'rgba(255, 255, 255, 0.95)',
    borderRadius: 24,
    padding: 6,
    zIndex: 10,
    elevation: 4,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.12,
    shadowRadius: 4,
    borderWidth: 1,
    borderColor: 'rgba(0, 0, 0, 0.05)',
  },
  linkButton: {
    backgroundColor: '#007AFF',
    paddingVertical: 12,
    paddingHorizontal: 16,
    borderRadius: 12,
    alignSelf: 'flex-start',
    flexDirection: 'row',
    alignItems: 'center',
    shadowColor: '#007AFF',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.2,
    shadowRadius: 4,
    elevation: 3,
  },
  linkButtonText: {
    color: '#FFFFFF',
    fontWeight: '600',
    fontSize: 15,
  },
  savedButton: {
    backgroundColor: '#34C759',
    paddingVertical: 8,
    paddingHorizontal: 12,
    borderRadius: 8,
    alignSelf: 'flex-start',
    flexDirection: 'row',
    alignItems: 'center',
    gap: 6,
  },
  saveButtonText: {
    color: '#FFFFFF',
    fontWeight: '600',
    fontSize: 14,
  },
  saveButtonSmall: {
    padding: 8,
    borderRadius: 20,
    alignItems: 'center',
    justifyContent: 'center',
  },
  saveButtonDisabled: {
    opacity: 0.6,
  },
  unsaveButton: {
    backgroundColor: '#FF3B30',
    paddingVertical: 8,
    paddingHorizontal: 12,
    borderRadius: 8,
    alignSelf: 'flex-start',
    flexDirection: 'row',
    alignItems: 'center',
    gap: 6,
  },
  completeButton: {
    backgroundColor: '#34C759',
    paddingVertical: 10,
    paddingHorizontal: 16,
    borderRadius: 8,
    marginHorizontal: 20,
    marginTop: 12,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
  },
  completeButtonIcon: {
    marginRight: 8,
  },
  completeButtonText: {
    color: '#FFFFFF',
    fontWeight: '600',
    fontSize: 16,
  },
  completedBadge: {
    backgroundColor: '#E6F9E9',
    paddingVertical: 10,
    paddingHorizontal: 16,
    borderRadius: 8,
    marginHorizontal: 20,
    marginTop: 12,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
  },
  completedBadgeIcon: {
    marginRight: 8,
  },
  completedBadgeText: {
    color: '#34C759',
    fontWeight: '600',
    fontSize: 16,
  },
  activityHeaderContainer: {
    flexDirection: 'column',
    alignItems: 'flex-end',
    gap: 4,
  },
  activityHeaderButtons: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 4,
  },
  reorderButtonSmall: {
    padding: 8,
    borderRadius: 20,
    alignItems: 'center',
    justifyContent: 'center',
  },
  reorderButtonDisabled: {
    opacity: 0.5,
  },
  deleteButtonSmall: {
    padding: 8,
    borderRadius: 20,
    alignItems: 'center',
    justifyContent: 'center',
  },
  addPlaceButton: {
    backgroundColor: '#007AFF',
    paddingVertical: 12,
    paddingHorizontal: 16,
    borderRadius: 8,
    marginHorizontal: 20,
    marginTop: 16,
    marginBottom: 24,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
  },
  addPlaceButtonIcon: {
    marginRight: 8,
  },
  addPlaceButtonText: {
    color: '#FFFFFF',
    fontWeight: '600',
    fontSize: 16,
  },
  notesContainer: {
    backgroundColor: 'white',
    borderRadius: 12,
    padding: 16,
    marginHorizontal: 16,
    marginTop: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  notesHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  notesHeaderRight: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  notesTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#333',
  },
  savingIndicator: {
    fontSize: 14,
    color: '#999',
    fontStyle: 'italic',
    marginRight: 10,
  },
  doneButton: {
    fontSize: 16,
    fontWeight: '600',
    color: '#007AFF',
  },
  notesInput: {
    backgroundColor: '#F5F5F5',
    borderRadius: 8,
    padding: 12,
    fontSize: 16,
    color: '#333',
    textAlignVertical: 'top',
  },
  notesButton: {
    backgroundColor: 'white',
    borderRadius: 12,
    padding: 12,
    marginHorizontal: 16,
    marginTop: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  notesButtonContent: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
  },
  notesButtonText: {
    fontSize: 16,
    fontWeight: '500',
    color: '#4A4A4A',
    marginLeft: 8,
  },
  floatingNotesOverlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    justifyContent: 'flex-end',
    zIndex: 1000,
  },
  floatingNotesBackdrop: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: 'rgba(0,0,0,0.5)',
  },
  floatingNotesContainer: {
    backgroundColor: 'white',
    borderTopLeftRadius: 16,
    borderTopRightRadius: 16,
    padding: 16,
    paddingBottom: Platform.OS === 'ios' ? 40 : 16,
    marginBottom: Platform.OS === 'ios' ? 0 : 20, // Add extra margin on Android
    shadowColor: '#000',
    shadowOffset: { width: 0, height: -3 },
    shadowOpacity: 0.1,
    shadowRadius: 5,
    elevation: 5,
    maxHeight: '80%', // Limit height to ensure it doesn't take up too much space
  },
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'flex-end',
  },
  modalContent: {
    backgroundColor: '#FFFFFF',
    borderTopLeftRadius: 16,
    borderTopRightRadius: 16,
    paddingTop: 16,
    paddingBottom: 32,
    maxHeight: Dimensions.get('window').height * 0.8,
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingBottom: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#EEEEEE',
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#000000',
  },
  suggestionsContainer: {
    maxHeight: Dimensions.get('window').height * 0.6,
    paddingHorizontal: 16,
  },
  suggestionCard: {
    backgroundColor: '#F9F9F9',
    borderRadius: 12,
    padding: 16,
    marginVertical: 8,
    flexDirection: 'row',
    borderWidth: 1,
    borderColor: '#EEEEEE',
  },
  suggestionCardSelected: {
    backgroundColor: '#E3F2FD',
    borderColor: '#007AFF',
  },
  suggestionContent: {
    flex: 1,
  },
  suggestionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: 8,
  },
  suggestionTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#000000',
    flex: 1,
    marginRight: 8,
  },
  suggestionTypeContainer: {
    backgroundColor: '#EEEEEE',
    paddingVertical: 4,
    paddingHorizontal: 8,
    borderRadius: 4,
  },
  suggestionType: {
    fontSize: 12,
    color: '#666666',
    fontWeight: '500',
  },
  suggestionDescription: {
    fontSize: 14,
    color: '#666666',
    marginBottom: 8,
    lineHeight: 20,
  },
  suggestionCoordinates: {
    fontSize: 12,
    color: '#8E8E93',
    fontStyle: 'italic',
  },
  checkboxContainer: {
    justifyContent: 'center',
    paddingLeft: 12,
  },
  checkbox: {
    width: 24,
    height: 24,
    borderRadius: 12,
    borderWidth: 2,
    borderColor: '#CCCCCC',
    justifyContent: 'center',
    alignItems: 'center',
  },
  checkboxSelected: {
    backgroundColor: '#007AFF',
    borderColor: '#007AFF',
  },
  modalFooter: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    paddingHorizontal: 20,
    paddingTop: 16,
    borderTopWidth: 1,
    borderTopColor: '#EEEEEE',
  },
  cancelButton: {
    paddingVertical: 12,
    paddingHorizontal: 16,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: '#CCCCCC',
  },
  cancelButtonText: {
    color: '#666666',
    fontWeight: '600',
    fontSize: 16,
  },
  confirmButton: {
    backgroundColor: '#007AFF',
    paddingVertical: 12,
    paddingHorizontal: 16,
    borderRadius: 8,
  },
  confirmButtonText: {
    color: '#FFFFFF',
    fontWeight: '600',
    fontSize: 16,
  },
});
