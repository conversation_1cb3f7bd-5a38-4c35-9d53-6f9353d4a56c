import React, { useState } from 'react';
import { StyleSheet, View, ScrollView, Image, TouchableOpacity, ImageBackground } from 'react-native';
import { Text } from 'react-native-paper';
import { SafeAreaView } from 'react-native-safe-area-context';
import { StatusBar } from 'expo-status-bar';
import { useLocalSearchParams, router } from 'expo-router';
import { FontAwesome5, Feather, MaterialIcons, Ionicons } from '@expo/vector-icons';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { useTranslation } from 'react-i18next';

// Budapest trip data - this would normally come from a backend
const budapestTrip = {
  id: 'budapest',
  city: 'Budapest',
  countryCode: 'HU',
  image: 'https://images.unsplash.com/photo-1551867633-194f125baaaa?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2340&q=80',
  startDate: new Date(2024, 3, 20), // April 20, 2024
  endDate: new Date(2024, 3, 22), // April 22, 2024
  days: [
    {
      id: 'day1',
      label: 'Day 1',
      activities: [
        {
          id: 'buda-castle',
          title: 'Walking Tour of Buda Castle',
          time: '9:00 AM - 11:00 AM',
          location: 'Buda Castle',
          description: 'Explore the historic castle complex and enjoy panoramic views of the city.',
          type: 'attraction',
          completed: false,
          mapImage: null,
          aiRecommended: false
        },
        {
          id: 'market-hall',
          title: 'Lunch at Central Market Hall',
          time: null,
          location: 'Central Market Hall',
          description: null,
          type: 'food',
          completed: false,
          mapImage: null,
          aiRecommended: false
        },
        {
          id: 'st-stephens',
          title: 'St. Stephen\'s Basilica',
          time: null,
          location: 'St. Stephen\'s Basilica',
          description: 'Visit the stunning baslica and take in the impressive interior and views from the dome',
          type: 'attraction',
          completed: false,
          mapImage: null,
          aiRecommended: true
        },
        {
          id: 'hungarikum-bistro',
          title: 'Dinner at Hungarikum Bistro',
          time: null,
          location: 'Hundarikum Bistro',
          description: null,
          type: 'food',
          completed: false,
          mapImage: null,
          aiRecommended: false
        }
      ]
    },
    {
      id: 'day2',
      label: 'Day 2',
      activities: [
        {
          id: 'szechenyi-baths',
          title: 'Széchenyi Thermal Baths',
          time: '10:00 AM - 1:00 PM',
          location: 'Széchenyi Thermal Baths',
          description: 'Relax in Budapest\'s famous thermal baths',
          type: 'attraction',
          completed: false,
          mapImage: null,
          aiRecommended: false
        }
      ]
    },
    {
      id: 'day3',
      label: 'Day 3',
      activities: [
        {
          id: 'hungarian-parliament',
          title: 'Hungarian Parliament Tour',
          time: '9:30 AM - 11:30 AM',
          location: 'Hungarian Parliament Building',
          description: 'Tour one of Europe\'s oldest legislative buildings',
          type: 'attraction',
          completed: false,
          mapImage: null,
          aiRecommended: false
        }
      ]
    }
  ]
};

export default function TripDetailScreen() {
  const params = useLocalSearchParams();
  const { id } = params;
  const insets = useSafeAreaInsets();
  const { t } = useTranslation();
  
  // For a real app, this would fetch trip data based on the ID
  // For now, we'll just use the Budapest data
  const trip = budapestTrip;
  
  const [selectedDay, setSelectedDay] = useState('day1');
  
  const formatDateRange = (start: Date, end: Date) => {
    // Use the user's current language for date formatting
    const options: Intl.DateTimeFormatOptions = { month: 'short', day: 'numeric' };
    const startStr = start.toLocaleDateString(undefined, options);
    const endStr = end.toLocaleDateString(undefined, options);
    return `${startStr} — ${endStr}`;
  };
  
  const calculateDays = (start: Date, end: Date) => {
    const diffTime = Math.abs(end.getTime() - start.getTime());
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    return `${diffDays} days`;
  };
  
  const getActivityIcon = (type: string) => {
    switch (type) {
      case 'attraction':
        return <FontAwesome5 name="monument" size={24} color="white" style={styles.activityIconBg} />;
      case 'food':
        return <MaterialIcons name="restaurant" size={24} color="white" style={styles.activityIconBg} />;
      default:
        return <Feather name="map-pin" size={24} color="white" style={styles.activityIconBg} />;
    }
  };
  
  const currentDay = trip.days.find(day => day.id === selectedDay);
  
  return (
    <View style={styles.container}>
      <StatusBar style="light" />
      
      {/* Header Image */}
      <View style={styles.headerContainer}>
        <ImageBackground
          source={{ uri: trip.image }}
          style={styles.headerImage}
          imageStyle={{ opacity: 0.9 }}
        >
          <SafeAreaView edges={['top']} style={styles.headerContent}>
            <TouchableOpacity 
              style={styles.backButton}
              onPress={() => router.back()}
            >
              <Feather name="chevron-left" size={28} color="white" />
            </TouchableOpacity>
          </SafeAreaView>
        </ImageBackground>
      </View>
      
      {/* Trip Details */}
      <View style={styles.tripInfoContainer}>
        <Text style={styles.cityName}>{trip.city}</Text>
        <View style={styles.locationRow}>
          <Feather name="map-pin" size={18} color="#8E8E93" style={styles.locationIcon} />
          <Text style={styles.locationText}>{trip.city}</Text>
          <View style={styles.dateContainer}>
            <Text style={styles.dateText}>
              {formatDateRange(trip.startDate, trip.endDate)}
            </Text>
            <Text style={styles.daysText}>
              {calculateDays(trip.startDate, trip.endDate)}
            </Text>
          </View>
        </View>
        
        {/* Day Selection Tabs */}
        <ScrollView 
          horizontal 
          showsHorizontalScrollIndicator={false}
          style={styles.dayTabsContainer}
          contentContainerStyle={styles.dayTabsContent}
        >
          {trip.days.map(day => (
            <TouchableOpacity
              key={day.id}
              style={[
                styles.dayTab,
                selectedDay === day.id && styles.selectedDayTab
              ]}
              onPress={() => setSelectedDay(day.id)}
            >
              <Text style={[
                styles.dayTabText,
                selectedDay === day.id && styles.selectedDayTabText
              ]}>
                {day.label}
              </Text>
            </TouchableOpacity>
          ))}
        </ScrollView>
        
        {/* Activities List */}
        <ScrollView
          style={styles.activitiesContainer}
          contentContainerStyle={[
            styles.activitiesContent,
            { paddingBottom: 90 + insets.bottom } // Add padding for tab bar
          ]}
        >
          {currentDay?.activities.map((activity, index) => (
            <View key={activity.id} style={styles.activityCard}>
              {/* Activity Icon */}
              <View style={styles.activityIconContainer}>
                {getActivityIcon(activity.type)}
              </View>
              
              {/* Activity Content */}
              <View style={styles.activityContent}>
                <View style={styles.activityHeader}>
                  <Text style={styles.activityTitle}>{activity.title}</Text>
                  <TouchableOpacity style={styles.checkCircle}>
                    {/* Leave empty for unchecked state */}
                  </TouchableOpacity>
                </View>
                
                {activity.time && (
                  <Text style={styles.activityTime}>{activity.time}</Text>
                )}
                
                <Text style={styles.activityLocation}>{activity.location}</Text>
                
                {activity.description && (
                  <Text style={styles.activityDescription}>{activity.description}</Text>
                )}
                
                {activity.id === 'st-stephens' && (
                  <View style={styles.mapImageContainer}>
                    <Image 
                      source={{ uri: 'https://maps.googleapis.com/maps/api/staticmap?center=St+Stephens+Basilica+Budapest&zoom=15&size=600x300&maptype=roadmap&markers=color:red%7CSt+Stephens+Basilica+Budapest&key=YOUR_API_KEY_HERE' }} 
                      style={styles.mapImage} 
                    />
                  </View>
                )}
                
                {activity.aiRecommended && (
                  <View style={styles.aiRecommendedTag}>
                    <Text style={styles.aiRecommendedText}>AI Recommended</Text>
                  </View>
                )}
              </View>
            </View>
          ))}
        </ScrollView>
      </View>
      
      {/* Bottom Action Buttons */}
      <View style={[styles.bottomActions, { paddingBottom: insets.bottom || 16 }]}>
          <TouchableOpacity style={styles.editButton} onPress={() => router.push(`/trip/edit/${trip.id}`)}>
            <Text style={styles.editButtonText}>{t('tripPlanner.editTrip')}</Text>
          </TouchableOpacity>
          
          <TouchableOpacity style={styles.favButton}>
            <Ionicons name="heart-outline" size={18} color="#007AFF" />
            <Text style={styles.favButtonText}>{t('common.save')}</Text>
          </TouchableOpacity>
          
          <TouchableOpacity style={styles.exportButton}>
            <Feather name="share" size={18} color="#007AFF" />
            <Text style={styles.exportButtonText}>{t('tripPlanner.share')}</Text>
          </TouchableOpacity>
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#FFFFFF',
  },
  headerContainer: {
    height: 240,
    width: '100%',
  },
  headerImage: {
    width: '100%',
    height: '100%',
    backgroundColor: 'rgba(0,0,0,0.2)',
  },
  headerContent: {
    flex: 1,
    padding: 16,
  },
  backButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: 'rgba(0,0,0,0.3)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  tripInfoContainer: {
    flex: 1,
    backgroundColor: '#F9F9F9',
    borderTopLeftRadius: 24,
    borderTopRightRadius: 24,
    marginTop: -24,
  },
  cityName: {
    fontSize: 28,
    fontWeight: 'bold',
    color: '#000',
    paddingHorizontal: 20,
    paddingTop: 20,
    paddingBottom: 8,
  },
  locationRow: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 20,
    marginBottom: 16,
  },
  locationIcon: {
    marginRight: 4,
  },
  locationText: {
    fontSize: 16,
    color: '#8E8E93',
  },
  dateContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginLeft: 'auto',
  },
  dateText: {
    fontSize: 16,
    color: '#000',
    marginRight: 8,
  },
  daysText: {
    fontSize: 14,
    color: '#8E8E93',
  },
  dayTabsContainer: {
    maxHeight: 50,
    borderBottomWidth: 1,
    borderBottomColor: '#EEEEEE',
  },
  dayTabsContent: {
    paddingHorizontal: 16,
  },
  dayTab: {
    paddingVertical: 12,
    paddingHorizontal: 20,
    marginRight: 8,
    borderRadius: 8,
    backgroundColor: '#F0F0F0',
  },
  selectedDayTab: {
    backgroundColor: '#007AFF',
  },
  dayTabText: {
    fontSize: 16,
    fontWeight: '500',
    color: '#8E8E93',
  },
  selectedDayTabText: {
    color: '#FFFFFF',
  },
  activitiesContainer: {
    flex: 1,
  },
  activitiesContent: {
    paddingHorizontal: 16,
    paddingTop: 16,
  },
  activityCard: {
    flexDirection: 'row',
    backgroundColor: '#FFFFFF',
    marginBottom: 16,
    borderRadius: 12,
    padding: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.05,
    shadowRadius: 3,
    elevation: 3,
  },
  activityIconContainer: {
    marginRight: 12,
    alignItems: 'center',
  },
  activityIconBg: {
    backgroundColor: '#007AFF',
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 8,
    overflow: 'hidden',
  },
  activityContent: {
    flex: 1,
  },
  activityHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: 4,
  },
  activityTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#000',
    flex: 1,
    marginRight: 8,
  },
  checkCircle: {
    width: 24,
    height: 24,
    borderRadius: 12,
    borderWidth: 2,
    borderColor: '#DDDDDD',
  },
  activityTime: {
    fontSize: 14,
    color: '#8E8E93',
    marginBottom: 2,
  },
  activityLocation: {
    fontSize: 14,
    color: '#3C3C43',
    marginBottom: 6,
  },
  activityDescription: {
    fontSize: 14,
    color: '#3C3C43',
    lineHeight: 20,
    marginBottom: 12,
  },
  mapImageContainer: {
    width: '100%',
    height: 120,
    borderRadius: 8,
    overflow: 'hidden',
    marginBottom: 8,
  },
  mapImage: {
    width: '100%',
    height: '100%',
    borderRadius: 8,
  },
  aiRecommendedTag: {
    backgroundColor: '#F0F0F0',
    paddingHorizontal: 10,
    paddingVertical: 4,
    borderRadius: 12,
    alignSelf: 'flex-start',
  },
  aiRecommendedText: {
    fontSize: 12,
    color: '#666',
  },
  bottomActions: {
    flexDirection: 'row',
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    backgroundColor: 'white',
    borderTopWidth: 1,
    borderTopColor: '#EEEEEE',
    paddingTop: 12,
    paddingHorizontal: 16,
    paddingBottom: 16,
    justifyContent: 'space-between',
  },
  editButton: {
    backgroundColor: '#007AFF',
    padding: 12,
    borderRadius: 8,
    flex: 0.3,
    alignItems: 'center',
  },
  editButtonText: {
    color: 'white',
    fontWeight: '600',
    fontSize: 16,
  },
  favButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: '#F0F0F0',
    padding: 12,
    borderRadius: 8,
    flex: 0.35,
    marginHorizontal: 8,
  },
  favButtonText: {
    color: '#007AFF',
    fontWeight: '600',
    fontSize: 14,
    marginLeft: 4,
  },
  exportButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: '#F0F0F0',
    padding: 12,
    borderRadius: 8,
    flex: 0.3,
  },
  exportButtonText: {
    color: '#007AFF',
    fontWeight: '600',
    fontSize: 14,
    marginLeft: 4,
  },
}); 