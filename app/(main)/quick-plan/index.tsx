import React, { useState, useRef, useEffect } from 'react';
import { useAuth } from '../../../contexts/AuthContext';
import {
  StyleSheet,
  View,
  ScrollView,
  KeyboardAvoidingView,
  Platform,
  TextInput,
  TouchableOpacity,
  Keyboard,
  ActivityIndicator,
  Dimensions
} from 'react-native';
import { useTranslation } from 'react-i18next';
import { Text } from 'react-native-paper';
import { SafeAreaView } from 'react-native-safe-area-context';
import { MaterialIcons, Feather, Ionicons } from '@expo/vector-icons';
import { StatusBar } from 'expo-status-bar';
import { router } from 'expo-router';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import Toast from 'react-native-toast-message';
import * as Location from 'expo-location';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { doc, getDoc } from 'firebase/firestore';
import { db } from '../../../services/firebase';
import { CopilotStep, walkthroughable } from 'react-native-copilot';
import { TUTORIAL_STEPS } from '../../../components/tutorial/TutorialWrapper';

// Create walkthroughable components
const CopilotTouchableOpacity = walkthroughable(TouchableOpacity);

// Message type definition
type Message = {
  id: string;
  text: string;
  isUser: boolean;
  timestamp: Date;
};

// Constants for AsyncStorage
const STORAGE_KEY_PREFIX = 'tripzy_quick_plan_messages_user_';

// Fixed values for layout
const TAB_BAR_HEIGHT = 80; // Height of the bottom tab bar

// Save messages to AsyncStorage with user-specific key
const saveMessagesToStorage = async (messages: Message[], userId: string) => {
  try {
    if (!userId) {
      console.error('Cannot save messages: No user ID provided');
      return;
    }
    
    // Convert Date objects to strings for storage
    const messagesToStore = messages.map(msg => ({
      ...msg,
      timestamp: msg.timestamp.toISOString()
    }));
    
    // Use user-specific storage key
    const userStorageKey = `${STORAGE_KEY_PREFIX}${userId}`;
    await AsyncStorage.setItem(userStorageKey, JSON.stringify(messagesToStore));
  } catch (error) {
    console.error('Error saving messages to storage:', error);
  }
};

// Load messages from AsyncStorage with user-specific key
const loadMessagesFromStorage = async (userId: string): Promise<Message[]> => {
  try {
    if (!userId) {
      console.error('Cannot load messages: No user ID provided');
      return getDefaultWelcomeMessage();
    }
    
    // Use user-specific storage key
    const userStorageKey = `${STORAGE_KEY_PREFIX}${userId}`;
    const storedMessages = await AsyncStorage.getItem(userStorageKey);
    
    if (storedMessages) {
      // Convert string timestamps back to Date objects
      const parsedMessages = JSON.parse(storedMessages);
      return parsedMessages.map((msg: any) => ({
        ...msg,
        timestamp: new Date(msg.timestamp)
      }));
    }
  } catch (error) {
    console.error('Error loading messages from storage:', error);
  }
  
  // Return default welcome message if no stored messages
  return getDefaultWelcomeMessage();
};

// Helper function to get default welcome message
const getDefaultWelcomeMessage = (): Message[] => {
  return [
    {
      id: '1',
      text: "Hi there! I'm your Tripzy AI assistant. I can help you with travel recommendations, finding restaurants, attractions, or activities in specific cities. What can I help you with today?",
      isUser: false,
      timestamp: new Date(),
    }
  ];
};

// Real API call to Firebase function endpoint
const fetchAIResponse = async (
  message: string,
  userId: string,
  appLanguage: string,
  location?: { latitude: number; longitude: number },
  city?: string
): Promise<string> => {
  try {
    // Fetch user preferences from Firestore
    const docRef = doc(db, "users", userId);
    const userSnap = await getDoc(docRef);
    const userData = userSnap.data();

    // Extract preferences using correct Firestore field names
    const preferences = {
      budget: userData?.budgetPreference || null,
      interests: userData?.interests || [],
      activityLevel: userData?.activityLevel || null,
      travelCompanion: userData?.travelCompanion || null,
      travelChallenges: userData?.travelChallenges || [],
      travelPreferences: userData?.travelPreferences || null,
    };

    const requestBody = {
      message,
      ...(location && { location }),
      ...(city && { city }),
      language: appLanguage,
      preferences: preferences,
    };

    console.log('Sending request to AI with data:', JSON.stringify(requestBody));

    const response = await fetch('https://europe-west1-traveling-app-c19a8.cloudfunctions.net/chatReply', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(requestBody),
    });

    if (!response.ok) {
      throw new Error(`Network response was not ok: ${response.status}`);
    }

    const data = await response.json();
    return data.reply;
  } catch (error) {
    console.error('Error fetching AI response:', error);
    throw error;
  }
};

export default function QuickPlanScreen() {
  const insets = useSafeAreaInsets();
  const { t, i18n } = useTranslation();
  const { user } = useAuth(); // Get current user
  const [input, setInput] = useState('');
  const [messages, setMessages] = useState<Message[]>([]);
  const [messagesLoaded, setMessagesLoaded] = useState(false);
  const [isTyping, setIsTyping] = useState(false);
  
  // Location state
  const [location, setLocation] = useState<Location.LocationObject | null>(null);
  const [userCity, setUserCity] = useState<string | null>(null);
  
  const scrollViewRef = useRef<ScrollView>(null);
  const inputRef = useRef<TextInput>(null);
  
  // We use the TAB_BAR_HEIGHT constant defined outside the component

  // Load messages from AsyncStorage when component mounts
  useEffect(() => {
    const loadMessages = async () => {
      if (user?.uid) {
        const loadedMessages = await loadMessagesFromStorage(user.uid);
        setMessages(loadedMessages);
        setMessagesLoaded(true);
      }
    };
    
    if (user) {
      loadMessages();
    }
  }, [user]);  // Re-run when user changes
  
  // Save messages to AsyncStorage whenever they change
  useEffect(() => {
    if (messagesLoaded && messages.length > 0 && user?.uid) {
      saveMessagesToStorage(messages, user.uid);
    }
  }, [messages, messagesLoaded, user]);  // Re-run when user changes
  
  // Get location permission and user location when component mounts
  useEffect(() => {
    const getLocationPermission = async () => {
      try {
        const { status } = await Location.requestForegroundPermissionsAsync();
        
        if (status !== 'granted') {
          console.log('Location permission denied');
          return;
        }
        
        // Get current location
        const currentLocation = await Location.getCurrentPositionAsync({});
        setLocation(currentLocation);
        
        // Reverse geocode to get city name
        if (currentLocation) {
          const geocode = await Location.reverseGeocodeAsync({
            latitude: currentLocation.coords.latitude,
            longitude: currentLocation.coords.longitude,
          });
          
          if (geocode && geocode.length > 0) {
            const city = geocode[0].city;
            if (city) {
              setUserCity(city);
              console.log('User city:', city);
            }
          }
        }
      } catch (error) {
        console.error('Error getting location:', error);
      }
    };
    
    // Call the function
    getLocationPermission();
    
    // Scroll to bottom and set up keyboard listeners
    scrollToBottom();
    
    // Add keyboard listeners to scroll to bottom when keyboard opens
    const keyboardDidShowListener = Keyboard.addListener(
      'keyboardDidShow',
      () => {
        scrollToBottom();
      }
    );
    
    // Clean up listeners
    return () => {
      keyboardDidShowListener.remove();
    };
  }, []);

  const handleSuggestionPress = (suggestion: string) => {
    handleSendMessage(suggestion);
  };

  const handleSendMessage = async (messageText: string) => {
    // Validate input
    if (!messageText.trim()) {
      Toast.show({
        type: 'error',
        text1: 'Empty message',
        text2: 'Please enter a message to send.',
        position: 'bottom',
      });
      return;
    }

    // Add user message
    const userMessage: Message = {
      id: Date.now().toString(),
      text: messageText.trim(),
      isUser: true,
      timestamp: new Date(),
    };

    setMessages(prev => [...prev, userMessage]);
    setInput('');

    // Show typing indicator
    setIsTyping(true);
    scrollToBottom();

    try {
      // Validate user is available
      if (!user?.uid) {
        throw new Error('User not authenticated');
      }

      // Get current app language
      const appLanguage = i18n.language || 'en';

      // Get AI response with location data if available
      let responseText;
      if (location && userCity) {
        responseText = await fetchAIResponse(
          messageText,
          user.uid,
          appLanguage,
          {
            latitude: location.coords.latitude,
            longitude: location.coords.longitude
          },
          userCity
        );
      } else {
        responseText = await fetchAIResponse(messageText, user.uid, appLanguage);
      }

      // Add AI message
      const aiMessage: Message = {
        id: (Date.now() + 1).toString(),
        text: responseText,
        isUser: false,
        timestamp: new Date(),
      };

      setMessages(prev => [...prev, aiMessage]);
    } catch (error) {
      console.error('Error getting response:', error);
      
      // Show error toast
      Toast.show({
        type: 'error',
        text1: 'Connection Error',
        text2: 'Could not connect to the AI service. Please try again later.',
        position: 'bottom',
      });
    } finally {
      setIsTyping(false);
      scrollToBottom();
    }
  };
  
  const scrollToBottom = () => {
    setTimeout(() => {
      if (scrollViewRef.current) {
        scrollViewRef.current.scrollToEnd({ animated: true });
      }
    }, 100);
  };
  
  // Scroll to bottom whenever messages change
  useEffect(() => {
    if (messagesLoaded && messages.length > 0) {
      scrollToBottom();
    }
  }, [messages, messagesLoaded]);

  // Pre-defined suggestion buttons
  const suggestions = [
    { icon: 'restaurant', text: 'Find restaurant nearby' },
    { icon: 'attractions', text: 'Top attractions to visit' },
    { icon: 'hiking', text: 'Day trip ideas nearby' },
  ];

  // State to store the calculated tab bar height
  const [adjustedTabBarHeight, setAdjustedTabBarHeight] = useState(0);
  
  // Calculate the tab bar height based on screen dimensions and device type
  const calculateTabBarHeight = () => {
    const { width, height } = Dimensions.get('window');
    const isSmallScreen = height < 700;
    
    // For iOS devices with notch (iPhone X and newer), we need more space
    const hasNotch = Platform.OS === 'ios' && insets.bottom > 0;
    
    // Base tab bar height
    let tabBarHeight = hasNotch ? 100 : (Platform.OS === 'ios' ? 85 : 75);
    
    // Adjust for smaller screens
    if (isSmallScreen) {
      tabBarHeight = tabBarHeight - 10;
    }
    
    // Ensure we have enough space above the tab bar (minimum 60px)
    return Math.max(60, tabBarHeight);
  };
  
  // Update tab bar height when dimensions change
  useEffect(() => {
    // Calculate initial height
    setAdjustedTabBarHeight(calculateTabBarHeight());
    
    // Add event listener for dimension changes
    const dimensionsHandler = Dimensions.addEventListener('change', () => {
      setAdjustedTabBarHeight(calculateTabBarHeight());
    });
    
    // Clean up
    return () => {
      dimensionsHandler.remove();
    };
  }, [insets]);
  
  return (
    <View style={styles.container}>
      <StatusBar style="dark" />
      
      {/* Header */}
      <SafeAreaView edges={['top']} style={styles.safeTop}>
        <View style={styles.header}>
          <View style={styles.backButtonPlaceholder}></View>
          <Text style={styles.headerTitle}>{t('quickPlan.title')}</Text>
          <View style={styles.backButtonPlaceholder}></View>
        </View>
      </SafeAreaView>
      
      {/* Suggestion buttons */}
      <View style={styles.suggestionsContainer}>
        <CopilotStep
          text="Tap here to get restaurant recommendations near your location."
          order={5}
          name={TUTORIAL_STEPS.FIND_RESTAURANT}
        >
          <CopilotTouchableOpacity
            style={styles.suggestionButton}
            onPress={() => handleSuggestionPress(t('quickPlan.findRestaurantNearby'))}
          >
            <Ionicons name="restaurant" size={20} color="#4689FF" style={styles.suggestionIcon} />
            <Text style={styles.suggestionText}>{t('quickPlan.findRestaurantNearby')}</Text>
          </CopilotTouchableOpacity>
        </CopilotStep>

        <CopilotStep
          text="Get suggestions for the top attractions to visit in your area."
          order={6}
          name={TUTORIAL_STEPS.TOP_ATTRACTIONS}
        >
          <CopilotTouchableOpacity
            style={styles.suggestionButton}
            onPress={() => handleSuggestionPress(t('quickPlan.topAttractionsToVisit'))}
          >
            <Ionicons name="compass" size={20} color="#4689FF" style={styles.suggestionIcon} />
            <Text style={styles.suggestionText}>{t('quickPlan.topAttractionsToVisit')}</Text>
          </CopilotTouchableOpacity>
        </CopilotStep>

        <CopilotStep
          text="Discover day trip ideas and nearby destinations to explore."
          order={7}
          name={TUTORIAL_STEPS.DAY_TRIP_IDEAS}
        >
          <CopilotTouchableOpacity
            style={styles.suggestionButton}
            onPress={() => handleSuggestionPress(t('quickPlan.dayTripIdeasNearby'))}
          >
            <Ionicons name="map" size={20} color="#4689FF" style={styles.suggestionIcon} />
            <Text style={styles.suggestionText}>{t('quickPlan.dayTripIdeasNearby')}</Text>
          </CopilotTouchableOpacity>
        </CopilotStep>
      </View>
      
      {/* Messages */}
      <View style={[styles.messagesContainer, { marginBottom: 120 }]}>
        <ScrollView
          ref={scrollViewRef}
          contentContainerStyle={styles.messagesContent}
          showsVerticalScrollIndicator={false}
        >
          {messages.map((message) => (
            <View
              key={message.id}
              style={[
                styles.messageBubble,
                message.isUser ? styles.userBubble : styles.aiBubble,
              ]}
            >
              {!message.isUser && (
                <View style={styles.avatarContainer}>
                  <View style={styles.avatar}>
                    <Text style={styles.avatarText}>AI</Text>
                  </View>
                </View>
              )}
              
              <View
                style={[
                  styles.messageContent,
                  message.isUser
                    ? styles.userMessageContent
                    : styles.aiMessageContent,
                ]}
              >
                <Text
                  style={[
                    styles.messageText,
                    message.isUser
                      ? styles.userMessageText
                      : styles.aiMessageText,
                  ]}
                >
                  {message.text}
                </Text>
                
                {/* Bookmark button removed as requested */}
              </View>
              
              {message.isUser && <View style={styles.avatarPlaceholder} />}
            </View>
          ))}
          
          {isTyping && (
            <View
              style={[
                styles.messageBubble,
                styles.aiBubble,
              ]}
            >
              <View style={styles.avatarContainer}>
                <View style={styles.avatar}>
                  <Text style={styles.avatarText}>AI</Text>
                </View>
              </View>
              
              <View
                style={[
                  styles.messageContent,
                  styles.aiMessageContent,
                ]}
              >
                <View style={styles.typingIndicator}>
                  <ActivityIndicator
                    size="small"
                    color="#4689FF"
                    style={styles.typingDot}
                  />
                  <Text style={styles.typingText}>
                    Thinking...
                  </Text>
                </View>
              </View>
            </View>
          )}
        </ScrollView>
      </View>
      
      {/* Fixed Input Area at bottom */}
      <KeyboardAvoidingView
        behavior="position"
        keyboardVerticalOffset={Platform.OS === 'ios' ? -10 : 0}
        style={styles.inputWrapper}
        enabled
      >
        <View style={styles.bottomContainer}>
          <View style={styles.inputArea}>
            <View style={styles.inputContainer}>
              <TextInput
                ref={inputRef}
                style={styles.input}
                placeholder={t('quickPlan.typeMessage')}
                value={input}
                onChangeText={setInput}
                multiline={false}
                returnKeyType="send"
                onSubmitEditing={() => handleSendMessage(input)}
              />
              
              <TouchableOpacity
                style={[
                  styles.sendButton,
                  !input.trim() && styles.sendButtonDisabled,
                ]}
                onPress={() => handleSendMessage(input)}
                disabled={!input.trim()}
              >
                <Ionicons name="send" size={20} color="white" style={styles.sendIcon} />
              </TouchableOpacity>
            </View>
          </View>
        </View>
      </KeyboardAvoidingView>
      
      {/* Toast Component */}
      <Toast />
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#FFFFFF',
  },
  safeTop: {
    backgroundColor: '#FFFFFF',
    width: '100%',
  },
  inputWrapper: {
    position: 'absolute',
    left: 0,
    right: 0,
    bottom: Platform.OS === 'ios' ? 80 : 70, // Adjusted for better positioning
    width: '100%',
    zIndex: 999,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingVertical: 10,
    paddingHorizontal: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#EEEEEE',
  },
  backButton: {
    padding: 5,
  },
  backButtonPlaceholder: {
    width: 40,
  },
  headerTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#000000',
  },
  suggestionsContainer: {
    paddingTop: 8,
    paddingHorizontal: 16,
    gap: 6,
    flexDirection: 'column',
    marginBottom: 8,
  },
  suggestionButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 8,
    paddingHorizontal: 12,
    backgroundColor: '#F8F8F8',
    borderRadius: 10,
    borderWidth: 1,
    borderColor: '#EEEEEE',
    marginBottom: 6,
    width: '100%',
    minHeight: 40,
  },
  suggestionIcon: {
    marginRight: 8,
    fontSize: 18,
    minWidth: 20,
    color: '#4689FF',
  },
  suggestionText: {
    flex: 1,
    fontSize: 14,
    fontWeight: '500',
    color: '#333333',
    flexShrink: 1,
    flexWrap: 'wrap',
    lineHeight: 18,
  },
  messagesContainer: {
    flex: 1,
    paddingHorizontal: 16,
    paddingTop: 12,
  },
  messagesContent: {
    paddingBottom: 50,
    flexGrow: 1,
  },
  messageBubble: {
    flexDirection: 'row',
    marginBottom: 24,
    alignItems: 'flex-start',
  },
  userBubble: {
    justifyContent: 'flex-end',
  },
  aiBubble: {
    justifyContent: 'flex-start',
  },
  avatarContainer: {
    width: 40,
    height: 40,
    marginRight: 12,
  },
  avatarPlaceholder: {
    width: 40,
    marginLeft: 12,
  },
  avatar: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: '#D0D0D0',
    justifyContent: 'center',
    alignItems: 'center',
  },
  avatarText: {
    fontSize: 16,
    color: 'white',
    fontWeight: 'bold',
  },
  messageContent: {
    borderRadius: 20,
    paddingVertical: 12,
    paddingHorizontal: 16,
    maxWidth: '70%',
    position: 'relative',
  },
  userMessageContent: {
    backgroundColor: '#E1EFFE',
    borderBottomRightRadius: 4,
  },
  aiMessageContent: {
    backgroundColor: '#F9F9F9',
    borderWidth: 1,
    borderColor: '#EEEEEE',
    borderBottomLeftRadius: 4,
  },
  messageText: {
    fontSize: 16,
    lineHeight: 22,
  },
  userMessageText: {
    color: '#000000',
  },
  aiMessageText: {
    color: '#000000',
  },
  typingIndicator: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  typingDot: {
    marginRight: 8,
  },
  typingText: {
    fontSize: 16,
    color: '#A0A0A0',
    fontStyle: 'italic',
  },
  bottomContainer: {
    width: '100%',
    backgroundColor: '#FFFFFF',
    borderTopWidth: 1,
    borderTopColor: '#EEEEEE',
    paddingVertical: 10,
    marginBottom: Platform.OS === 'ios' ? 20 : 10,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: -2 },
    shadowOpacity: 0.1,
    shadowRadius: 3,
    elevation: 5,
  },
  inputArea: {
    width: '100%',
    padding: 8,
    paddingHorizontal: 16,
  },
  inputContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#F5F5F5',
    borderRadius: 25,
    paddingHorizontal: 16,
    height: 48,
    borderWidth: 1,
    borderColor: '#EEEEEE',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 1,
    elevation: 2,
  },
  input: {
    flex: 1,
    height: 48,
    fontSize: 16,
    color: '#000000',
  },
  sendButton: {
    backgroundColor: '#4689FF',
    width: 36,
    height: 36,
    borderRadius: 18,
    justifyContent: 'center',
    alignItems: 'center',
    marginLeft: 5,
  },
  sendButtonDisabled: {
    backgroundColor: '#A0A0A0',
  },
  sendIcon: {
    marginLeft: 2,
  },
  bookmarkButton: {
    position: 'absolute',
    top: 12,
    right: 12,
  },
});
