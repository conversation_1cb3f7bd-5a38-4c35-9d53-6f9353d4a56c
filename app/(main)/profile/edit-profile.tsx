import React, { useState, useEffect } from 'react';
import {
  StyleSheet,
  View,
  ScrollView,
  TouchableOpacity,
  ActivityIndicator,
  Alert,
  SafeAreaView,
} from 'react-native';
import { Text } from 'react-native-paper';
import { useRouter } from 'expo-router';
import { Ionicons, MaterialIcons } from '@expo/vector-icons';
import { useAuth } from '../../../contexts/AuthContext';
import { useTranslation } from 'react-i18next';
import { doc, getDoc, updateDoc } from 'firebase/firestore';
import { db } from '../../../services/firebase';
import AsyncStorage from '@react-native-async-storage/async-storage';

// Define types for onboarding preferences
type TravelCompanion = 'solo' | 'couple' | 'friends' | 'family';
type Interest = 'history' | 'art' | 'gastronomy' | 'fashion' | 'nature' | 'sports' | 'sightseeing' | 'nightlife' | 'relaxation';
type ActivityLevel = 'relax' | 'medium' | 'active';
type TravelChallenge = 'tooManyOptions' | 'prioritization' | 'touristTraps' | 'transportation' | 'valueForMoney';
type TravelFrequency = 'weekly' | 'monthly' | 'quarterly' | 'yearly';
type TravelDuration = 'weekend' | 'week' | 'twoWeeks' | 'longer';
type TravelStyle = 'luxury' | 'comfort' | 'budget' | 'backpacking';

interface UserPreferences {
  travelCompanion?: TravelCompanion;
  interests?: Interest[];
  budget?: string;
  destinations?: string[];
  activityLevel?: ActivityLevel;
  travelChallenges?: TravelChallenge[];
  travelFrequency?: TravelFrequency;
  travelDuration?: TravelDuration;
  travelStyle?: TravelStyle;
}

export default function EditProfileScreen() {
  const router = useRouter();
  const { user } = useAuth();
  const { t } = useTranslation();
  
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [userPreferences, setUserPreferences] = useState<UserPreferences>({});

  useEffect(() => {
    if (user) {
      fetchUserPreferences();
    } else {
      setLoading(false);
    }
  }, [user]);

  const fetchUserPreferences = async () => {
    if (!user) return;
    
    setLoading(true);
    try {
      const userDocRef = doc(db, 'users', user.uid);
      const userDoc = await getDoc(userDocRef);
      
      if (userDoc.exists()) {
        const userData = userDoc.data();
        
        // Extract onboarding preferences from user data
        const preferences: UserPreferences = {
          travelCompanion: userData.travelCompanion,
          interests: userData.interests,
          budget: userData.budget,
          destinations: userData.destinations,
          activityLevel: userData.activityLevel,
          travelChallenges: userData.travelChallenges,
          travelFrequency: userData.travelFrequency,
          travelDuration: userData.travelDuration,
          travelStyle: userData.travelStyle,
        };
        
        setUserPreferences(preferences);
      }
    } catch (error) {
      console.error('Error fetching user preferences:', error);
      Alert.alert('Error', 'Failed to load your preferences. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const saveUserPreferences = async () => {
    if (!user) return;
    
    setSaving(true);
    try {
      const userDocRef = doc(db, 'users', user.uid);
      
      // Update user document with preferences
      await updateDoc(userDocRef, {
        ...userPreferences,
        updatedAt: new Date().toISOString()
      });
      
      Alert.alert('Success', 'Your preferences have been updated successfully.');
      router.back();
    } catch (error) {
      console.error('Error saving user preferences:', error);
      Alert.alert('Error', 'Failed to save your preferences. Please try again.');
    } finally {
      setSaving(false);
    }
  };

  const navigateToEditSection = async (section: string) => {
    try {
      // Set flag to indicate we're in edit mode
      await AsyncStorage.setItem('editProfileMode', 'true');
      router.push(`/onboarding/${section}`);
    } catch (error) {
      console.error('Error setting edit mode:', error);
      Alert.alert('Error', 'Failed to navigate to edit screen. Please try again.');
    }
  };

  if (loading) {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="large" color="#007AFF" />
        <Text style={styles.loadingText}>Loading your preferences...</Text>
      </View>
    );
  }

  return (
    <SafeAreaView style={styles.container}>
      <View style={styles.header}>
        <TouchableOpacity onPress={() => router.back()} style={styles.backButton}>
          <Ionicons name="arrow-back" size={24} color="#000" />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>Edit Profile</Text>
        <TouchableOpacity onPress={saveUserPreferences} style={styles.saveButton} disabled={saving}>
          {saving ? (
            <ActivityIndicator size="small" color="#FFFFFF" />
          ) : (
            <Text style={styles.saveButtonText}>Save</Text>
          )}
        </TouchableOpacity>
      </View>

      <ScrollView style={styles.scrollView} contentContainerStyle={styles.scrollContent}>
        <Text style={styles.sectionTitle}>Travel Preferences</Text>
        
        {/* Travel Companions */}
        <TouchableOpacity 
          style={styles.preferenceTile}
          onPress={() => navigateToEditSection('travel-companions')}
        >
          <View style={styles.preferenceIconContainer}>
            <MaterialIcons name="people" size={24} color="#4285F4" />
          </View>
          <View style={styles.preferenceContent}>
            <Text style={styles.preferenceTitle}>Travel Companions</Text>
            <Text style={styles.preferenceValue}>
              {userPreferences.travelCompanion ? 
                userPreferences.travelCompanion.charAt(0).toUpperCase() + userPreferences.travelCompanion.slice(1) : 
                'Not set'}
            </Text>
          </View>
          <Ionicons name="chevron-forward" size={20} color="#999" />
        </TouchableOpacity>
        
        {/* Interests */}
        <TouchableOpacity 
          style={styles.preferenceTile}
          onPress={() => navigateToEditSection('interests')}
        >
          <View style={styles.preferenceIconContainer}>
            <MaterialIcons name="interests" size={24} color="#EA4335" />
          </View>
          <View style={styles.preferenceContent}>
            <Text style={styles.preferenceTitle}>Interests</Text>
            <Text style={styles.preferenceValue}>
              {userPreferences.interests && userPreferences.interests.length > 0 ? 
                `${userPreferences.interests.length} selected` : 
                'Not set'}
            </Text>
          </View>
          <Ionicons name="chevron-forward" size={20} color="#999" />
        </TouchableOpacity>
        
        {/* Budget */}
        <TouchableOpacity 
          style={styles.preferenceTile}
          onPress={() => navigateToEditSection('budget')}
        >
          <View style={styles.preferenceIconContainer}>
            <MaterialIcons name="account-balance-wallet" size={24} color="#FBBC05" />
          </View>
          <View style={styles.preferenceContent}>
            <Text style={styles.preferenceTitle}>Budget</Text>
            <Text style={styles.preferenceValue}>
              {userPreferences.budget || 'Not set'}
            </Text>
          </View>
          <Ionicons name="chevron-forward" size={20} color="#999" />
        </TouchableOpacity>
        
        {/* Destinations */}
        <TouchableOpacity 
          style={styles.preferenceTile}
          onPress={() => navigateToEditSection('destinations')}
        >
          <View style={styles.preferenceIconContainer}>
            <MaterialIcons name="place" size={24} color="#34A853" />
          </View>
          <View style={styles.preferenceContent}>
            <Text style={styles.preferenceTitle}>Destinations</Text>
            <Text style={styles.preferenceValue}>
              {userPreferences.destinations && userPreferences.destinations.length > 0 ? 
                `${userPreferences.destinations.length} selected` : 
                'Not set'}
            </Text>
          </View>
          <Ionicons name="chevron-forward" size={20} color="#999" />
        </TouchableOpacity>
        
        {/* Activity Level */}
        <TouchableOpacity 
          style={styles.preferenceTile}
          onPress={() => navigateToEditSection('activity-level')}
        >
          <View style={styles.preferenceIconContainer}>
            <MaterialIcons name="directions-walk" size={24} color="#4285F4" />
          </View>
          <View style={styles.preferenceContent}>
            <Text style={styles.preferenceTitle}>Activity Level</Text>
            <Text style={styles.preferenceValue}>
              {userPreferences.activityLevel ? 
                userPreferences.activityLevel.charAt(0).toUpperCase() + userPreferences.activityLevel.slice(1) : 
                'Not set'}
            </Text>
          </View>
          <Ionicons name="chevron-forward" size={20} color="#999" />
        </TouchableOpacity>
        
        {/* Travel Challenges */}
        <TouchableOpacity 
          style={styles.preferenceTile}
          onPress={() => navigateToEditSection('travel-challenges')}
        >
          <View style={styles.preferenceIconContainer}>
            <MaterialIcons name="warning" size={24} color="#EA4335" />
          </View>
          <View style={styles.preferenceContent}>
            <Text style={styles.preferenceTitle}>Travel Challenges</Text>
            <Text style={styles.preferenceValue}>
              {userPreferences.travelChallenges && userPreferences.travelChallenges.length > 0 ? 
                `${userPreferences.travelChallenges.length} selected` : 
                'Not set'}
            </Text>
          </View>
          <Ionicons name="chevron-forward" size={20} color="#999" />
        </TouchableOpacity>
        
        {/* Travel Preferences (Frequency, Duration, Style) */}
        <TouchableOpacity 
          style={styles.preferenceTile}
          onPress={() => navigateToEditSection('travel-preferences')}
        >
          <View style={styles.preferenceIconContainer}>
            <MaterialIcons name="settings" size={24} color="#FBBC05" />
          </View>
          <View style={styles.preferenceContent}>
            <Text style={styles.preferenceTitle}>Travel Preferences</Text>
            <Text style={styles.preferenceValue}>
              {userPreferences.travelFrequency || userPreferences.travelDuration || userPreferences.travelStyle ? 
                'Set' : 'Not set'}
            </Text>
          </View>
          <Ionicons name="chevron-forward" size={20} color="#999" />
        </TouchableOpacity>
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#FFFFFF',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#EEEEEE',
  },
  backButton: {
    padding: 8,
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: 'bold',
  },
  saveButton: {
    backgroundColor: '#007AFF',
    paddingVertical: 8,
    paddingHorizontal: 16,
    borderRadius: 20,
  },
  saveButtonText: {
    color: '#FFFFFF',
    fontWeight: '600',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    marginTop: 12,
    fontSize: 16,
    color: '#555555',
  },
  scrollView: {
    flex: 1,
  },
  scrollContent: {
    padding: 20,
  },
  sectionTitle: {
    fontSize: 22,
    fontWeight: 'bold',
    marginBottom: 20,
  },
  preferenceTile: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#FFFFFF',
    borderRadius: 12,
    padding: 16,
    marginBottom: 12,
    shadowColor: '#000000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  preferenceIconContainer: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: '#F5F5F5',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 16,
  },
  preferenceContent: {
    flex: 1,
  },
  preferenceTitle: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 4,
  },
  preferenceValue: {
    fontSize: 14,
    color: '#666666',
  },
});
