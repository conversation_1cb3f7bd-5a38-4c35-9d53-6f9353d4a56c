import React, { useState, useEffect } from 'react';
import { StyleSheet, View, ScrollView, Image, TouchableOpacity, ImageBackground, ActivityIndicator, Alert } from 'react-native';
import { Text } from 'react-native-paper';
import { SafeAreaView } from 'react-native-safe-area-context';
import { StatusBar } from 'expo-status-bar';
import { useRouter } from 'expo-router';
import { Ionicons, Feather, MaterialCommunityIcons } from '@expo/vector-icons';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { useAuth } from '../../../contexts/AuthContext';
import { useTranslation } from 'react-i18next';
// Import Firebase services
import { getFirestore, collection, query, where, getDocs, doc, getDoc } from 'firebase/firestore';
import { useTutorial } from '../../../contexts/TutorialContext';
// Import Superwall SDK
import { Superwall } from 'expo-superwall';

// Import assets with relative paths
import profileAvatar from '../../../assets/profile/profile-avatar.png';

// Import custom components
import CompletedTripsCard from '../../../components/profile/CompletedTripsCard';
import LanguageSelector from '../../../src/components/LanguageSelector';

// Firebase premium mascot image URL
const PREMIUM_MASCOT_URL = "https://firebasestorage.googleapis.com/v0/b/traveling-app-c19a8.firebasestorage.app/o/Profile_page_pics%2Fpremium_guy.png?alt=media&token=7b0b96b4-53ca-4316-b5ba-813cb1a80467";

export default function ProfileScreen() {
  const insets = useSafeAreaInsets();
  const { user } = useAuth();
  const { t } = useTranslation();
  const { resetTutorial } = useTutorial();

  const { logout: authLogout } = useAuth();
  const router = useRouter();
  
  // Logout function that uses Firebase authentication
  const logout = async () => {
    try {
      await authLogout();
      console.log('User logged out successfully');
      router.replace('/(auth)/login');
    } catch (error) {
      console.error('Error logging out:', error);
      Alert.alert('Logout Error', 'Failed to log out. Please try again.');
    }
  };

  // Restart tutorial function
  const handleRestartTutorial = () => {
    Alert.alert(
      'Restart Tutorial',
      'This will restart the app tutorial from the beginning. You will be taken to the dashboard.',
      [
        {
          text: 'Cancel',
          style: 'cancel',
        },
        {
          text: 'Restart',
          style: 'default',
          onPress: async () => {
            await resetTutorial();
            router.push('/(main)/dashboard');
          },
        },
      ]
    );
  };
  const [userProfile, setUserProfile] = useState<any>(null);
  const [loading, setLoading] = useState(true);

  // Fetch user profile data from Firebase
  useEffect(() => {
    const fetchUserProfile = async () => {
      if (!user) {
        setLoading(false);
        return;
      }
      
      try {
        // Get Firestore instance
        const db = getFirestore();
        
        // Directly get the user document using their UID (most efficient approach)
        console.log('Fetching user document by UID:', user.uid);
        const userDoc = await getDoc(doc(db, 'users', user.uid));

        if (userDoc.exists()) {
          const userData = userDoc.data();
          console.log('Found user document by UID:', {
            username: userData.username || 'not set',
            displayName: userData.displayName || 'not set',
            email: userData.email || 'not set'
          });

          setUserProfile(userData);
          console.log('Profile - Fetched user data from Firebase by UID');
        } else {
          // No user document found, create default profile
          console.log('No user document found in Firestore, creating default profile');
          const defaultProfile = {
            uid: user.uid,
            email: user.email,
            displayName: user.displayName || user.email?.split('@')[0],
            photoURL: user.photoURL,
            premium: false,
            joinDate: new Date().toISOString(),
            tripsCount: 0,
            countriesVisited: 0
          };
          setUserProfile(defaultProfile);
          console.log('Profile - Created default user profile');
        }
      } catch (error) {
        console.error('Error fetching user profile:', error);
      } finally {
        setLoading(false);
      }
    };
    
    fetchUserProfile();
  }, [user]);

  // Get display name based on user profile data
  const getDisplayName = () => {
    if (!user) return 'Guest';
    
    // First prioritize the username from the user profile in Firestore
    if (userProfile && userProfile.username) {
      return userProfile.username;
    }
    
    // Next try displayName from Firebase Auth or profile
    if (userProfile?.displayName || user.displayName) {
      return userProfile?.displayName || user.displayName;
    }
    
    // Fallback to email only as last resort
    return user.email?.split('@')[0] || 'Traveler';
  };

  // Get user email
  const getUserEmail = () => {
    if (!user) return 'Not signed in';
    return userProfile?.email || user.email || 'No email available';
  };

  // Completed trips counter
  const [completedTripsCount, setCompletedTripsCount] = useState(0);
  
  // Fetch completed trips count when user profile changes
  useEffect(() => {
    const fetchCompletedTripsCount = async () => {
      if (!user) return;

      try {
        // Get Firestore instance
        const db = getFirestore();

        // Query both collections for completed trips
        // 1. Regular trips collection
        const regularTripsRef = collection(db, 'tripPlans', user.uid, 'plans');
        const regularQuery = query(regularTripsRef, where('status', '==', 'completed'));
        const regularSnapshot = await getDocs(regularQuery);

        // 2. Specific attractions plans collection
        const specificTripsRef = collection(db, 'specificAttractionPlans', user.uid, 'plans');
        const specificQuery = query(specificTripsRef, where('status', '==', 'completed'));
        const specificSnapshot = await getDocs(specificQuery);

        // Set the total count of completed trips from both collections
        const totalCount = regularSnapshot.size + specificSnapshot.size;
        console.log(`Completed trips count: ${regularSnapshot.size} regular + ${specificSnapshot.size} specific attractions = ${totalCount} total`);
        setCompletedTripsCount(totalCount);
      } catch (error) {
        console.error('Error fetching completed trips count:', error);
        setCompletedTripsCount(0);
      }
    };

    fetchCompletedTripsCount();
  }, [user]);

  // Subscription data
  const [subscription, setSubscription] = useState({
    plan: 'free',
    features: [
      'basicTripStorage',
      'standardSupport'
    ]
  });
  
  // Update subscription when user profile changes
  useEffect(() => {
    if (userProfile) {
      if (userProfile.premium) {
        setSubscription({
          plan: 'premium',
          features: [
            'unlimitedTripStorage',
            'exclusiveTravelDeals',
            'prioritySupport',
            'advancedAiPlanning'
          ]
        });
      } else {
        setSubscription({
          plan: 'free',
          features: [
            'basicTripStorage',
            'standardSupport'
          ]
        });
      }
    }
  }, [userProfile]);

  // Handle sign out
  const handleSignOut = () => {
    Alert.alert(
      t('profile.signOut'),
      t('profile.signOutConfirmation'),
      [
        {
          text: t('common.cancel'),
          style: "cancel"
        },
        {
          text: t('profile.signOut'),
          onPress: async () => {
            try {
              await logout();
            } catch (error) {
              console.error("Error signing out:", error);
              Alert.alert("Error", "Failed to sign out. Please try again.");
            }
          },
          style: "destructive"
        }
      ]
    );
  };

  // Show Superwall paywall
  const showPaywall = async () => {
    try {
      await Superwall.shared().presentPaywall('tripzy_premium');
    } catch (error) {
      console.warn('Failed to show paywall:', error);
    }
  };

  if (loading) {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="large" color="#007AFF" />
        <Text style={styles.loadingText}>{t('profile.loadingProfile')}</Text>
      </View>
    );
  }

  return (
    <SafeAreaView style={styles.container}>
      <StatusBar style="auto" />
      
      <ScrollView
        style={styles.scrollView}
        contentContainerStyle={[
          styles.scrollContent,
          { paddingBottom: insets.bottom + 20 }
        ]}
      >
        {/* App Title */}
        <Text style={styles.appTitle}>Tripzy</Text>
        
        {/* User Info */}
        <Text style={styles.userName}>{getDisplayName()}</Text>
        <Text style={styles.userEmail}>{getUserEmail()}</Text>
        
        {/* Completed Trips Card */}
        <TouchableOpacity style={styles.card} onPress={() => router.push('/(main)/completed-trips')}>
          <View style={styles.cardHeader}>
            <Text style={[styles.cardTitle, styles.noBottomMargin]}>{t('profile.completedTrips')}</Text>
            <Text style={styles.tripsCount}>{completedTripsCount} {t('profile.trips')}</Text>
          </View>
          <Text style={styles.cardDescription}>{t('profile.viewPastAdventures')}</Text>
        </TouchableOpacity>
        
        {/* Subscription */}
        <View style={styles.card}>
          <View style={styles.subscriptionHeader}>
            <View>
              <Text style={styles.subscriptionTitle}>{t('profile.subscription')}</Text>
              <Text style={styles.planLabel}>{t('profile.plan')}: <Text style={styles.planValue}>{t(`profile.${subscription.plan}`)}</Text></Text>
            </View>
            <Image 
              source={{ uri: PREMIUM_MASCOT_URL }}
              style={styles.mascotImage}
              resizeMode="contain"
            />
          </View>
          
          <TouchableOpacity style={styles.upgradeButton} onPress={showPaywall}>
            <Text style={styles.upgradeButtonText}>{t('profile.upgradeToPremium')}</Text>
          </TouchableOpacity>
          
          <View style={styles.featuresContainer}>
            {subscription.features.map((feature, index) => (
              <View key={index} style={styles.featureRow}>
                <Ionicons name="checkmark" size={22} color="#007AFF" />
                <Text style={styles.featureText}>{t(`profile.${feature}`)}</Text>
              </View>
            ))}
          </View>
        </View>
        
        {/* Settings */}
        <View style={styles.card}>
          <Text style={styles.cardTitle}>{t('profile.settings')}</Text>
          

          
          <View style={styles.settingRow}>
            <View style={styles.settingIconContainer}>
              <Ionicons name="globe-outline" size={22} color="#007AFF" />
            </View>
            <Text style={styles.settingText}>{t('profile.languagePreferences')}</Text>
            <View style={styles.languageSelectorContainer}>
              <LanguageSelector />
            </View>
          </View>
          
          {/* Notifications and Privacy Settings options removed */}

          <TouchableOpacity style={styles.settingRow} onPress={handleRestartTutorial}>
            <View style={styles.settingIconContainer}>
              <Ionicons name="help-circle-outline" size={22} color="#3A7BF8" />
            </View>
            <Text style={styles.settingText}>Restart Tutorial</Text>
          </TouchableOpacity>

          <TouchableOpacity style={[styles.settingRow, styles.lastSettingRow]} onPress={logout}>
            <View style={styles.settingIconContainer}>
              <Ionicons name="log-out-outline" size={22} color="#FF3B30" />
            </View>
            <Text style={[styles.settingText, styles.signOutText]}>{t('profile.signOut')}</Text>
          </TouchableOpacity>
        </View>
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  languageSelectorContainer: {
    flex: 1,
    alignItems: 'flex-end',
  },
  container: {
    flex: 1,
    backgroundColor: '#FFFFFF',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    marginTop: 12,
    fontSize: 16,
    color: '#555555',
  },
  scrollView: {
    flex: 1,
  },
  scrollContent: {
    padding: 20,
  },
  appTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#001A41',
    textAlign: 'center',
    marginBottom: 20,
  },
  userName: {
    fontSize: 28,
    fontWeight: 'bold',
    color: '#000000',
    textAlign: 'left',
  },
  userEmail: {
    fontSize: 18,
    color: '#666666',
    marginBottom: 30,
  },
  card: {
    backgroundColor: '#FFFFFF',
    borderRadius: 12,
    padding: 20,
    marginBottom: 20,
    shadowColor: '#000000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.05,
    shadowRadius: 4,
    elevation: 2,
  },
  cardHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 15,
  },
  cardTitle: {
    fontSize: 22,
    fontWeight: 'bold',
    color: '#000000',
    marginBottom: 15,
  },
  noBottomMargin: {
    marginBottom: 0,
  },
  cardDescription: {
    fontSize: 16,
    color: '#666666',
    marginTop: 8,
  },
  tripsCount: {
    fontSize: 18,
    fontWeight: '500',
    color: '#666666',
  },

  subscriptionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: 15,
  },
  subscriptionTitle: {
    fontSize: 32,
    fontWeight: 'bold',
    color: '#000000',
    marginBottom: 5,
  },
  planLabel: {
    fontSize: 24,
    color: '#333333',
    fontWeight: '400',
  },
  planValue: {
    fontWeight: 'bold',
  },
  mascotImage: {
    width: 100,
    height: 100,
    marginTop: -10,
    marginRight: -5,
  },
  upgradeButton: {
    backgroundColor: '#007AFF',
    borderRadius: 25,
    paddingVertical: 15,
    alignItems: 'center',
    marginBottom: 15,
  },
  upgradeButtonText: {
    color: '#FFFFFF',
    fontSize: 22,
    fontWeight: '600',
  },
  featuresContainer: {
    backgroundColor: '#F5F8FF',
    borderRadius: 15,
    padding: 15,
  },
  featureRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginVertical: 8,
  },
  featureText: {
    fontSize: 18,
    color: '#333333',
    marginLeft: 10,
    fontWeight: '500',
  },
  settingRow: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 15,
    borderBottomWidth: 1,
    borderBottomColor: '#F0F0F0',
  },
  settingText: {
    fontSize: 16,
    color: '#333333',
    marginLeft: 15,
    flex: 1,
  },
  chevron: {
    marginLeft: 10,
  },
  settingIconContainer: {
    width: 40,
    height: 40,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 10,
  },
  lastSettingRow: {
    borderBottomWidth: 0,
  },
  signOutText: {
    color: '#FF3B30',
    fontWeight: '500',
  },
});