import React, { useState, useEffect, useRef, useCallback } from 'react';
import {
  StyleSheet,
  View,
  Text,
  TouchableOpacity,
  ActivityIndicator,
  ScrollView,
  Platform,
  SafeAreaView,
  Alert
} from 'react-native';
import { useTranslation } from 'react-i18next';
import MapView, { PROVIDER_GOOGLE, Region } from 'react-native-maps';
import { StatusBar } from 'expo-status-bar';
import { MaterialIcons } from '@expo/vector-icons';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import * as Location from 'expo-location';
import { useLocalSearchParams } from 'expo-router';
import { useAuth } from '../../../contexts/AuthContext';
import { collection, doc, getDoc, getDocs, query, where } from 'firebase/firestore';
import { db } from '../../../services/firebase/firebase-core';
import { getAllMapMarkers } from '../../../services/firebase/mapMarkers';
import { SavedPlace, PlaceCategory } from '../../../types/SavedPlace';
import SavedPlaceMarker from '../../../components/map/SavedPlaceMarker';
import PlaceDetailCard from '../../../components/map/PlaceDetailCard';
import { convertToSavedPlace } from '../../../utils/convertToSavedPlace';

// Default region (centered on Europe)
const DEFAULT_REGION: Region = {
  latitude: 48.8566,
  longitude: 2.3522,
  latitudeDelta: 10,
  longitudeDelta: 10,
};

// Category filters for the map - labels will be translated at render time
const CATEGORIES: { id: PlaceCategory | 'all', label: string }[] = [
  { id: 'all', label: 'allPlaces' },
  { id: 'restaurant', label: 'food' },
  { id: 'attraction', label: 'attractions' },
];

export default function MapScreen() {
  const { user } = useAuth();
  const params = useLocalSearchParams();
  const { tripId } = params;
  const insets = useSafeAreaInsets();
  const mapRef = useRef<MapView>(null);
  const { t } = useTranslation();
  
  // State
  const [places, setPlaces] = useState<SavedPlace[]>([]);
  const [filteredPlaces, setFilteredPlaces] = useState<SavedPlace[]>([]);
  const [selectedPlace, setSelectedPlace] = useState<SavedPlace | null>(null);
  const [selectedCategory, setSelectedCategory] = useState<PlaceCategory | 'all'>('all');
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [userLocation, setUserLocation] = useState<{ latitude: number, longitude: number } | null>(null);
  const [initialRegion, setInitialRegion] = useState<Region>(DEFAULT_REGION);
  const [trips, setTrips] = useState<{ id: string, name: string }[]>([]);
  const [selectedTripId, setSelectedTripId] = useState<string | null>(null);
  const [cities, setCities] = useState<string[]>([]);
  const [selectedCity, setSelectedCity] = useState<string>('all');

  // Request location permission and get user's location
  useEffect(() => {
    (async () => {
      try {
        const { status } = await Location.requestForegroundPermissionsAsync();
        if (status === 'granted') {
          const location = await Location.getCurrentPositionAsync({});
          const { latitude, longitude } = location.coords;
          setUserLocation({ latitude, longitude });
          setInitialRegion({
            latitude,
            longitude,
            latitudeDelta: 0.05,
            longitudeDelta: 0.05,
          });
        }
      } catch (err) {
        console.log('Error getting location:', err);
        // Continue with default region if location fails
      }
    })();
  }, []);

  // Load user's trips and extract unique cities
  useEffect(() => {
    if (!user) return;

    const loadTrips = async () => {
      try {
        // Get today's date in YYYY-MM-DD format for comparison
        const today = new Date().toISOString().split('T')[0];

        // Load trips from both collections
        const regularTripsRef = collection(db, 'tripPlans', user.uid, 'plans');
        const specificTripsRef = collection(db, 'specificAttractionPlans', user.uid, 'plans');

        const [regularSnapshot, specificSnapshot] = await Promise.all([
          getDocs(regularTripsRef),
          getDocs(specificTripsRef)
        ]);

        // Process regular trips
        const regularTrips = regularSnapshot.docs
          .filter(doc => {
            const tripData = doc.data();
            // Filter out completed trips
            if (tripData.status === 'completed') return false;

            // Filter out past trips (only show upcoming trips)
            const startDate = tripData.startDate || '';
            return startDate >= today; // Only include trips with start dates in the future
          })
          .map(doc => ({
            id: doc.id,
            name: doc.data().destination || 'Unnamed Trip'
          }));

        // Process specific attraction trips
        const specificTrips = specificSnapshot.docs
          .filter(doc => {
            const tripData = doc.data();
            // Filter out completed trips
            if (tripData.status === 'completed') return false;

            // Convert travel_dates to check if trip is upcoming
            let startDate = '';
            if (tripData.travel_dates) {
              if (tripData.travel_dates.start_date) {
                startDate = tripData.travel_dates.start_date;
              } else if (tripData.travel_dates.number_of_days) {
                // For number of days, use today as start date
                startDate = today;
              }
            }

            return startDate >= today; // Only include trips with start dates in the future
          })
          .map(doc => ({
            id: doc.id,
            name: doc.data().destination || 'Unnamed Trip'
          }));

        // Combine both trip types
        const allTrips = [...regularTrips, ...specificTrips];
        setTrips(allTrips);

        // Extract unique city names from trips
        const uniqueCities = Array.from(new Set(
          allTrips
            .map(trip => trip.name)
            .filter(name => name !== 'Unnamed Trip')
        ));

        console.log(`Loaded trips for map: ${regularTrips.length} regular + ${specificTrips.length} specific = ${allTrips.length} total`);
        console.log('Unique cities with itineraries:', uniqueCities);
        setCities(uniqueCities);

        // If tripId is provided in params, use it
        if (tripId) {
          setSelectedTripId(tripId as string);
        }
        // Otherwise use the first trip if available
        else if (allTrips.length > 0) {
          setSelectedTripId(allTrips[0].id);
        }
      } catch (err) {
        console.error('Error loading trips:', err);
        setError('Failed to load trips');
      }
    };
    
    loadTrips();
  }, [user, tripId]);

  // Load saved places when selectedTripId changes
  useEffect(() => {
    if (!user || !selectedTripId) {
      setLoading(false);
      return;
    }

    const loadSavedPlaces = async () => {
      try {
        setLoading(true);
        setError(null);
        
        // Get all map markers for this user
        const allMapMarkers = await getAllMapMarkers(user.uid);
        console.log(`Found ${allMapMarkers.length} saved places across all trips`);
        
        if (allMapMarkers.length === 0) {
          console.log('No saved places found for user');
          setPlaces([]);
          setFilteredPlaces([]);
          setLoading(false);
          return;
        }
        
        // Get the list of active trip IDs (non-completed trips) from both collections
        const regularTripsRef = collection(db, 'tripPlans', user.uid, 'plans');
        const specificTripsRef = collection(db, 'specificAttractionPlans', user.uid, 'plans');

        const [regularSnapshot, specificSnapshot] = await Promise.all([
          getDocs(regularTripsRef),
          getDocs(specificTripsRef)
        ]);

        // Get today's date in YYYY-MM-DD format for comparison
        const today = new Date().toISOString().split('T')[0];

        // Filter regular trips to get only active trip IDs
        const activeRegularTrips = regularSnapshot.docs
          .filter(doc => {
            const tripData = doc.data();
            // Filter out completed trips
            if (tripData.status === 'completed') return false;

            // Filter out past trips (only show upcoming trips)
            const startDate = tripData.startDate || '';
            return startDate >= today; // Only include trips with start dates in the future
          })
          .map(doc => doc.id);

        // Filter specific attraction trips to get only active trip IDs
        const activeSpecificTrips = specificSnapshot.docs
          .filter(doc => {
            const tripData = doc.data();
            // Filter out completed trips
            if (tripData.status === 'completed') return false;

            // Convert travel_dates to check if trip is upcoming
            let startDate = '';
            if (tripData.travel_dates) {
              if (tripData.travel_dates.start_date) {
                startDate = tripData.travel_dates.start_date;
              } else if (tripData.travel_dates.number_of_days) {
                // For number of days, use today as start date
                startDate = today;
              }
            }

            return startDate >= today; // Only include trips with start dates in the future
          })
          .map(doc => doc.id);

        // Combine both trip types
        const activeTrips = [...activeRegularTrips, ...activeSpecificTrips];
        
        console.log(`Found ${activeTrips.length} active trips`);
        
        // Filter markers to only include those from active trips
        const filteredMarkers = allMapMarkers.filter(marker => 
          activeTrips.includes(marker.planId)
        );
        
        console.log(`Showing ${filteredMarkers.length} places from active trips only`);
        
        // Convert map markers to SavedPlace objects
        const savedPlaces = filteredMarkers
          .map(marker => 
            convertToSavedPlace({
              id: marker.id,
              title: marker.title,
              description: marker.description,
              location: marker.location,
              type: marker.type,
              savedAt: marker.savedAt,
              tripId: marker.planId
            }, marker.planId)
          )
          .filter(Boolean) as SavedPlace[];
        
        console.log(`Successfully converted ${savedPlaces.length} places`);
        
        setPlaces(savedPlaces);
        
        // Apply current category filter
        if (selectedCategory === 'all') {
          setFilteredPlaces(savedPlaces);
        } else {
          setFilteredPlaces(savedPlaces.filter(place => place.category === selectedCategory));
        }
        
        // Fit map to show all places
        if (savedPlaces.length > 0) {
          fitMapToPlaces(savedPlaces);
        }
      } catch (err) {
        console.error('Error loading saved places:', err);
        setError('Failed to load saved places');
      } finally {
        setLoading(false);
      }
    };
    
    loadSavedPlaces();
  }, [user, selectedTripId]);

  // Filter places by category and city
  useEffect(() => {
    let filtered = [...places];
    
    // Filter by city if not 'all'
    if (selectedCity !== 'all') {
      filtered = filtered.filter(place => {
        // Extract the city from the trip name if available
        const trip = trips.find(t => t.id === place.tripId);
        return trip && trip.name === selectedCity;
      });
    }
    
    // Then filter by category if not 'all'
    if (selectedCategory !== 'all') {
      filtered = filtered.filter(place => {
        if (selectedCategory === 'restaurant') {
          // For Food category, include restaurant category
          return place.category === 'restaurant';
        } else if (selectedCategory === 'attraction') {
          // For Attractions, include attraction category
          return place.category === 'attraction';
        }
        return place.category === selectedCategory;
      });
    }
    
    // Just update the filtered places without changing the map zoom
    setFilteredPlaces(filtered);
  }, [selectedCategory, selectedCity, places, trips]);

  // Fit map to show all places
  const fitMapToPlaces = useCallback((placesToFit: SavedPlace[]) => {
    if (!mapRef.current || placesToFit.length === 0) return;
    
    // Extract coordinates from places
    const coordinates = placesToFit.map(place => ({
      latitude: place.latitude,
      longitude: place.longitude,
    }));
    
    // Add user location if available
    if (userLocation) {
      coordinates.push(userLocation);
    }
    
    // Fit map to coordinates with padding
    setTimeout(() => {
      mapRef.current?.fitToCoordinates(coordinates, {
        edgePadding: { 
          top: 100, 
          right: 50, 
          bottom: 200, 
          left: 50 
        },
        animated: true,
      });
    }, 500);
  }, [userLocation]);

  // Handle place selection
  const handlePlacePress = useCallback((place: SavedPlace) => {
    setSelectedPlace(place);
    
    // Center map on selected place
    mapRef.current?.animateToRegion({
      latitude: place.latitude,
      longitude: place.longitude,
      latitudeDelta: 0.01,
      longitudeDelta: 0.01,
    });
  }, []);
  
  // Handle city selection
  const handleCitySelect = useCallback((city: string) => {
    setSelectedCity(city);
    setSelectedPlace(null);
    console.log(`Selected city: ${city}`);
    
    // If 'All Cities' is selected, don't zoom
    if (city === 'all') {
      return;
    }
    
    // Get all places for the selected city
    const cityPlaces = places.filter(place => {
      const trip = trips.find(t => t.id === place.tripId);
      return trip && trip.name === city;
    });
    
    // If there are places in this city, zoom to fit them all
    if (cityPlaces.length > 0) {
      const coordinates = cityPlaces.map(place => ({
        latitude: place.latitude,
        longitude: place.longitude,
      }));
      
      // Zoom to fit all places in the selected city
      mapRef.current?.fitToCoordinates(coordinates, {
        edgePadding: { 
          top: 150, 
          right: 50, 
          bottom: 250, 
          left: 50 
        },
        animated: true,
      });
    }
  }, [places, trips]);
  
  // Handle category selection
  const handleCategoryPress = useCallback((category: PlaceCategory | 'all') => {
    setSelectedCategory(category);
    setSelectedPlace(null);
  }, []);
  
  // Handle trip selection
  const handleTripChange = useCallback((tripId: string) => {
    setSelectedTripId(tripId);
    setSelectedPlace(null);
  }, []);

  // Get the trip name from the selected trip ID
  const getSelectedTripName = useCallback(() => {
    const trip = trips.find(t => t.id === selectedTripId);
    return trip ? trip.name : 'All Trips';
  }, [trips, selectedTripId]);

  return (
    <SafeAreaView style={styles.container}>
      <StatusBar style="auto" />
      
      {/* Map */}
      <MapView
        ref={mapRef}
        style={styles.map}
        provider={PROVIDER_GOOGLE}
        initialRegion={initialRegion}
        showsUserLocation
        showsMyLocationButton
        showsCompass
        showsScale
        rotateEnabled
        loadingEnabled
      >
        {/* Place Markers */}
        {filteredPlaces.map((place) => (
          <SavedPlaceMarker
            key={place.id}
            place={place}
            onPress={handlePlacePress}
          />
        ))}
      </MapView>
      
      {/* App Title */}
      <View style={[styles.titleContainer, { paddingTop: insets.top }]}>
        <Text style={styles.appTitle}>Tripzy</Text>
      </View>
      
      {/* City Selector */}
      <View style={[styles.citySelectorContainer, { top: insets.top + 40 }]}>
        <ScrollView
          horizontal
          showsHorizontalScrollIndicator={false}
          contentContainerStyle={styles.citySelectorContent}
        >
          <TouchableOpacity
            key="all-cities"
            style={[
              styles.cityButton,
              selectedCity === 'all' && styles.selectedCityButton
            ]}
            onPress={() => handleCitySelect('all')}
          >
            <Text
              style={[
                styles.cityButtonText,
                selectedCity === 'all' && styles.selectedCityButtonText
              ]}
            >
              {t('map.allCities')}
            </Text>
          </TouchableOpacity>
          
          {cities.map((city) => (
            <TouchableOpacity
              key={city === 'all' ? 'all-cities' : city}
              style={[
                styles.cityButton,
                selectedCity === city && styles.selectedCityButton,
              ]}
              onPress={() => handleCitySelect(city)}
            >
              <Text
                style={[
                  styles.cityButtonText,
                  selectedCity === city && styles.selectedCityButtonText,
                ]}
              >
                {city}
              </Text>
            </TouchableOpacity>
          ))}
        </ScrollView>
      </View>
      
      {/* Trip Selector - Only show if needed for backward compatibility */}
      {false && trips.length > 1 && (
        <View style={[styles.tripSelectorContainer, { top: insets.top + 40 }]}>
          <ScrollView
            horizontal
            showsHorizontalScrollIndicator={false}
            contentContainerStyle={styles.tripSelectorContent}
          >
            {trips.map((trip) => (
              <TouchableOpacity
                key={trip.id}
                style={[
                  styles.tripButton,
                  selectedTripId === trip.id && styles.selectedTripButton
                ]}
                onPress={() => handleTripChange(trip.id)}
              >
                <Text
                  style={[
                    styles.tripButtonText,
                    selectedTripId === trip.id && styles.selectedTripButtonText
                  ]}
                >
                  {trip.name}
                </Text>
              </TouchableOpacity>
            ))}
          </ScrollView>
        </View>
      )}
      
      {/* Category Filters */}
      <View style={[styles.categoryContainer, { top: insets.top + 85 }]}>
        <View style={{ flexDirection: 'row', justifyContent: 'center', width: '100%', paddingHorizontal: 16 }}>
          {CATEGORIES.map((category) => (
            <TouchableOpacity
              key={category.id}
              style={[
                styles.categoryButton,
                selectedCategory === category.id && styles.categoryButtonActive,
              ]}
              onPress={() => handleCategoryPress(category.id)}
            >
              <Text
                style={[
                  styles.categoryText,
                  selectedCategory === category.id && styles.categoryTextActive,
                ]}
              >
                {t(`map.${category.label}`)}
              </Text>
            </TouchableOpacity>
          ))}
        </View>
      </View>
      
      {/* Loading Indicator */}
      {loading && (
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color="#3A7BF8" />
          <Text style={styles.loadingText}>Loading places...</Text>
        </View>
      )}
      
      {/* Error Message */}
      {error && (
        <View style={styles.errorContainer}>
          <Text style={styles.errorText}>{error}</Text>
        </View>
      )}
      
      {/* Empty State */}
      {!loading && filteredPlaces.length === 0 && !error && (
        <View style={styles.emptyContainer}>
          <MaterialIcons name="place" size={64} color="#ccc" />
          <Text style={styles.emptyTitle}>{t('common.noPlacesFound')}</Text>
          <Text style={styles.emptyText}>
            {selectedCategory === 'all'
              ? t('common.savePlacesFromItinerary')
              : t('common.noPlacesFoundInCategory', { category: selectedCategory })}
          </Text>
        </View>
      )}
      
      {/* Place Details */}
      {selectedPlace && (
        <View style={styles.detailsContainer}>
          <PlaceDetailCard
            place={selectedPlace}
            onClose={() => setSelectedPlace(null)}
          />
        </View>
      )}
      
      {/* Map Controls */}
      <View style={[styles.controlsContainer, { bottom: insets.bottom + 16 }]}>
        <TouchableOpacity
          style={styles.controlButton}
          onPress={() => fitMapToPlaces(filteredPlaces)}
        >
          <MaterialIcons name="fit-screen" size={24} color="#333" />
        </TouchableOpacity>
      </View>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#fff',
  },
  map: {
    ...StyleSheet.absoluteFillObject,
  },
  titleContainer: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    alignItems: 'center',
    backgroundColor: 'transparent',
    paddingVertical: 8,
    zIndex: 1,
  },
  appTitle: {
    fontSize: 28,
    fontWeight: 'bold',
    color: '#333',
    letterSpacing: 0.5,
  },
  citySelectorContainer: {
    position: 'absolute',
    top: Platform.OS === 'ios' ? 50 : 40,
    left: 0,
    right: 0,
    backgroundColor: 'transparent',
    zIndex: 1,
  },
  citySelectorContent: {
    paddingHorizontal: 16,
    flexGrow: 1,
    justifyContent: 'center',
  },
  cityButton: {
    backgroundColor: 'white',
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 20,
    marginRight: 8,
    ...Platform.select({
      ios: {
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.1,
        shadowRadius: 4,
      },
      android: {
        elevation: 2,
      },
    }),
  },
  selectedCityButton: {
    backgroundColor: '#3A7BF8',
  },
  cityButtonText: {
    color: '#333',
    fontWeight: '500',
  },
  selectedCityButtonText: {
    color: 'white',
  },
  tripSelectorContainer: {
    position: 'absolute',
    top: Platform.OS === 'ios' ? 50 : 40,
    left: 0,
    right: 0,
    backgroundColor: 'transparent',
    zIndex: 1,
  },
  tripSelectorContent: {
    paddingHorizontal: 16,
  },
  tripButton: {
    backgroundColor: 'white',
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 20,
    marginRight: 8,
    ...Platform.select({
      ios: {
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.1,
        shadowRadius: 4,
      },
      android: {
        elevation: 2,
      },
    }),
  },
  selectedTripButton: {
    backgroundColor: '#3A7BF8',
  },
  tripButtonText: {
    color: '#333',
    fontWeight: '500',
  },
  selectedTripButtonText: {
    color: 'white',
  },
  categoryContainer: {
    position: 'absolute',
    top: Platform.OS === 'ios' ? 100 : 90,
    left: 0,
    right: 0,
    backgroundColor: 'transparent',
    zIndex: 1,
    alignItems: 'center',
  },
  filterTitle: {
    fontSize: 14,
    fontWeight: '500',
    color: '#666',
    marginBottom: 8,
    marginLeft: 4,
  },
  categoryScrollContent: {
    paddingRight: 16,
  },
  categoryButton: {
    backgroundColor: 'white',
    paddingHorizontal: 16,
    paddingVertical: 10,
    borderRadius: 20,
    marginHorizontal: 5,
    ...Platform.select({
      ios: {
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.15,
        shadowRadius: 4,
      },
      android: {
        elevation: 3,
      },
    }),
  },
  categoryButtonActive: {
    backgroundColor: '#3A7BF8',
  },
  categoryText: {
    color: '#333',
    fontWeight: '500',
    fontSize: 15,
  },
  categoryTextActive: {
    color: 'white',
  },
  loadingContainer: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'rgba(255, 255, 255, 0.7)',
    zIndex: 2,
  },
  loadingText: {
    marginTop: 12,
    fontSize: 16,
    color: '#333',
  },
  errorContainer: {
    position: 'absolute',
    top: Platform.OS === 'ios' ? 190 : 180,
    left: 16,
    right: 16,
    backgroundColor: '#ffebee',
    padding: 16,
    borderRadius: 8,
    ...Platform.select({
      ios: {
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.1,
        shadowRadius: 4,
      },
      android: {
        elevation: 2,
      },
    }),
    zIndex: 2,
  },
  errorText: {
    color: '#d32f2f',
    fontSize: 14,
  },
  emptyContainer: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'rgba(255, 255, 255, 0.7)',
    zIndex: 1,
  },
  emptyTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#333',
    marginTop: 16,
  },
  emptyText: {
    fontSize: 16,
    color: '#666',
    textAlign: 'center',
    marginTop: 8,
    paddingHorizontal: 32,
  },
  detailsContainer: {
    position: 'absolute',
    bottom: 80,
    left: 0,
    right: 0,
    zIndex: 2,
  },
  controlsContainer: {
    position: 'absolute',
    right: 16,
    zIndex: 1,
  },
  controlButton: {
    width: 48,
    height: 48,
    borderRadius: 24,
    backgroundColor: 'white',
    justifyContent: 'center',
    alignItems: 'center',
    ...Platform.select({
      ios: {
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.2,
        shadowRadius: 4,
      },
      android: {
        elevation: 4,
      },
    }),
  },
});
