import React, { useState, useEffect, useRef, useCallback } from 'react';
import {
  StyleSheet,
  View,
  Text,
  TouchableOpacity,
  ActivityIndicator,
  ScrollView,
  Platform,
  Dimensions,
  Alert
} from 'react-native';
import MapView, { PROVIDER_GOOGLE, Region, Marker } from 'react-native-maps';
import { StatusBar } from 'expo-status-bar';
import { MaterialIcons } from '@expo/vector-icons';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import * as Location from 'expo-location';
import { useAuth } from '../../../contexts/AuthContext';
import { collection, getDocs, onSnapshot, query } from 'firebase/firestore';
import { db } from '../../../services/firebase/firebase-core';
import { getAllMapMarkers, removePlaceFromMap } from '../../../services/firebase/mapMarkers';
import { SavedPlace, PlaceCategory } from '../../../types/SavedPlace';
import SavedPlaceMarker from '../../../components/map/SavedPlaceMarker';
import PlaceDetailCard from '../../../components/map/PlaceDetailCard';
import { convertToSavedPlace } from '../../../utils/convertToSavedPlace';

// Default region (centered on Europe)
const DEFAULT_REGION: Region = {
  latitude: 48.8566,
  longitude: 2.3522,
  latitudeDelta: 10,
  longitudeDelta: 10,
};

// Category filters for the map - simplified as per requirements
const CATEGORIES: { id: PlaceCategory | 'all', label: string }[] = [
  { id: 'all', label: 'All Places' },
  { id: 'restaurant', label: 'Food' },
  { id: 'attraction', label: 'Attractions' },
];

export default function AllTripsMapScreen() {
  const { user } = useAuth();
  const insets = useSafeAreaInsets();
  const mapRef = useRef<MapView>(null);
  
  // State
  const [places, setPlaces] = useState<SavedPlace[]>([]);
  const [filteredPlaces, setFilteredPlaces] = useState<SavedPlace[]>([]);
  const [selectedPlace, setSelectedPlace] = useState<SavedPlace | null>(null);
  const [selectedCategory, setSelectedCategory] = useState<PlaceCategory | 'all'>('all');
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [userLocation, setUserLocation] = useState<{ latitude: number, longitude: number } | null>(null);
  const [initialRegion, setInitialRegion] = useState<Region>(DEFAULT_REGION);
  
  // City filtering state
  const [cities, setCities] = useState<string[]>([]);
  const [selectedCity, setSelectedCity] = useState<string | 'all'>('all');
  const [cityPlaces, setCityPlaces] = useState<{[city: string]: SavedPlace[]}>({});

  // Request location permission and get user's location
  useEffect(() => {
    (async () => {
      try {
        const { status } = await Location.requestForegroundPermissionsAsync();
        if (status === 'granted') {
          const location = await Location.getCurrentPositionAsync({});
          const { latitude, longitude } = location.coords;
          setUserLocation({ latitude, longitude });
          setInitialRegion({
            latitude,
            longitude,
            latitudeDelta: 0.05,
            longitudeDelta: 0.05,
          });
        }
      } catch (err) {
        console.log('Error getting location:', err);
        // Continue with default region if location fails
      }
    })();
  }, []);

  // Helper function to fit map to show all places
  const fitMapToPlaces = useCallback((placesToFit: SavedPlace[]) => {
    if (!mapRef.current || placesToFit.length === 0) return;

    const coordinates = placesToFit.map(place => ({
      latitude: place.latitude,
      longitude: place.longitude,
    }));

    mapRef.current.fitToCoordinates(coordinates, {
      edgePadding: { top: 100, right: 100, bottom: 100, left: 100 },
      animated: true,
    });
  }, []);

  // Load all saved places from all trips and organize by city
  useEffect(() => {
    if (!user) {
      setLoading(false);
      return;
    }

    console.log('Setting up real-time listener for trips for user:', user.uid);
    setLoading(true);
    setError(null);
    
    // Create a query for all trips
    const tripsRef = collection(db, 'tripPlans', user.uid, 'plans');
    const tripsQuery = query(tripsRef);
    
    // Set up real-time listener for trips
    const unsubscribe = onSnapshot(tripsQuery, async (tripsSnapshot) => {
      try {
        // Get today's date in YYYY-MM-DD format for comparison
        const today = new Date().toISOString().split('T')[0];
        
        // Get all trip data for mapping places to cities
        const tripsList = tripsSnapshot.docs.map(doc => {
          const destination = doc.data().destination || 'Unnamed Trip';
          return {
            id: doc.id,
            name: destination
          };
        });

        console.log(`Found ${tripsList.length} total trips`);

        // Get all map markers for this user across all trips
        const allMarkers = await getAllMapMarkers(user.uid);
        console.log(`Found ${allMarkers.length} total saved places`);

        if (allMarkers.length === 0) {
          setLoading(false);
          return;
        }

        // Convert to SavedPlace objects
        const savedPlaces: SavedPlace[] = [];
        const placesByCity: {[city: string]: SavedPlace[]} = {};

        // Add an 'all' category to hold all places
        placesByCity['all'] = [];

        for (const marker of allMarkers) {
          const savedPlace = convertToSavedPlace(marker);
          if (savedPlace) {
            savedPlaces.push(savedPlace);

            // Add to 'all' category
            placesByCity['all'].push(savedPlace);

            // Find which city this place belongs to based on planId
            const tripData = tripsList.find(trip => trip.id === marker.planId);
            if (tripData && tripData.name !== 'Unnamed Trip') {
              // Initialize city array if it doesn't exist
              if (!placesByCity[tripData.name]) {
                placesByCity[tripData.name] = [];
              }
              // Add the place to this city
              placesByCity[tripData.name].push(savedPlace);
            }
          }
        }

        // Extract cities that actually have saved places
        const citiesWithPlaces = Object.keys(placesByCity).filter(city =>
          city !== 'all' && placesByCity[city].length > 0
        );
        setCities(citiesWithPlaces);
        console.log(`Found ${citiesWithPlaces.length} cities with saved places`);
        
        console.log(`Converted ${savedPlaces.length} places`);
        
        // Set places and filtered places
        setPlaces(savedPlaces);
        setFilteredPlaces(savedPlaces);
        setCityPlaces(placesByCity);
        
        // Set default selected city if available
        if (citiesWithPlaces.length > 0) {
          setSelectedCity(citiesWithPlaces[0]);
          setFilteredPlaces(placesByCity[citiesWithPlaces[0]]);
        }
        
        // Fit map to show all places
        if (savedPlaces.length > 0 && mapRef.current) {
          fitMapToPlaces(savedPlaces);
        }
      } catch (error) {
        console.error('Error loading saved places:', error);
        setError('Failed to load saved places. Please try again.');
      } finally {
        setLoading(false);
      }
    });
    
    // Clean up the listener when the component unmounts
    return () => unsubscribe();
  }, [user, fitMapToPlaces]);

  // Handle city selection
  const handleCitySelect = useCallback((city: string | 'all') => {
    setSelectedCity(city);
    
    // Apply both city and category filters
    let filtered = cityPlaces[city] || [];
    
    // Apply category filter if not 'all'
    if (selectedCategory !== 'all') {
      filtered = filtered.filter(place => place.category === selectedCategory);
    }
    
    setFilteredPlaces(filtered);
    
    // Fit map to show places in this city
    if (filtered.length > 0 && mapRef.current) {
      fitMapToPlaces(filtered);
    }
  }, [selectedCategory, cityPlaces, fitMapToPlaces]);
  
  // Handle category selection
  const handleCategorySelect = useCallback((category: PlaceCategory | 'all') => {
    setSelectedCategory(category);
    
    // Apply both city and category filters
    let filtered = cityPlaces[selectedCity] || [];
    
    // Apply category filter if not 'all'
    if (category !== 'all') {
      filtered = filtered.filter(place => place.category === category);
    }
    
    setFilteredPlaces(filtered);
  }, [selectedCity, cityPlaces]);

  // Handle unsaving a place
  const handleUnsavePlace = useCallback(async (placeId: string) => {
    if (!user || !placeId) return;
    
    try {
      setLoading(true);
      
      // Find the place to unsave
      const placeToRemove = places.find(place => place.id === placeId);
      if (!placeToRemove) {
        console.error('Place not found:', placeId);
        setLoading(false);
        return;
      }
      
      // Remove from Firestore
      const success = await removePlaceFromMap(user.uid, placeId);
      
      if (success) {
        // Update local state
        const updatedPlaces = places.filter(place => place.id !== placeId);
        setPlaces(updatedPlaces);
        
        // Update filtered places
        const updatedFiltered = filteredPlaces.filter(place => place.id !== placeId);
        setFilteredPlaces(updatedFiltered);
        
        // Update city places
        const updatedCityPlaces = { ...cityPlaces };
        Object.keys(updatedCityPlaces).forEach(city => {
          updatedCityPlaces[city] = updatedCityPlaces[city].filter(place => place.id !== placeId);
        });
        setCityPlaces(updatedCityPlaces);

        // Update cities list to only include cities with places
        const citiesWithPlaces = Object.keys(updatedCityPlaces).filter(city =>
          city !== 'all' && updatedCityPlaces[city].length > 0
        );
        setCities(citiesWithPlaces);
        
        // Close the detail card
        setSelectedPlace(null);
      }
    } catch (error) {
      console.error('Error unsaving place:', error);
    } finally {
      setLoading(false);
    }
  }, [user, places, filteredPlaces, cityPlaces]);

  // Handle refresh
  const handleRefresh = useCallback(async () => {
    if (!user) return;
    
    try {
      setLoading(true);
      setError(null);
      
      // Reload places from Firestore
      const allMarkers = await getAllMapMarkers(user.uid);
      
      // Convert to SavedPlace objects
      const savedPlaces: SavedPlace[] = [];
      for (const marker of allMarkers) {
        const savedPlace = convertToSavedPlace(marker);
        if (savedPlace) {
          savedPlaces.push(savedPlace);
        }
      }
      
      console.log(`Refreshed ${savedPlaces.length} places`);
      
      // Update places and filtered places
      setPlaces(savedPlaces);
      
      // Apply current category filter
      if (selectedCategory === 'all') {
        setFilteredPlaces(savedPlaces);
      } else {
        setFilteredPlaces(savedPlaces.filter(place => place.category === selectedCategory));
      }
      
      // Fit map to show all places
      if (savedPlaces.length > 0) {
        fitMapToPlaces(savedPlaces);
      }
    } catch (error) {
      console.error('Error refreshing places:', error);
      setError('Failed to refresh places. Please try again.');
    } finally {
      setLoading(false);
    }
  }, [user, selectedCategory, fitMapToPlaces]);

  return (
    <View style={styles.container}>
      <StatusBar style="auto" />
      
      {/* Map */}
      <MapView
        ref={mapRef}
        style={styles.map}
        provider={PROVIDER_GOOGLE}
        initialRegion={initialRegion}
        showsUserLocation
        showsMyLocationButton={false}
      >
        {filteredPlaces.map((place) => (
          <SavedPlaceMarker
            key={place.id}
            place={place}
            onPress={() => setSelectedPlace(place)}
          />
        ))}
        
        {/* User location marker */}
        {userLocation && (
          <Marker
            coordinate={{
              latitude: userLocation.latitude,
              longitude: userLocation.longitude,
            }}
            title="Your Location"
            pinColor="blue"
          />
        )}
      </MapView>
      
      {/* App Title */}
      <View style={[styles.titleContainer, { paddingTop: insets.top }]}>
        <Text style={styles.appTitle}>Tripzy</Text>
      </View>
      
      {/* City Selector */}
      <View style={[styles.cityContainer, { top: insets.top + 40 }]}>
        <ScrollView
          horizontal
          showsHorizontalScrollIndicator={false}
          contentContainerStyle={styles.cityScrollContent}
        >
          <TouchableOpacity
            style={[
              styles.cityButton,
              selectedCity === 'all' && styles.selectedCityButton,
            ]}
            onPress={() => handleCitySelect('all')}
          >
            <Text
              style={[
                styles.cityButtonText,
                selectedCity === 'all' && styles.selectedCityButtonText,
              ]}
            >
              All Cities
            </Text>
          </TouchableOpacity>
          
          {cities.map((city) => (
            <TouchableOpacity
              key={city}
              style={[
                styles.cityButton,
                selectedCity === city && styles.selectedCityButton,
              ]}
              onPress={() => handleCitySelect(city)}
            >
              <Text
                style={[
                  styles.cityButtonText,
                  selectedCity === city && styles.selectedCityButtonText,
                ]}
              >
                {city}
              </Text>
            </TouchableOpacity>
          ))}
        </ScrollView>
      </View>
      
      {/* Category Filter */}
      <View style={[styles.categoryContainer, { top: insets.top + 90 }]}>
        <ScrollView
          horizontal
          showsHorizontalScrollIndicator={false}
          contentContainerStyle={styles.categoryScrollContent}
        >
          {CATEGORIES.map((category) => (
            <TouchableOpacity
              key={category.id}
              style={[
                styles.categoryButton,
                selectedCategory === category.id && styles.categoryButtonActive,
              ]}
              onPress={() => handleCategorySelect(category.id)}
            >
              <Text
                style={[
                  styles.categoryText,
                  selectedCategory === category.id && styles.categoryTextActive,
                ]}
              >
                {category.label}
              </Text>
            </TouchableOpacity>
          ))}
        </ScrollView>
      </View>
      
      {/* Loading Indicator */}
      {loading && (
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color="#3A7BF8" />
          <Text style={styles.loadingText}>Loading saved places...</Text>
        </View>
      )}
      
      {/* Error Message */}
      {error && (
        <View style={[styles.errorContainer, { top: insets.top + 140 }]}>
          <Text style={styles.errorText}>{error}</Text>
        </View>
      )}
      
      {/* Empty State */}
      {!loading && filteredPlaces.length === 0 && (
        <View style={styles.emptyContainer}>
          <MaterialIcons name="map" size={64} color="#CCCCCC" />
          <Text style={styles.emptyTitle}>No Places Saved</Text>
          <Text style={styles.emptyText}>
            Save places from your itinerary to see them on the map.
          </Text>
        </View>
      )}
      
      {/* Place Details */}
      {selectedPlace && (
        <View style={styles.detailsContainer}>
          <PlaceDetailCard
            place={selectedPlace}
            onClose={() => setSelectedPlace(null)}
            onUnsave={handleUnsavePlace}
          />
        </View>
      )}
      
      {/* Map Controls */}
      <View style={[styles.controlsContainer, { bottom: insets.bottom + 16 }]}>
        <TouchableOpacity
          style={styles.controlButton}
          onPress={() => fitMapToPlaces(filteredPlaces)}
        >
          <MaterialIcons name="fit-screen" size={24} color="#333" />
        </TouchableOpacity>
        
        <TouchableOpacity
          style={styles.controlButton}
          onPress={handleRefresh}
        >
          <MaterialIcons name="refresh" size={24} color="#333" />
        </TouchableOpacity>
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#fff',
  },
  map: {
    ...StyleSheet.absoluteFillObject,
  },
  titleContainer: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    alignItems: 'center',
    backgroundColor: 'transparent',
    paddingVertical: 8,
    zIndex: 1,
  },
  appTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#333',
    letterSpacing: 0.5,
  },
  cityContainer: {
    position: 'absolute',
    left: 0,
    right: 0,
    backgroundColor: 'transparent',
    zIndex: 1,
  },
  cityScrollContent: {
    paddingHorizontal: 16,
  },
  cityButton: {
    backgroundColor: 'white',
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 20,
    marginRight: 8,
    ...Platform.select({
      ios: {
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.1,
        shadowRadius: 4,
      },
      android: {
        elevation: 2,
      },
    }),
  },
  selectedCityButton: {
    backgroundColor: '#3A7BF8',
  },
  cityButtonText: {
    color: '#333',
    fontWeight: '500',
  },
  selectedCityButtonText: {
    color: 'white',
  },
  categoryContainer: {
    position: 'absolute',
    left: 0,
    right: 0,
    backgroundColor: 'transparent',
    zIndex: 1,
  },
  categoryScrollContent: {
    paddingHorizontal: 16,
  },
  categoryButton: {
    backgroundColor: 'white',
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 20,
    marginRight: 8,
    ...Platform.select({
      ios: {
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.1,
        shadowRadius: 4,
      },
      android: {
        elevation: 2,
      },
    }),
  },
  categoryButtonActive: {
    backgroundColor: '#3A7BF8',
  },
  categoryText: {
    color: '#333',
    fontWeight: '500',
  },
  categoryTextActive: {
    color: 'white',
  },
  loadingContainer: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'rgba(255, 255, 255, 0.7)',
    zIndex: 2,
  },
  loadingText: {
    marginTop: 12,
    fontSize: 16,
    color: '#333',
  },
  errorContainer: {
    position: 'absolute',
    left: 16,
    right: 16,
    backgroundColor: '#ffebee',
    padding: 16,
    borderRadius: 8,
    ...Platform.select({
      ios: {
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.1,
        shadowRadius: 4,
      },
      android: {
        elevation: 2,
      },
    }),
    zIndex: 2,
  },
  errorText: {
    color: '#d32f2f',
    fontSize: 14,
  },
  emptyContainer: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'rgba(255, 255, 255, 0.7)',
    zIndex: 1,
  },
  emptyTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#333',
    marginTop: 16,
  },
  emptyText: {
    fontSize: 16,
    color: '#666',
    textAlign: 'center',
    marginTop: 8,
    paddingHorizontal: 32,
  },
  detailsContainer: {
    position: 'absolute',
    bottom: 80,
    left: 0,
    right: 0,
    zIndex: 2,
  },
  controlsContainer: {
    position: 'absolute',
    right: 16,
    zIndex: 1,
    flexDirection: 'column',
    gap: 8,
  },
  controlButton: {
    width: 48,
    height: 48,
    borderRadius: 24,
    backgroundColor: 'white',
    justifyContent: 'center',
    alignItems: 'center',
    ...Platform.select({
      ios: {
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.2,
        shadowRadius: 4,
      },
      android: {
        elevation: 4,
      },
    }),
  },
});
