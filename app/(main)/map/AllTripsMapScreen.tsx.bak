import React, { useState, useEffect, useRef, useCallback } from 'react';
import {
  StyleSheet,
  View,
  Text,
  TouchableOpacity,
  ActivityIndicator,
  ScrollView,
  Platform,
  Dimensions,
  Alert
} from 'react-native';
import MapView, { PROVIDER_GOOGLE, Region, Marker } from 'react-native-maps';
import { StatusBar } from 'expo-status-bar';
import { MaterialIcons } from '@expo/vector-icons';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import * as Location from 'expo-location';
import { useAuth } from '../../../contexts/AuthContext';
import { collection, getDocs, onSnapshot, query } from 'firebase/firestore';
import { db } from '../../../services/firebase/firebase-core';
import { getAllMapMarkers, removePlaceFromMap } from '../../../services/firebase/mapMarkers';
import { SavedPlace, PlaceCategory } from '../../../types/SavedPlace';
import SavedPlaceMarker from '../../../components/map/SavedPlaceMarker';
import PlaceDetailCard from '../../../components/map/PlaceDetailCard';
import { convertToSavedPlace } from '../../../utils/convertToSavedPlace';

// Default region (centered on Europe)
const DEFAULT_REGION: Region = {
  latitude: 48.8566,
  longitude: 2.3522,
  latitudeDelta: 10,
  longitudeDelta: 10,
};

// Category filters for the map - simplified as per requirements
const CATEGORIES: { id: PlaceCategory | 'all', label: string }[] = [
  { id: 'all', label: 'All Places' },
  { id: 'restaurant', label: 'Food' },
  { id: 'attraction', label: 'Attractions' },
];

// Define styles at the top level to avoid 'used before declaration' errors
const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#fff',
  },
  map: {
    ...StyleSheet.absoluteFillObject,
  },
  titleContainer: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    alignItems: 'center',
    backgroundColor: 'transparent',
    paddingVertical: 8,
    zIndex: 1,
  },
  appTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#333',
    letterSpacing: 0.5,
  },
  cityContainer: {
    position: 'absolute',
    left: 0,
    right: 0,
    backgroundColor: 'transparent',
    zIndex: 1,
  },
  cityScrollContent: {
    paddingHorizontal: 16,
  },
  cityButton: {
    backgroundColor: 'white',
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 20,
    marginRight: 8,
    ...Platform.select({
      ios: {
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.1,
        shadowRadius: 4,
      },
      android: {
        elevation: 2,
      },
    }),
  },
  selectedCityButton: {
    backgroundColor: '#3A7BF8',
  },
  cityButtonText: {
    color: '#333',
    fontWeight: '500',
  },
  selectedCityButtonText: {
    color: 'white',
  },
  categoryContainer: {
    position: 'absolute',
    left: 0,
    right: 0,
    backgroundColor: 'transparent',
    zIndex: 1,
  },
  categoryScrollContent: {
    paddingHorizontal: 16,
  },
  categoryButton: {
    backgroundColor: 'white',
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 20,
    marginRight: 8,
    ...Platform.select({
      ios: {
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.1,
        shadowRadius: 4,
      },
      android: {
        elevation: 2,
      },
    }),
  },
  categoryButtonActive: {
    backgroundColor: '#3A7BF8',
  },
  categoryText: {
    color: '#333',
    fontWeight: '500',
  },
  categoryTextActive: {
    color: 'white',
  },
  loadingContainer: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'rgba(255, 255, 255, 0.7)',
    zIndex: 2,
  },
  loadingText: {
    marginTop: 12,
    fontSize: 16,
    color: '#333',
  },
  errorContainer: {
    position: 'absolute',
    left: 16,
    right: 16,
    backgroundColor: '#ffebee',
    padding: 16,
    borderRadius: 8,
    ...Platform.select({
      ios: {
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.1,
        shadowRadius: 4,
      },
      android: {
        elevation: 2,
      },
    }),
    zIndex: 2,
  },
  errorText: {
    color: '#d32f2f',
    fontSize: 14,
  },
  emptyContainer: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'rgba(255, 255, 255, 0.7)',
    zIndex: 1,
  },
  emptyTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#333',
    marginTop: 16,
  },
  emptyText: {
    fontSize: 16,
    color: '#666',
    textAlign: 'center',
    marginTop: 8,
    paddingHorizontal: 32,
  },
  detailsContainer: {
    position: 'absolute',
    bottom: 80,
    left: 0,
    right: 0,
    zIndex: 2,
  },
  controlsContainer: {
    position: 'absolute',
    right: 16,
    zIndex: 1,
    flexDirection: 'column',
    gap: 8,
  },
  controlButton: {
    width: 48,
    height: 48,
    borderRadius: 24,
    backgroundColor: 'white',
    justifyContent: 'center',
    alignItems: 'center',
    ...Platform.select({
      ios: {
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.2,
        shadowRadius: 4,
      },
      android: {
        elevation: 4,
      },
    }),
  },
});

export default function AllTripsMapScreen() {
  const { user } = useAuth();
  const insets = useSafeAreaInsets();
  const mapRef = useRef<MapView>(null);
  
  // State
  const [places, setPlaces] = useState<SavedPlace[]>([]);
  const [filteredPlaces, setFilteredPlaces] = useState<SavedPlace[]>([]);
  const [selectedPlace, setSelectedPlace] = useState<SavedPlace | null>(null);
  const [selectedCategory, setSelectedCategory] = useState<PlaceCategory | 'all'>('all');
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [userLocation, setUserLocation] = useState<{ latitude: number, longitude: number } | null>(null);
  const [initialRegion, setInitialRegion] = useState<Region>(DEFAULT_REGION);
  
  // City filtering state
  const [cities, setCities] = useState<string[]>([]);
  const [selectedCity, setSelectedCity] = useState<string | 'all'>('all');
  const [cityPlaces, setCityPlaces] = useState<{[city: string]: SavedPlace[]}>({});

  // Request location permission and get user's location
  useEffect(() => {
    (async () => {
      try {
        const { status } = await Location.requestForegroundPermissionsAsync();
        if (status === 'granted') {
          const location = await Location.getCurrentPositionAsync({});
          const { latitude, longitude } = location.coords;
          setUserLocation({ latitude, longitude });
          setInitialRegion({
            latitude,
            longitude,
            latitudeDelta: 0.05,
            longitudeDelta: 0.05,
          });
        }
      } catch (err) {
        console.log('Error getting location:', err);
        // Continue with default region if location fails
      }
    })();
  }, []);

  // Helper function to fit map to show all places
  const fitMapToPlaces = useCallback((placesToFit: SavedPlace[]) => {
    if (!mapRef.current || placesToFit.length === 0) return;

    const coordinates = placesToFit.map(place => ({
      latitude: place.latitude,
      longitude: place.longitude,
    }));

    mapRef.current.fitToCoordinates(coordinates, {
      edgePadding: { top: 100, right: 100, bottom: 100, left: 100 },
      animated: true,
    });
  }, []);

  // Refresh map data when needed
  const refreshMapData = useCallback(async () => {
    if (!user) {
      setLoading(false);
      return;
    }

    console.log('Refreshing map data for user:', user.uid);
    setLoading(true);
    setError(null);
    
    try {
      // Create a query for all trips
      const tripsRef = collection(db, 'tripPlans', user.uid, 'plans');
      const tripsQuery = query(tripsRef);
      
      // Get all trips
      const tripsSnapshot = await getDocs(tripsQuery);
      
      // Get today's date in YYYY-MM-DD format for comparison
      const today = new Date().toISOString().split('T')[0];
      
      // Extract unique cities from trip plans (only upcoming and non-completed trips)
      const citiesSet = new Set<string>();
      const tripsList = tripsSnapshot.docs
        .filter(doc => {
          const tripData = doc.data();
          // Filter out completed trips
          if (tripData.status === 'completed') return false;
          
          // Filter out past trips (only show upcoming trips)
          const startDate = tripData.startDate || '';
          return startDate >= today; // Only include trips with start dates in the future
        })
        .map(doc => {
          const destination = doc.data().destination || 'Unnamed Trip';
          if (destination !== 'Unnamed Trip') {
            citiesSet.add(destination);
          }
          return {
            id: doc.id,
            name: destination
          };
        });
        
      console.log(`Found ${tripsList.length} non-completed trips`);
      
      // Convert Set to Array for state
      const uniqueCities = Array.from(citiesSet);
      setCities(uniqueCities);
      console.log(`Found ${uniqueCities.length} unique cities`);
      
      // Get all map markers for this user across all trips
      const allMarkers = await getAllMapMarkers(user.uid);
      console.log('All markers retrieved:', allMarkers.length);
      
      // Filter markers to only include those from upcoming trips in tripsList
      const upcomingTripIds = tripsList.map(trip => trip.id);
      
      // Debug logs to help diagnose the issue
      console.log('Upcoming trip IDs:', upcomingTripIds);
      console.log('All markers planIds:', allMarkers.map(m => m.planId));
      
      // FIXED: Include all markers regardless of planId match
      // This ensures that saved places from itineraries will show up
      // We'll still filter out completed trips via the tripsList filtering above
      const filteredMarkers = allMarkers;
      
      console.log(`Found ${allMarkers.length} total markers, ${filteredMarkers.length} from upcoming trips`);
      
      // Even if no markers were found, we should still set up the map with empty state
      if (filteredMarkers.length === 0) {
        console.log('No markers found for upcoming trips');
        setPlaces([]);
        setFilteredPlaces([]);
        setCityPlaces({ 'all': [] });
        setLoading(false);
        return;
      }
      
      // Convert to SavedPlace objects
      const savedPlaces: SavedPlace[] = [];
      const placesByCity: {[city: string]: SavedPlace[]} = {};
      
      // Initialize placesByCity with empty arrays for each city
      uniqueCities.forEach(city => {
        placesByCity[city] = [];
      });
      
      // Add an 'all' category to hold all places
      placesByCity['all'] = [];
      
      for (const marker of filteredMarkers) {
        const savedPlace = convertToSavedPlace(marker);
        if (savedPlace) {
          savedPlaces.push(savedPlace);
          
          // Add to 'all' category
          placesByCity['all'].push(savedPlace);
          
          // Find which city this place belongs to based on planId
          const tripData = tripsList.find(trip => trip.id === marker.planId);
          if (tripData && tripData.name !== 'Unnamed Trip') {
            // If this city exists in our placesByCity object, add the place
            if (placesByCity[tripData.name]) {
              placesByCity[tripData.name].push(savedPlace);
            }
          }
        }
      }
      
      console.log(`Converted ${savedPlaces.length} places`);
      
      // Set places and filtered places
      setPlaces(savedPlaces);
      setFilteredPlaces(savedPlaces);
      setCityPlaces(placesByCity);
      
      // Update filtered places based on selected category
      if (selectedCategory !== 'all') {
        const filtered = savedPlaces.filter(place => place.category === selectedCategory);
        setFilteredPlaces(filtered);
      }
      
      // Update filtered places based on selected city
      if (selectedCity !== 'all' && placesByCity[selectedCity]) {
        const cityFiltered = placesByCity[selectedCity];
        if (selectedCategory !== 'all') {
          setFilteredPlaces(cityFiltered.filter(place => place.category === selectedCategory));
        } else {
          setFilteredPlaces(cityFiltered);
        }
      }
      
      setLoading(false);
    } catch (error) {
      console.error('Error loading map data:', error);
      setError('Failed to load map data. Please try again.');
      setLoading(false);
    }
  }, [user]);

  // Handle refresh button click
  const handleRefresh = useCallback(() => {
    refreshMapData();
  }, [refreshMapData]);

  // Load all saved places from all trips and organize by city
  useEffect(() => {
    if (!user) {
      setLoading(false);
      return;
    }

    console.log('Setting up real-time listener for trips for user:', user.uid);
    
    // Initial load of data
    refreshMapData();
    
    // Set up real-time listener for trips to detect changes
    const tripsRef = collection(db, 'tripPlans', user.uid, 'plans');
    const tripsQuery = query(tripsRef);
    
    const unsubscribe = onSnapshot(tripsQuery, async (tripsSnapshot) => {
      // Refresh data when trips change
      console.log('Trip data changed, refreshing map data');
      refreshMapData();
      try {
        // Get today's date in YYYY-MM-DD format for comparison
        const today = new Date().toISOString().split('T')[0];
        
        // Extract unique cities from trip plans (only upcoming and non-completed trips)
        const citiesSet = new Set<string>();
        const tripsList = tripsSnapshot.docs
          .filter(doc => {
            const tripData = doc.data();
            // Filter out completed trips
            if (tripData.status === 'completed') return false;
            
            // Filter out past trips (only show upcoming trips)
            const startDate = tripData.startDate || '';
            return startDate >= today; // Only include trips with start dates in the future
          })
          .map(doc => {
            const destination = doc.data().destination || 'Unnamed Trip';
            if (destination !== 'Unnamed Trip') {
              citiesSet.add(destination);
            }
            return {
              id: doc.id,
              name: destination
            };
          });
          
        console.log(`Found ${tripsList.length} non-completed trips`);
        
        // Convert Set to Array for state
        const uniqueCities = Array.from(citiesSet);
        setCities(uniqueCities);
        console.log(`Found ${uniqueCities.length} unique cities`);
        
        // Get all map markers for this user across all trips
        // We'll filter the markers to only include those from upcoming trips
        const allMarkers = await getAllMapMarkers(user.uid);
        
        // Filter markers to only include those from upcoming trips in tripsList
        const upcomingTripIds = tripsList.map(trip => trip.id);
        
        // Debug logs to help diagnose the issue
        console.log('Upcoming trip IDs:', upcomingTripIds);
        console.log('All markers planIds:', allMarkers.map(m => m.planId));
        
        // Make sure we're comparing strings to strings
        const filteredMarkers = allMarkers.filter(marker => {
          if (!marker.planId) {
            console.log('Marker missing planId:', marker);
            return false;
          }
          return upcomingTripIds.includes(marker.planId);
        });
        
        console.log(`Found ${allMarkers.length} total markers, ${filteredMarkers.length} from upcoming trips`);
        
        // Even if no markers were found, we should still set up the map with empty state
        if (filteredMarkers.length === 0) {
          console.log('No markers found for upcoming trips');
          setPlaces([]);
          setFilteredPlaces([]);
          setCityPlaces({ 'all': [] });
          setLoading(false);
          return;
        }
        
        // Convert to SavedPlace objects
        const savedPlaces: SavedPlace[] = [];
        const placesByCity: {[city: string]: SavedPlace[]} = {};
        
        // Initialize placesByCity with empty arrays for each city
        uniqueCities.forEach(city => {
          placesByCity[city] = [];
        });
        
        // Add an 'all' category to hold all places
        placesByCity['all'] = [];
        
        for (const marker of filteredMarkers) {
          const savedPlace = convertToSavedPlace(marker);
          if (savedPlace) {
            savedPlaces.push(savedPlace);
            
            // Add to 'all' category
            placesByCity['all'].push(savedPlace);
            
            // Find which city this place belongs to based on planId
            const tripData = tripsList.find(trip => trip.id === marker.planId);
            if (tripData && tripData.name !== 'Unnamed Trip') {
              // If this city exists in our placesByCity object, add the place
              if (placesByCity[tripData.name]) {
                placesByCity[tripData.name].push(savedPlace);
              }
            }
          }
        }
        
        console.log(`Converted ${savedPlaces.length} places`);
        
        // Set places and filtered places
        setPlaces(savedPlaces);
        setFilteredPlaces(savedPlaces);
        setCityPlaces(placesByCity);
        
        // Set default selected city if available
        if (uniqueCities.length > 0) {
          setSelectedCity(uniqueCities[0]);
          setFilteredPlaces(placesByCity[uniqueCities[0]]);
        }
        
        // Fit map to show all places
        if (savedPlaces.length > 0 && mapRef.current) {
          fitMapToPlaces(savedPlaces);
        }
      } catch (error) {
        console.error('Error loading saved places:', error);
        setError('Failed to load saved places. Please try again.');
      } finally {
        setLoading(false);
      }
    });
    
    // Clean up the listener when the component unmounts
    return () => unsubscribe();
  }, [user, fitMapToPlaces]);

  // Handle city selection
  const handleCitySelect = useCallback((city: string | 'all') => {
    setSelectedCity(city);
    
    // Apply both city and category filters
    let filtered = cityPlaces[city] || [];
    
    // Apply category filter if not 'all'
    if (selectedCategory !== 'all') {
      filtered = filtered.filter(place => place.category === selectedCategory);
    }
    
    setFilteredPlaces(filtered);
    
    // Fit map to show places in this city
    if (filtered.length > 0 && mapRef.current) {
      fitMapToPlaces(filtered);
    }
  }, [selectedCategory, cityPlaces, fitMapToPlaces]);
  
  // Handle category selection
  const handleCategorySelect = useCallback((category: PlaceCategory | 'all') => {
    setSelectedCategory(category);
    
    // Apply both city and category filters
    let filtered = cityPlaces[selectedCity] || [];
    
    // Apply category filter if not 'all'
    if (category !== 'all') {
      filtered = filtered.filter(place => place.category === category);
    }
    
    setFilteredPlaces(filtered);
  }, [selectedCity, cityPlaces]);

  // Handle unsaving a place
  const handleUnsavePlace = useCallback(async (placeId: string) => {
    if (!user || !placeId) return;
    
    try {
      setLoading(true);
      
      // Find the place to unsave
      const placeToRemove = places.find(place => place.id === placeId);
      if (!placeToRemove) {
        console.error('Place not found:', placeId);
        setLoading(false);
        return;
      }
      
      // Remove from Firestore
      const success = await removePlaceFromMap(user.uid, placeId);
      
      if (success) {
        // Update local state
        const updatedPlaces = places.filter(place => place.id !== placeId);
        setPlaces(updatedPlaces);
        
        // Update filtered places
        const updatedFiltered = filteredPlaces.filter(place => place.id !== placeId);
        setFilteredPlaces(updatedFiltered);
        
        // Update city places
        const updatedCityPlaces = { ...cityPlaces };
        Object.keys(updatedCityPlaces).forEach(city => {
          updatedCityPlaces[city] = updatedCityPlaces[city].filter(place => place.id !== placeId);
        });
        setCityPlaces(updatedCityPlaces);
        
        // Close the detail card
        setSelectedPlace(null);
      }
    } catch (error) {
      console.error('Error unsaving place:', error);
    
    {/* App Title */}
    <View style={[styles.titleContainer, { paddingTop: insets.top }]}>
      <Text style={styles.appTitle}>Tripzy</Text>
    </View>
    
    {/* City Selector */}
    <View style={[styles.cityContainer, { top: insets.top + 40 }]}>
      <ScrollView
        horizontal
        showsHorizontalScrollIndicator={false}
        contentContainerStyle={styles.cityScrollContent}
      >
        {cities.map(city => (
          <TouchableOpacity
            key={city}
            style={[
              styles.cityButton,
              selectedCity === city ? styles.selectedCityButton : null
            ]}
            onPress={() => handleCitySelect(city)}
          >
            <Text
              style={[
                styles.cityButtonText,
                selectedCity === city ? styles.selectedCityButtonText : null
              ]}
            >
              {city}
            </Text>
          </TouchableOpacity>
        ))}
        <TouchableOpacity
          key="all"
          style={[
            styles.cityButton,
            selectedCity === 'all' ? styles.selectedCityButton : null
          ]}
          onPress={() => handleCitySelect('all')}
        >
          <Text
            style={[
              styles.cityButtonText,
              selectedCity === 'all' ? styles.selectedCityButtonText : null
            ]}
          >
            All Cities
          </Text>
        </TouchableOpacity>
      </ScrollView>
    </View>

  return (
    <View style={styles.container}>
      <StatusBar style="auto" />
      
      {/* Map */}
      <MapView
        ref={mapRef}
        style={styles.map}
        provider={PROVIDER_GOOGLE}
        initialRegion={initialRegion}
        showsUserLocation
        showsMyLocationButton={false}
      >
        {filteredPlaces.map((place) => (
          <SavedPlaceMarker
            key={place.id}
            place={place}
            onPress={() => setSelectedPlace(place)}
          />
        ))}
        
        {/* User location marker */}
        {userLocation && (
          <Marker
            coordinate={{
              latitude: userLocation.latitude,
              longitude: userLocation.longitude,
            }}
            title="Your Location"
            pinColor="blue"
          />
        )}
      </MapView>
      
      {/* App Title */}
      <View style={[styles.titleContainer, { paddingTop: insets.top }]}>
        <Text style={styles.appTitle}>Tripzy</Text>
      </View>
      
      {/* City Selector */}
      <View style={[styles.cityContainer, { top: insets.top + 40 }]}>
        <ScrollView
          horizontal
          showsHorizontalScrollIndicator={false}
          contentContainerStyle={styles.cityScrollContent}
        >
          <TouchableOpacity
            style={[
              styles.cityButton,
              selectedCity === 'all' && styles.selectedCityButton,
            ]}
            onPress={() => handleCitySelect('all')}
          >
            <Text
              style={[
                styles.cityButtonText,
                selectedCity === 'all' && styles.selectedCityButtonText,
              ]}
            >
              All Cities
            </Text>
          </TouchableOpacity>
          
          {cities.map((city) => (
            <TouchableOpacity
              key={city}
              style={[
                styles.cityButton,
                selectedCity === city && styles.selectedCityButton,
              ]}
              onPress={() => handleCitySelect(city)}
            >
              <Text
                style={[
                  styles.cityButtonText,
                  selectedCity === city && styles.selectedCityButtonText,
                ]}
              >
                {city}
              </Text>
            </TouchableOpacity>
          ))}
        </ScrollView>
      </View>
      
      {/* Category Filter */}
      <View style={[styles.categoryContainer, { top: insets.top + 90 }]}>
        <ScrollView
          horizontal
          showsHorizontalScrollIndicator={false}
          contentContainerStyle={styles.categoryScrollContent}
        >
          {CATEGORIES.map((category) => (
            <TouchableOpacity
              key={category.id}
              style={[
                styles.categoryButton,
                selectedCategory === category.id && styles.categoryButtonActive,
              ]}
              onPress={() => handleCategorySelect(category.id)}
            >
              <Text
                style={[
                  styles.categoryText,
                  selectedCategory === category.id && styles.categoryTextActive,
                ]}
              >
                {category.label}
              </Text>
            </TouchableOpacity>
          ))}
        </ScrollView>
      </View>
      
      {/* Loading Indicator */}
      {loading && (
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color="#3A7BF8" />
          <Text style={styles.loadingText}>Loading saved places...</Text>
        </View>
      )}
      
      {/* Error Message */}
      {error && (
        <View style={[styles.errorContainer, { top: insets.top + 140 }]}>
          <Text style={styles.errorText}>{error}</Text>
        </View>
      )}
      
      {/* Empty State */}
      {!loading && filteredPlaces.length === 0 && (
        <View style={styles.emptyContainer}>
          <MaterialIcons name="map" size={64} color="#CCCCCC" />
          <Text style={styles.emptyTitle}>No Places Saved</Text>
          <Text style={styles.emptyText}>
            Save places from your itinerary to see them on the map.
          </Text>
        </View>
      )}
      
      {/* Place Details */}
      {selectedPlace && (
        <View style={styles.detailsContainer}>
          <PlaceDetailCard
            place={selectedPlace}
            onClose={() => setSelectedPlace(null)}
            onUnsave={handleUnsavePlace}
          />
        </View>
      )}
      
      {/* Map Controls */}
      <View style={[styles.controlsContainer, { bottom: insets.bottom + 16 }]}>
        <TouchableOpacity
          style={styles.controlButton}
          onPress={() => fitMapToPlaces(filteredPlaces)}
        >
          <MaterialIcons name="fit-screen" size={24} color="#333" />
        </TouchableOpacity>
        
        <TouchableOpacity
          style={styles.controlButton}
          onPress={handleRefresh}
        >
          <MaterialIcons name="refresh" size={24} color="#333" />
        </TouchableOpacity>
      </View>
    </View>
  );
}
