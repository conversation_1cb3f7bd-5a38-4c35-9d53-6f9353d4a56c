import React, { useState, useEffect, useRef, useCallback } from 'react';
import {
  StyleSheet,
  View,
  Text,
  TouchableOpacity,
  ActivityIndicator,
  ScrollView,
  Platform,
  SafeAreaView,
  Dimensions
} from 'react-native';
import MapView, { PROVIDER_GOOGLE, Region } from 'react-native-maps';
import { StatusBar } from 'expo-status-bar';
import { MaterialIcons } from '@expo/vector-icons';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import * as Location from 'expo-location';
import { useLocalSearchParams } from 'expo-router';
import { useAuth } from '../../../contexts/AuthContext';
import { collection, doc, getDoc, getDocs, query, where } from 'firebase/firestore';
import { db } from '../../../services/firebase/firebase-core';
import { SavedPlace, PlaceCategory } from '../../../types/SavedPlace';
import SavedPlaceMarker from '../../../components/map/SavedPlaceMarker';
import PlaceDetailCard from '../../../components/map/PlaceDetailCard';
import { convertToSavedPlace } from '../../../utils/convertToSavedPlace';

// Default region (centered on Europe)
const DEFAULT_REGION: Region = {
  latitude: 48.8566,
  longitude: 2.3522,
  latitudeDelta: 10,
  longitudeDelta: 10,
};

// Category filters for the map
const CATEGORIES: { id: PlaceCategory | 'all', label: string }[] = [
  { id: 'all', label: 'All Places' },
  { id: 'restaurant', label: 'Restaurants' },
  { id: 'hotel', label: 'Hotels' },
  { id: 'attraction', label: 'Attractions' },
  { id: 'event', label: 'Events' },
  { id: 'other', label: 'Other' },
];

export default function MapScreen() {
  const { user } = useAuth();
  const params = useLocalSearchParams();
  const { tripId } = params;
  const insets = useSafeAreaInsets();
  const mapRef = useRef<MapView>(null);
  
  // State
  const [places, setPlaces] = useState<SavedPlace[]>([]);
  const [filteredPlaces, setFilteredPlaces] = useState<SavedPlace[]>([]);
  const [selectedPlace, setSelectedPlace] = useState<SavedPlace | null>(null);
  const [selectedCategory, setSelectedCategory] = useState<PlaceCategory | 'all'>('all');
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [userLocation, setUserLocation] = useState<{ latitude: number, longitude: number } | null>(null);
  const [initialRegion, setInitialRegion] = useState<Region>(DEFAULT_REGION);
  const [trips, setTrips] = useState<{ id: string, name: string }[]>([]);
  const [selectedTripId, setSelectedTripId] = useState<string | null>(null);

  // Request location permission and get user's location
  useEffect(() => {
    (async () => {
      try {
        const { status } = await Location.requestForegroundPermissionsAsync();
        if (status === 'granted') {
          const location = await Location.getCurrentPositionAsync({});
          const { latitude, longitude } = location.coords;
          setUserLocation({ latitude, longitude });
          setInitialRegion({
            latitude,
            longitude,
            latitudeDelta: 0.05,
            longitudeDelta: 0.05,
          });
        }
      } catch (err) {
        console.log('Error getting location:', err);
        // Continue with default region if location fails
      }
    })();
  }, []);

  // Load user's trips
  useEffect(() => {
    if (!user) return;

    const loadTrips = async () => {
      try {
        const tripsRef = collection(db, 'tripPlans', user.uid, 'plans');
        const tripsSnapshot = await getDocs(tripsRef);
        
        const tripsData = tripsSnapshot.docs.map(doc => ({
          id: doc.id,
          name: doc.data().destination || 'Unnamed Trip'
        }));
        
        setTrips(tripsData);
        
        // If tripId is provided in params, use it
        if (tripId) {
          setSelectedTripId(tripId as string);
        } 
        // Otherwise use the first trip if available
        else if (tripsData.length > 0) {
          setSelectedTripId(tripsData[0].id);
        }
      } catch (err) {
        console.error('Error loading trips:', err);
        setError('Failed to load trips');
      }
    };
    
    loadTrips();
  }, [user, tripId]);

  // Load saved places when selectedTripId changes
  useEffect(() => {
    if (!user || !selectedTripId) {
      setLoading(false);
      return;
    }

    const loadSavedPlaces = async () => {
      try {
        setLoading(true);
        setError(null);
        
        const docId = `${user.uid}_${selectedTripId}`;
        console.log(`Loading saved places from document: ${docId}`);
        
        const docRef = doc(db, 'mapMarkers', docId);
        const docSnap = await getDoc(docRef);
        
        if (!docSnap.exists()) {
          console.log(`No saved places found for trip: ${selectedTripId}`);
          setPlaces([]);
          setFilteredPlaces([]);
          setLoading(false);
          return;
        }
        
        const data = docSnap.data();
        
        if (!data.places || !Array.isArray(data.places) || data.places.length === 0) {
          console.log('Document exists but has no places array or it is empty');
          setPlaces([]);
          setFilteredPlaces([]);
          setLoading(false);
          return;
        }
        
        console.log(`Found ${data.places.length} saved places`);
        
        // Convert raw Firestore data to SavedPlace objects
        const savedPlaces = data.places
          .map((place: any, index: number) => 
            convertToSavedPlace(place, selectedTripId)
          )
          .filter(Boolean) as SavedPlace[];
        
        console.log(`Successfully converted ${savedPlaces.length} places`);
        
        setPlaces(savedPlaces);
        
        // Apply current category filter
        if (selectedCategory === 'all') {
          setFilteredPlaces(savedPlaces);
        } else {
          setFilteredPlaces(savedPlaces.filter(place => place.category === selectedCategory));
        }
        
        // Fit map to show all places
        if (savedPlaces.length > 0) {
          fitMapToPlaces(savedPlaces);
        }
      } catch (err) {
        console.error('Error loading saved places:', err);
        setError('Failed to load saved places');
      } finally {
        setLoading(false);
      }
    };
    
    loadSavedPlaces();
  }, [user, selectedTripId]);

  // Filter places by category
  useEffect(() => {
    if (selectedCategory === 'all') {
      setFilteredPlaces(places);
    } else {
      setFilteredPlaces(places.filter(place => place.category === selectedCategory));
    }
    
    // Fit map to filtered places if there are any
    if (filteredPlaces.length > 0) {
      fitMapToPlaces(filteredPlaces);
    }
  }, [selectedCategory, places]);

  // Fit map to show all places
  const fitMapToPlaces = useCallback((placesToFit: SavedPlace[]) => {
    if (!mapRef.current || placesToFit.length === 0) return;
    
    // Extract coordinates from places
    const coordinates = placesToFit.map(place => ({
      latitude: place.latitude,
      longitude: place.longitude,
    }));
    
    // Add user location if available
    if (userLocation) {
      coordinates.push(userLocation);
    }
    
    // Fit map to coordinates with padding
    setTimeout(() => {
      mapRef.current?.fitToCoordinates(coordinates, {
        edgePadding: { 
          top: 100, 
          right: 50, 
          bottom: 200, 
          left: 50 
        },
        animated: true,
      });
    }, 500);
  }, [userLocation]);

  // Handle place selection
  const handlePlacePress = useCallback((place: SavedPlace) => {
    setSelectedPlace(place);
    
    // Center map on selected place
    mapRef.current?.animateToRegion({
      latitude: place.latitude,
      longitude: place.longitude,
      latitudeDelta: 0.01,
      longitudeDelta: 0.01,
    });
  }, []);

  // Handle category selection
  const handleCategoryPress = useCallback((category: PlaceCategory | 'all') => {
    setSelectedCategory(category);
    setSelectedPlace(null);
  }, []);

  // Handle trip selection
  const handleTripChange = useCallback((tripId: string) => {
    setSelectedTripId(tripId);
    setSelectedPlace(null);
  }, []);

  // Get the trip name from the selected trip ID
  const getSelectedTripName = useCallback(() => {
    const trip = trips.find(t => t.id === selectedTripId);
    return trip ? trip.name : 'All Trips';
  }, [trips, selectedTripId]);

  return (
    <SafeAreaView style={styles.container}>
      <StatusBar style="auto" />
      
      {/* Map */}
      <MapView
        ref={mapRef}
        style={styles.map}
        provider={PROVIDER_GOOGLE}
        initialRegion={initialRegion}
        showsUserLocation
        showsMyLocationButton
        showsCompass
        showsScale
        rotateEnabled
        loadingEnabled
      >
        {/* Place Markers */}
        {filteredPlaces.map((place) => (
          <SavedPlaceMarker
            key={place.id}
            place={place}
            onPress={handlePlacePress}
          />
        ))}
      </MapView>
      
      {/* Header */}
      <View style={[styles.header, { paddingTop: insets.top || 16 }]}>
        <Text style={styles.title}>Saved Places</Text>
        {selectedTripId && (
          <Text style={styles.subtitle}>{getSelectedTripName()}</Text>
        )}
      </View>
      
      {/* Trip Selector */}
      {trips.length > 1 && (
        <View style={styles.tripSelectorContainer}>
          <ScrollView
            horizontal
            showsHorizontalScrollIndicator={false}
            contentContainerStyle={styles.tripSelectorContent}
          >
            {trips.map((trip) => (
              <TouchableOpacity
                key={trip.id}
                style={[
                  styles.tripButton,
                  selectedTripId === trip.id && styles.selectedTripButton
                ]}
                onPress={() => handleTripChange(trip.id)}
              >
                <Text
                  style={[
                    styles.tripButtonText,
                    selectedTripId === trip.id && styles.selectedTripButtonText
                  ]}
                >
                  {trip.name}
                </Text>
              </TouchableOpacity>
            ))}
          </ScrollView>
        </View>
      )}
      
      {/* Category Filters */}
      <View style={styles.categoryContainer}>
        <ScrollView
          horizontal
          showsHorizontalScrollIndicator={false}
          contentContainerStyle={styles.categoryScrollContent}
        >
          {CATEGORIES.map((category) => (
            <TouchableOpacity
              key={category.id}
              style={[
                styles.categoryButton,
                selectedCategory === category.id && styles.categoryButtonActive
              ]}
              onPress={() => handleCategoryPress(category.id)}
            >
              <Text
                style={[
                  styles.categoryText,
                  selectedCategory === category.id && styles.categoryTextActive
                ]}
              >
                {category.label}
              </Text>
            </TouchableOpacity>
          ))}
        </ScrollView>
      </View>
      
      {/* Loading Indicator */}
      {loading && (
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color="#3A7BF8" />
          <Text style={styles.loadingText}>Loading places...</Text>
        </View>
      )}
      
      {/* Error Message */}
      {error && (
        <View style={styles.errorContainer}>
          <Text style={styles.errorText}>{error}</Text>
        </View>
      )}
      
      {/* Empty State */}
      {!loading && filteredPlaces.length === 0 && !error && (
        <View style={styles.emptyContainer}>
          <MaterialIcons name="place" size={64} color="#ccc" />
          <Text style={styles.emptyTitle}>No Places Found</Text>
          <Text style={styles.emptyText}>
            {selectedCategory === 'all'
              ? 'Save places from your itinerary to see them on the map.'
              : `No ${selectedCategory} places found. Try a different category.`}
          </Text>
        </View>
      )}
      
      {/* Place Details */}
      {selectedPlace && (
        <View style={styles.detailsContainer}>
          <PlaceDetailCard
            place={selectedPlace}
            onClose={() => setSelectedPlace(null)}
          />
        </View>
      )}
      
      {/* Map Controls */}
      <View style={[styles.controlsContainer, { bottom: insets.bottom + 16 }]}>
        <TouchableOpacity
          style={styles.controlButton}
          onPress={() => fitMapToPlaces(filteredPlaces)}
        >
          <MaterialIcons name="fit-screen" size={24} color="#333" />
        </TouchableOpacity>
      </View>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#fff',
  },
  map: {
    ...StyleSheet.absoluteFillObject,
  },
  header: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    backgroundColor: 'rgba(255, 255, 255, 0.9)',
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: 'rgba(0, 0, 0, 0.1)',
    zIndex: 1,
  },
  title: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#333',
  },
  subtitle: {
    fontSize: 14,
    color: '#666',
    marginTop: 4,
  },
  tripSelectorContainer: {
    position: 'absolute',
    top: Platform.OS === 'ios' ? 90 : 80,
    left: 0,
    right: 0,
    backgroundColor: 'transparent',
    zIndex: 1,
  },
  tripSelectorContent: {
    paddingHorizontal: 16,
  },
  tripButton: {
    backgroundColor: 'white',
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 20,
    marginRight: 8,
    ...Platform.select({
      ios: {
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.1,
        shadowRadius: 4,
      },
      android: {
        elevation: 2,
      },
    }),
  },
  selectedTripButton: {
    backgroundColor: '#3A7BF8',
  },
  tripButtonText: {
    color: '#333',
    fontWeight: '500',
  },
  selectedTripButtonText: {
    color: 'white',
  },
  categoryContainer: {
    position: 'absolute',
    top: Platform.OS === 'ios' ? 140 : 130,
    left: 0,
    right: 0,
    backgroundColor: 'transparent',
    zIndex: 1,
  },
  categoryScrollContent: {
    paddingHorizontal: 16,
  },
  categoryButton: {
    backgroundColor: 'white',
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 20,
    marginRight: 8,
    ...Platform.select({
      ios: {
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.1,
        shadowRadius: 4,
      },
      android: {
        elevation: 2,
      },
    }),
  },
  categoryButtonActive: {
    backgroundColor: '#3A7BF8',
  },
  categoryText: {
    color: '#333',
    fontWeight: '500',
  },
  categoryTextActive: {
    color: 'white',
  },
  loadingContainer: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'rgba(255, 255, 255, 0.7)',
    zIndex: 2,
  },
  loadingText: {
    marginTop: 12,
    fontSize: 16,
    color: '#333',
  },
  errorContainer: {
    position: 'absolute',
    top: Platform.OS === 'ios' ? 190 : 180,
    left: 16,
    right: 16,
    backgroundColor: '#ffebee',
    padding: 16,
    borderRadius: 8,
    ...Platform.select({
      ios: {
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.1,
        shadowRadius: 4,
      },
      android: {
        elevation: 2,
      },
    }),
    zIndex: 2,
  },
  errorText: {
    color: '#d32f2f',
    fontSize: 14,
  },
  emptyContainer: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'rgba(255, 255, 255, 0.7)',
    zIndex: 1,
  },
  emptyTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#333',
    marginTop: 16,
  },
  emptyText: {
    fontSize: 16,
    color: '#666',
    textAlign: 'center',
    marginTop: 8,
    paddingHorizontal: 32,
  },
  detailsContainer: {
    position: 'absolute',
    bottom: 80,
    left: 0,
    right: 0,
    zIndex: 2,
  },
  controlsContainer: {
    position: 'absolute',
    right: 16,
    zIndex: 1,
  },
  controlButton: {
    width: 48,
    height: 48,
    borderRadius: 24,
    backgroundColor: 'white',
    justifyContent: 'center',
    alignItems: 'center',
    ...Platform.select({
      ios: {
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.2,
        shadowRadius: 4,
      },
      android: {
        elevation: 4,
      },
    }),
  },
});
