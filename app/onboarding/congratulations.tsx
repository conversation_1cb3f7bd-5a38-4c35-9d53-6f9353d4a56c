import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  Dimensions,
  TouchableOpacity,
  Alert,
} from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { useRouter } from 'expo-router';
import { Ionicons } from '@expo/vector-icons';
import { useCopilot } from 'react-native-copilot';

import { useTheme } from 'react-native-paper';
import { useAuth } from '../../contexts/AuthContext';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { doc, setDoc } from 'firebase/firestore';
import { db } from '../../services/firebase';

const { width, height } = Dimensions.get('window');

export default function OnboardingCongratulationsScreen() {
  const router = useRouter();
  const { colors } = useTheme();
  const { start } = useCopilot();
  const { user } = useAuth();
  const [isLoading, setIsLoading] = useState(false);

  // Debug function to test tutorial start - navigate to dashboard first then start
  const debugStartTutorial = async () => {
    console.log('[DEBUG] Starting tutorial with navigation...');

    // Navigate to dashboard first
    router.replace('/(main)/dashboard');

    // Wait for dashboard to load, then start tutorial
    setTimeout(() => {
      console.log('[DEBUG] Dashboard loaded, starting tutorial...');
      console.log('[DEBUG] Start function type:', typeof start);
      try {
        start();
        console.log('[DEBUG] Tutorial start() called successfully');
      } catch (error) {
        console.error('[DEBUG] Error starting tutorial:', error);
      }
    }, 2000); // 2 second delay to ensure dashboard is fully loaded
  };

  const handleStartTutorial = async () => {
    if (!user?.uid) return;

    setIsLoading(true);

    try {
      // Mark onboarding as completed since this is now the final step
      const userDocRef = doc(db, 'users', user.uid);
      await setDoc(userDocRef, { onboardingCompleted: true }, { merge: true });
      console.log('[OnboardingCongratulations] Marked onboarding as completed');

      // Mark congratulations as seen
      const CONGRATULATIONS_STORAGE_KEY = 'congratulations_seen';
      const userCongratulationsKey = `${CONGRATULATIONS_STORAGE_KEY}_${user.uid}`;
      await AsyncStorage.setItem(userCongratulationsKey, 'true');
      console.log('[OnboardingCongratulations] Marked congratulations as seen');

      // Navigate to dashboard first
      router.replace('/(main)/dashboard');

      // Simple delay then start tutorial
      setTimeout(() => {
        console.log('[OnboardingCongratulations] Starting tutorial...');
        start();
      }, 1000);

    } catch (error) {
      console.error('[OnboardingCongratulations] Error completing onboarding:', error);
      Alert.alert('Error', 'There was a problem completing your setup. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <LinearGradient
      colors={['#3A7BF8', '#1E40AF', '#1E3A8A']}
      style={styles.container}
    >
      {/* Decorative floating circles */}
      <View style={[styles.floatingCircle, styles.circle1]} />
      <View style={[styles.floatingCircle, styles.circle2]} />
      <View style={[styles.floatingCircle, styles.circle3]} />
      
      <View style={styles.content}>
        {/* Celebration Icon */}
        <View style={styles.iconContainer}>
          <Ionicons name="checkmark-circle" size={80} color="#FFFFFF" />
        </View>
        
        {/* Main Message */}
        <Text style={styles.title}>Congratulations!</Text>
        <Text style={styles.subtitle}>
          You are one step away from creating the best travel plans of your life
        </Text>
        
        {/* Features List */}
        <View style={styles.featuresContainer}>
          <View style={styles.featureItem}>
            <Ionicons name="map" size={24} color="#FFFFFF" />
            <Text style={styles.featureText}>Personalized itineraries</Text>
          </View>
          
          <View style={styles.featureItem}>
            <Ionicons name="heart" size={24} color="#FFFFFF" />
            <Text style={styles.featureText}>Save your favorite places</Text>
          </View>
          
          <View style={styles.featureItem}>
            <Ionicons name="chatbubble-ellipses" size={24} color="#FFFFFF" />
            <Text style={styles.featureText}>AI travel assistant</Text>
          </View>
        </View>
        
        {/* Start Tutorial Button */}
        <TouchableOpacity
          style={[styles.startButton, isLoading && styles.startButtonDisabled]}
          onPress={handleStartTutorial}
          disabled={isLoading}
        >
          <Text style={styles.startButtonText}>
            {isLoading ? 'Setting up...' : 'Start Tutorial'}
          </Text>
          {!isLoading && (
            <Ionicons name="arrow-forward" size={24} color="#3A7BF8" style={styles.buttonIcon} />
          )}
        </TouchableOpacity>

        {/* Debug Button */}
        <TouchableOpacity
          style={[styles.startButton, { backgroundColor: '#FF6B6B', marginTop: 16 }]}
          onPress={debugStartTutorial}
        >
          <Text style={[styles.startButtonText, { color: '#FFFFFF' }]}>
            DEBUG: Start Tutorial Now
          </Text>
        </TouchableOpacity>
        
        {/* Bottom Message */}
        <Text style={styles.bottomText}>
          Let's take a quick tour to get you started!
        </Text>
      </View>
    </LinearGradient>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 20,
  },
  floatingCircle: {
    position: 'absolute',
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
    borderRadius: 50,
  },
  circle1: {
    width: 100,
    height: 100,
    top: height * 0.1,
    left: width * 0.1,
  },
  circle2: {
    width: 60,
    height: 60,
    top: height * 0.2,
    right: width * 0.15,
  },
  circle3: {
    width: 80,
    height: 80,
    bottom: height * 0.15,
    left: width * 0.2,
  },
  content: {
    alignItems: 'center',
    paddingHorizontal: 20,
    zIndex: 1,
  },
  iconContainer: {
    marginBottom: 30,
    padding: 20,
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    borderRadius: 50,
  },
  title: {
    fontSize: 32,
    fontWeight: 'bold',
    color: '#FFFFFF',
    textAlign: 'center',
    marginBottom: 16,
  },
  subtitle: {
    fontSize: 18,
    color: '#E0E7FF',
    textAlign: 'center',
    lineHeight: 26,
    marginBottom: 40,
    paddingHorizontal: 10,
  },
  featuresContainer: {
    width: '100%',
    marginBottom: 50,
  },
  featureItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 20,
    paddingHorizontal: 20,
  },
  featureText: {
    fontSize: 16,
    color: '#FFFFFF',
    marginLeft: 16,
    fontWeight: '500',
  },
  startButton: {
    backgroundColor: '#FFFFFF',
    paddingVertical: 16,
    paddingHorizontal: 40,
    borderRadius: 30,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: 20,
    minWidth: 200,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 4,
    },
    shadowOpacity: 0.3,
    shadowRadius: 8,
    elevation: 8,
  },
  startButtonDisabled: {
    opacity: 0.7,
  },
  startButtonText: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#3A7BF8',
    marginRight: 8,
  },
  buttonIcon: {
    marginLeft: 4,
  },
  bottomText: {
    fontSize: 14,
    color: '#C7D2FE',
    textAlign: 'center',
    fontStyle: 'italic',
  },
});
