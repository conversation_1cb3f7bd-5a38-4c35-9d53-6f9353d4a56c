import React, { useState } from 'react';
import { useTranslation } from 'react-i18next';
import {
  StyleSheet,
  View,
  Text,
  TouchableOpacity,
  SafeAreaView,
  ActivityIndicator,
  ScrollView,
} from 'react-native';
import { useRouter } from 'expo-router';
import { useAuth } from '../../contexts/AuthContext';
import { doc, setDoc } from 'firebase/firestore';
import { db } from '../../services/firebase';
import { Feather } from '@expo/vector-icons';

// Get travel challenge options with translations
const getChallengeOptions = (t) => [
  { 
    id: 'tooManyOptions', 
    label: t('tripWizard.challenges.options.tooManyOptions'),
    icon: '💭',
    iconColor: '#4285F4'
  },
  { 
    id: 'prioritization', 
    label: t('tripWizard.challenges.options.prioritization'),
    icon: '⏱️',
    iconColor: '#4285F4'
  },
  { 
    id: 'touristTraps', 
    label: t('tripWizard.challenges.options.touristTraps'),
    icon: '🗺️',
    iconColor: '#34A853'
  },
  { 
    id: 'transportation', 
    label: t('tripWizard.challenges.options.transportation'),
    icon: '🚗',
    iconColor: '#EA4335'
  },
  { 
    id: 'valueForMoney', 
    label: t('tripWizard.challenges.options.valueForMoney'),
    icon: '💰',
    iconColor: '#34A853'
  },
];

export default function TravelChallengesScreen() {
  const [selectedChallenges, setSelectedChallenges] = useState<string[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const router = useRouter();
  const { user } = useAuth();
  const { t } = useTranslation();
  
  // Get translated challenge options
  const challengeOptions = getChallengeOptions(t);

  // Toggle challenge selection
  const toggleChallenge = (id: string) => {
    setSelectedChallenges(prev => 
      prev.includes(id)
        ? prev.filter(item => item !== id)
        : [...prev, id]
    );
  };

  // Save challenge preferences and go to next screen
  const handleNext = async () => {
    if (!user || selectedChallenges.length === 0) return;
    
    setIsLoading(true);
    
    try {
      // Save challenge preferences to user profile
      const userDocRef = doc(db, 'users', user.uid);
      
      // Update user document with challenge preferences
      await setDoc(userDocRef, {
        travelChallenges: selectedChallenges,
        updatedAt: new Date().toISOString()
      }, { merge: true });
      
      // Navigate to next screen (final step)
      router.push('/onboarding/travel-preferences');
    } catch (error) {
      console.error('Error saving challenge preferences:', error);
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <SafeAreaView style={styles.container}>
      <ScrollView contentContainerStyle={styles.scrollContent}>
        {/* Progress indicator */}
        <Text style={styles.progressText}>
          {t('tripWizard.progressText', '{{current}}/{{total}}', { current: 5, total: 8 })}
        </Text>
        
        {/* Title and subtitle */}
        <View style={styles.headerContainer}>
          <Text style={styles.title}>
            {t('tripWizard.challenges.question')}
          </Text>
          <Text style={styles.subtitle}>
            {t('tripWizard.challenges.subtitle')}
          </Text>
        </View>
        
        {/* Challenge options */}
        <View style={styles.optionsContainer}>
          {challengeOptions.map((option) => (
            <TouchableOpacity
              key={option.id}
              style={[
                styles.challengeOption,
                selectedChallenges.includes(option.id) && styles.selectedOption
              ]}
              onPress={() => toggleChallenge(option.id)}
              activeOpacity={0.7}
            >
              <View style={[styles.iconContainer, { backgroundColor: option.iconColor + '20' }]}>
                <Text style={styles.challengeIcon}>
                  {option.icon}
                </Text>
              </View>
              
              <Text style={[
                styles.challengeLabel,
                selectedChallenges.includes(option.id) && styles.selectedLabel
              ]}>
                {option.label}
              </Text>
              
              {selectedChallenges.includes(option.id) && (
                <View style={styles.checkContainer}>
                  <Feather name="check" size={16} color="#FFFFFF" />
                </View>
              )}
            </TouchableOpacity>
          ))}
        </View>
        
        {/* Next button */}
        <TouchableOpacity
          style={[
            styles.nextButton,
            selectedChallenges.length === 0 && styles.disabledButton
          ]}
          onPress={handleNext}
          disabled={isLoading || selectedChallenges.length === 0}
        >
          {isLoading ? (
            <ActivityIndicator size="small" color="#FFFFFF" />
          ) : (
            <Text style={styles.nextButtonText}>Next</Text>
          )}
        </TouchableOpacity>
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#FFFFFF',
  },
  scrollContent: {
    flexGrow: 1,
    padding: 24,
  },
  progressText: {
    textAlign: 'center',
    fontSize: 16,
    color: '#6B7280',
    marginBottom: 24,
  },
  headerContainer: {
    marginBottom: 32,
  },
  title: {
    fontSize: 28,
    fontWeight: 'bold',
    color: '#1F2937',
    textAlign: 'center',
    marginBottom: 12,
  },
  subtitle: {
    fontSize: 16,
    color: '#6B7280',
    textAlign: 'center',
    lineHeight: 22,
  },
  optionsContainer: {
    marginBottom: 40,
  },
  challengeOption: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#FFFFFF',
    borderRadius: 16,
    padding: 16,
    marginBottom: 16,
    borderWidth: 1,
    borderColor: '#E5E7EB',
  },
  selectedOption: {
    backgroundColor: '#6366F1',
    borderColor: '#6366F1',
  },
  iconContainer: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 16,
  },
  challengeIcon: {
    fontSize: 20,
  },
  challengeLabel: {
    fontSize: 16,
    color: '#1F2937',
    flex: 1,
    lineHeight: 22,
  },
  selectedLabel: {
    color: '#FFFFFF',
    fontWeight: '500',
  },
  checkContainer: {
    width: 24,
    height: 24,
    borderRadius: 12,
    backgroundColor: 'rgba(255, 255, 255, 0.3)',
    justifyContent: 'center',
    alignItems: 'center',
    marginLeft: 8,
  },
  nextButton: {
    backgroundColor: '#4285F4',
    borderRadius: 28,
    paddingVertical: 16,
    alignItems: 'center',
    justifyContent: 'center',
    marginTop: 'auto',
  },
  disabledButton: {
    backgroundColor: '#A0AEC0',
  },
  nextButtonText: {
    color: '#FFFFFF',
    fontSize: 18,
    fontWeight: '600',
  },
});
