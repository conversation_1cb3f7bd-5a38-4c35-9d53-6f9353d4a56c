import React, { useState } from 'react';
import { useTranslation } from 'react-i18next';
import {
  StyleSheet,
  View,
  Text,
  TouchableOpacity,
  SafeAreaView,
  ActivityIndicator,
} from 'react-native';
import { useRouter } from 'expo-router';
import { useAuth } from '../../contexts/AuthContext';
import { doc, setDoc } from 'firebase/firestore';
import { db } from '../../services/firebase';
import { Feather } from '@expo/vector-icons';

// Budget options - labels will be translated
const budgetOptions = [
  { id: 'low', labelKey: 'tripWizard.budget.lowBudget', icon: '$' },
  { id: 'medium', labelKey: 'tripWizard.budget.mediumBudget', icon: '$$' },
  { id: 'luxury', labelKey: 'tripWizard.budget.luxuryBudget', icon: '$$$' },
];

export default function BudgetScreen() {
  const [selectedBudget, setSelectedBudget] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const router = useRouter();
  const { user } = useAuth();
  const { t } = useTranslation();

  // Select budget option
  const selectBudget = (id: string) => {
    setSelectedBudget(id);
  };

  // Save budget preference and go to next screen
  const handleNext = async () => {
    if (!user || !selectedBudget) return;
    
    setIsLoading(true);
    
    try {
      // Save budget preference to user profile
      const userDocRef = doc(db, 'users', user.uid);
      
      // Update user document with budget preference
      await setDoc(userDocRef, {
        budgetPreference: selectedBudget,
        // Set empty destinations array to ensure data consistency even though we're skipping the destinations screen
        preferredDestinations: [],
        updatedAt: new Date().toISOString()
      }, { merge: true });
      
      // Skip destinations screen and navigate directly to travel-companions
      router.push('/onboarding/travel-companions');
    } catch (error) {
      console.error('Error saving budget preference:', error);
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <SafeAreaView style={styles.container}>
      <View style={styles.content}>
        {/* Progress indicator */}
        <Text style={styles.progressText}>
          {t('tripWizard.progressText', '{{current}}/{{total}}', { current: 2, total: 8 })}
        </Text>
        
        {/* Title */}
        <Text style={styles.title}>
          {t('tripWizard.budget.question', "What's your typical travel budget?")}
        </Text>
        
        {/* Budget options */}
        <View style={styles.optionsContainer}>
          {budgetOptions.map((option) => (
            <TouchableOpacity
              key={option.id}
              style={[
                styles.budgetOption,
                selectedBudget === option.id && styles.selectedOption
              ]}
              onPress={() => selectBudget(option.id)}
              activeOpacity={0.7}
            >
              <View style={styles.iconContainer}>
                <Text style={[
                  styles.budgetIcon,
                  selectedBudget === option.id && styles.selectedIcon
                ]}>
                  {option.icon}
                </Text>
              </View>
              
              <Text style={[
                styles.budgetLabel,
                selectedBudget === option.id && styles.selectedLabel
              ]}>
                {t(option.labelKey, option.labelKey)}
              </Text>
              
              {selectedBudget === option.id && (
                <View style={styles.checkContainer}>
                  <Feather name="check" size={20} color="#FFFFFF" />
                </View>
              )}
            </TouchableOpacity>
          ))}
        </View>
        
        {/* Next button */}
        <TouchableOpacity
          style={[
            styles.nextButton,
            !selectedBudget && styles.disabledButton
          ]}
          onPress={handleNext}
          disabled={isLoading || !selectedBudget}
        >
          {isLoading ? (
            <ActivityIndicator size="small" color="#FFFFFF" />
          ) : (
            <Text style={styles.nextButtonText}>Next</Text>
          )}
        </TouchableOpacity>
      </View>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#FFFFFF',
  },
  content: {
    flex: 1,
    padding: 24,
    justifyContent: 'space-between',
  },
  progressText: {
    textAlign: 'center',
    fontSize: 16,
    color: '#6B7280',
    marginBottom: 24,
  },
  title: {
    fontSize: 28,
    fontWeight: 'bold',
    color: '#1F2937',
    textAlign: 'center',
    marginBottom: 40,
  },
  optionsContainer: {
    marginBottom: 40,
  },
  budgetOption: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#FFFFFF',
    borderRadius: 16,
    padding: 16,
    marginBottom: 16,
    borderWidth: 1,
    borderColor: '#E5E7EB',
    height: 72,
  },
  selectedOption: {
    backgroundColor: '#4285F4',
    borderColor: '#4285F4',
  },
  iconContainer: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: '#F3F4F6',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 16,
  },
  budgetIcon: {
    fontSize: 18,
    color: '#4285F4',
    fontWeight: 'bold',
  },
  selectedIcon: {
    color: '#4285F4',
  },
  budgetLabel: {
    fontSize: 18,
    color: '#1F2937',
    flex: 1,
  },
  selectedLabel: {
    color: '#FFFFFF',
    fontWeight: '500',
  },
  checkContainer: {
    width: 24,
    height: 24,
    borderRadius: 12,
    backgroundColor: 'rgba(255, 255, 255, 0.3)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  nextButton: {
    backgroundColor: '#4285F4',
    borderRadius: 28,
    paddingVertical: 16,
    alignItems: 'center',
    justifyContent: 'center',
  },
  disabledButton: {
    backgroundColor: '#A0AEC0',
  },
  nextButtonText: {
    color: '#FFFFFF',
    fontSize: 18,
    fontWeight: '600',
  },
});
