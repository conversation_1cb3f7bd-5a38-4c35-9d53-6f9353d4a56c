import { Stack } from 'expo-router';
import React from 'react';

export default function OnboardingLayout() {
  return (
    <Stack screenOptions={{ headerShown: false }}>
      <Stack.Screen name="interests" />
      <Stack.Screen name="budget" />
      {/* Destinations screen removed from onboarding flow */}
      <Stack.Screen name="travel-companions" />
      <Stack.Screen name="activity-level" />
      <Stack.Screen name="travel-challenges" />
      <Stack.Screen name="travel-preferences" />
      <Stack.Screen name="language" />
    </Stack>
  );
}
