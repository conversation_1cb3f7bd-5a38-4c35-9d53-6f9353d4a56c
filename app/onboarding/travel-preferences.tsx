import React, { useState } from 'react';
import {
  StyleSheet,
  View,
  Text,
  TouchableOpacity,
  SafeAreaView,
  ScrollView,
  ActivityIndicator,
  Alert,
} from 'react-native';
import { useRouter } from 'expo-router';
import { useAuth } from '../../contexts/AuthContext';
import { doc, setDoc } from 'firebase/firestore';
import { db } from '../../services/firebase';
import { markOnboardingCompleted } from '../../utils/onboarding';
import { useTranslation } from 'react-i18next';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { FontAwesome5, MaterialIcons, Ionicons, MaterialCommunityIcons } from '@expo/vector-icons';

type PreferenceOption = {
  id: string;
  label: string;
  color: string;
  iconComponent: 'FontAwesome5' | 'MaterialIcons' | 'Ionicons' | 'MaterialCommunityIcons';
  iconName: string;
};

// Define travel frequency options with translation keys
const getFrequencyOptions = (t): PreferenceOption[] => [
  { id: 'weekly', label: t('tripWizard.travelPreferences.frequencyOptions.weekly'), color: '#4285F4', iconComponent: 'MaterialIcons', iconName: 'view-week' },
  { id: 'monthly', label: t('tripWizard.travelPreferences.frequencyOptions.monthly'), color: '#EA4335', iconComponent: 'MaterialIcons', iconName: 'calendar-today' },
  { id: 'quarterly', label: t('tripWizard.travelPreferences.frequencyOptions.quarterly'), color: '#FBBC05', iconComponent: 'MaterialIcons', iconName: 'date-range' },
  { id: 'yearly', label: t('tripWizard.travelPreferences.frequencyOptions.yearly'), color: '#34A853', iconComponent: 'MaterialIcons', iconName: 'event' },
];

// Define travel duration options with translation keys
const getDurationOptions = (t): PreferenceOption[] => [
  { id: 'weekend', label: t('tripWizard.travelPreferences.durationOptions.weekend'), color: '#4285F4', iconComponent: 'MaterialIcons', iconName: 'weekend' },
  { id: 'week', label: t('tripWizard.travelPreferences.durationOptions.week'), color: '#EA4335', iconComponent: 'MaterialIcons', iconName: 'view-week' },
  { id: 'twoWeeks', label: t('tripWizard.travelPreferences.durationOptions.twoWeeks'), color: '#FBBC05', iconComponent: 'MaterialIcons', iconName: 'date-range' },
  { id: 'longer', label: t('tripWizard.travelPreferences.durationOptions.longer'), color: '#34A853', iconComponent: 'MaterialIcons', iconName: 'event-note' },
];

// Define travel style options with translation keys
const getStyleOptions = (t): PreferenceOption[] => [
  { id: 'luxury', label: t('tripWizard.travelPreferences.styleOptions.luxury'), color: '#4285F4', iconComponent: 'MaterialIcons', iconName: 'star' },
  { id: 'comfort', label: t('tripWizard.travelPreferences.styleOptions.comfort'), color: '#EA4335', iconComponent: 'MaterialIcons', iconName: 'hotel' },
  { id: 'budget', label: t('tripWizard.travelPreferences.styleOptions.budget'), color: '#FBBC05', iconComponent: 'MaterialIcons', iconName: 'account-balance-wallet' },
  { id: 'backpacking', label: t('tripWizard.travelPreferences.styleOptions.backpacking'), color: '#34A853', iconComponent: 'MaterialIcons', iconName: 'backpack' },
];

// Helper function to render the appropriate icon based on the preference option
const renderPreferenceIcon = (option: PreferenceOption, isSelected: boolean) => {
  const iconColor = isSelected ? option.color : '#FFFFFF';
  const backgroundColor = isSelected ? '#FFFFFF' : option.color;
  const iconSize = 20;
  
  return (
    <View style={[styles.iconContainer, { backgroundColor }]}>
      {option.iconComponent === 'FontAwesome5' && (
        <FontAwesome5 name={option.iconName as any} size={iconSize} color={iconColor} />
      )}
      {option.iconComponent === 'MaterialIcons' && (
        <MaterialIcons name={option.iconName as any} size={iconSize} color={iconColor} />
      )}
      {option.iconComponent === 'Ionicons' && (
        <Ionicons name={option.iconName as any} size={iconSize} color={iconColor} />
      )}
      {option.iconComponent === 'MaterialCommunityIcons' && (
        <MaterialCommunityIcons name={option.iconName as any} size={iconSize} color={iconColor} />
      )}
    </View>
  );
};

export default function TravelPreferencesScreen() {
  const [frequency, setFrequency] = useState<string | null>(null);
  const [duration, setDuration] = useState<string | null>(null);
  const [style, setStyle] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const router = useRouter();
  const { user } = useAuth();
  const { t } = useTranslation();
  
  // Get translated options
  const frequencyOptions = getFrequencyOptions(t);
  const durationOptions = getDurationOptions(t);
  const styleOptions = getStyleOptions(t);

  // Select frequency option
  const selectFrequency = (id: string) => {
    setFrequency(id);
  };

  // Select duration option
  const selectDuration = (id: string) => {
    setDuration(id);
  };

  // Select style option
  const selectStyle = (id: string) => {
    setStyle(id);
  };

  // Save preferences to Firestore and proceed to language selection (final step)
  const handleFinish = async () => {
    // Require all selections before proceeding
    if (!frequency || !duration || !style) {
      Alert.alert(
        t('tripWizard.travelPreferences.selectionRequired.title', 'Selection Required'),
        t('tripWizard.travelPreferences.selectionRequired.message', 'Please select an option for each category before continuing.')
      );
      return;
    }

    if (!user) {
      console.error('[TravelPreferencesScreen] Cannot proceed: No authenticated user');
      Alert.alert('Error', 'You must be logged in to continue.');
      return;
    }
    
    setIsLoading(true);
    
    try {
      console.log('[TravelPreferencesScreen] Saving travel preferences');
      
      // Save travel preferences to user profile
      const userDocRef = doc(db, 'users', user.uid);
      
      // Update user document with travel preferences (but don't mark onboarding as completed yet)
      await setDoc(userDocRef, {
        travelPreferences: {
          frequency: frequency,
          duration: duration,
          style: style,
        },
        updatedAt: new Date().toISOString()
      }, { merge: true });
      
      console.log('[TravelPreferencesScreen] Updated user profile in Firestore with travel preferences');
      
      // Now proceed to language selection (final step)
      console.log('[TravelPreferencesScreen] Proceeding to language selection (final onboarding step)');
      
      // Navigate to language screen using replace to avoid stacking screens
      router.replace('/onboarding/language');
    } catch (error) {
      console.error('[TravelPreferencesScreen] Error saving travel preferences:', error);
      Alert.alert('Error', 'There was a problem saving your preferences. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  // Go back to previous screen
  const handleBack = () => {
    router.back();
  };

  return (
    <SafeAreaView style={styles.container}>
      <ScrollView contentContainerStyle={styles.scrollContent}>
        {/* Progress indicator - updated after removing destinations step */}
        <Text style={styles.progressText}>
          {t('tripWizard.progressText', '{{current}}/{{total}}', { current: 7, total: 8 })}
        </Text>
        
        {/* Title and subtitle */}
        <View style={styles.headerContainer}>
          <Text style={styles.title}>
            {t('tripWizard.travelPreferences.title')}
          </Text>
          <Text style={styles.subtitle}>
            {t('tripWizard.travelPreferences.subtitle')}
          </Text>
        </View>
        
        {/* Frequency section */}
        <View style={styles.sectionContainer}>
          <Text style={styles.sectionTitle}>{t('tripWizard.travelPreferences.frequency')}</Text>
          <View style={styles.optionsRow}>
            {frequencyOptions.map((option) => (
              <TouchableOpacity
                key={option.id}
                style={[
                  styles.optionButton,
                  frequency === option.id && styles.selectedOption
                ]}
                onPress={() => selectFrequency(option.id)}
                activeOpacity={0.7}
              >
                {renderPreferenceIcon(option, frequency === option.id)}
                <Text
                  style={[
                    styles.optionLabel,
                    frequency === option.id && styles.selectedLabel
                  ]}
                >
                  {option.label}
                </Text>
              </TouchableOpacity>
            ))}
          </View>
        </View>
        
        {/* Duration section */}
        <View style={styles.sectionContainer}>
          <Text style={styles.sectionTitle}>{t('tripWizard.travelPreferences.duration')}</Text>
          <View style={styles.optionsRow}>
            {durationOptions.map((option) => (
              <TouchableOpacity
                key={option.id}
                style={[
                  styles.optionButton,
                  duration === option.id && styles.selectedOption
                ]}
                onPress={() => selectDuration(option.id)}
                activeOpacity={0.7}
              >
                {renderPreferenceIcon(option, duration === option.id)}
                <Text
                  style={[
                    styles.optionLabel,
                    duration === option.id && styles.selectedLabel
                  ]}
                >
                  {option.label}
                </Text>
              </TouchableOpacity>
            ))}
          </View>
        </View>
        
        {/* Style section */}
        <View style={styles.sectionContainer}>
          <Text style={styles.sectionTitle}>{t('tripWizard.travelPreferences.style')}</Text>
          <View style={styles.optionsRow}>
            {styleOptions.map((option) => (
              <TouchableOpacity
                key={option.id}
                style={[
                  styles.optionButton,
                  style === option.id && styles.selectedOption
                ]}
                onPress={() => selectStyle(option.id)}
                activeOpacity={0.7}
              >
                {renderPreferenceIcon(option, style === option.id)}
                <Text
                  style={[
                    styles.optionLabel,
                    style === option.id && styles.selectedLabel
                  ]}
                >
                  {option.label}
                </Text>
              </TouchableOpacity>
            ))}
          </View>
        </View>
        
        {/* Navigation buttons */}
        <View style={styles.buttonsContainer}>
          <TouchableOpacity
            style={styles.backButton}
            onPress={handleBack}
          >
            <Text style={styles.backButtonText}>{t('tripWizard.travelPreferences.back')}</Text>
          </TouchableOpacity>
          
          <TouchableOpacity
            style={[
              styles.nextButton,
              (!frequency || !duration || !style) && styles.disabledButton
            ]}
            onPress={handleFinish}
            disabled={isLoading || !frequency || !duration || !style}
          >
            {isLoading ? (
              <ActivityIndicator size="small" color="#FFFFFF" />
            ) : (
              <Text style={styles.nextButtonText}>{t('tripWizard.travelPreferences.finish')}</Text>
            )}
          </TouchableOpacity>
        </View>
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  iconContainer: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 8,
  },
  container: {
    flex: 1,
    backgroundColor: '#F8F9FA',
  },
  scrollContent: {
    flexGrow: 1,
    padding: 24,
  },
  progressText: {
    textAlign: 'center',
    fontSize: 16,
    color: '#6B7280',
    marginBottom: 24,
  },
  headerContainer: {
    marginBottom: 32,
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#1F2937',
    textAlign: 'center',
    marginBottom: 12,
  },
  subtitle: {
    fontSize: 16,
    color: '#6B7280',
    textAlign: 'center',
  },
  sectionContainer: {
    marginBottom: 24,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#1F2937',
    marginBottom: 16,
  },
  optionsRow: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
  },
  optionButton: {
    width: '48%',
    backgroundColor: '#FFFFFF',
    borderRadius: 12,
    padding: 16,
    marginBottom: 12,
    alignItems: 'center',
    borderWidth: 1,
    borderColor: '#E5E7EB',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  selectedOption: {
    backgroundColor: '#4285F4',
    borderColor: '#4285F4',
    shadowColor: '#4285F4',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.3,
    shadowRadius: 3,
    elevation: 4,
  },
  optionIcon: {
    marginBottom: 8,
  },
  optionLabel: {
    fontSize: 14,
    color: '#1F2937',
    fontWeight: '500',
    textAlign: 'center',
  },
  selectedLabel: {
    color: '#FFFFFF',
  },
  buttonsContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginTop: 'auto',
    paddingTop: 24,
  },
  backButton: {
    backgroundColor: '#FFFFFF',
    borderRadius: 12,
    paddingVertical: 16,
    paddingHorizontal: 24,
    alignItems: 'center',
    justifyContent: 'center',
    borderWidth: 1,
    borderColor: '#E5E7EB',
    flex: 1,
    marginRight: 12,
  },
  backButtonText: {
    color: '#4B5563',
    fontSize: 16,
    fontWeight: '600',
  },
  nextButton: {
    backgroundColor: '#4285F4',
    borderRadius: 12,
    paddingVertical: 16,
    paddingHorizontal: 24,
    alignItems: 'center',
    justifyContent: 'center',
    flex: 1,
  },
  disabledButton: {
    backgroundColor: '#A0AEC0',
  },
  nextButtonText: {
    color: '#FFFFFF',
    fontSize: 16,
    fontWeight: '600',
  },
});
