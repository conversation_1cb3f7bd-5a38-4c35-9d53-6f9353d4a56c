import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import {
  StyleSheet,
  View,
  Text,
  TouchableOpacity,
  SafeAreaView,
  ActivityIndicator,
} from 'react-native';
import { useRouter } from 'expo-router';
import { useAuth } from '../../contexts/AuthContext';
import { doc, setDoc, getDoc } from 'firebase/firestore';
import { db } from '../../services/firebase';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { Feather, FontAwesome5, MaterialIcons } from '@expo/vector-icons';

type CompanionOption = {
  id: string;
  labelKey: string;
  color: string;
  iconComponent: 'FontAwesome5' | 'MaterialIcons';
  iconName: string;
};

// Travel companion options - labels will be translated
const companionOptions: CompanionOption[] = [
  { id: 'solo', labelKey: 'tripWizard.travelGroup.solo', color: '#4285F4', iconComponent: 'MaterialIcons', iconName: 'person' },
  { id: 'couple', labelKey: 'tripWizard.travelGroup.couple', color: '#4285F4', iconComponent: 'MaterialIcons', iconName: 'people' },
  { id: 'friends', labelKey: 'tripWizard.travelGroup.friends', color: '#4285F4', iconComponent: 'MaterialIcons', iconName: 'groups' },
  { id: 'family', labelKey: 'tripWizard.travelGroup.familyWithKids', color: '#4285F4', iconComponent: 'MaterialIcons', iconName: 'family-restroom' },
];

export default function TravelCompanionsScreen() {
  const [selectedCompanion, setSelectedCompanion] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [isEditMode, setIsEditMode] = useState(false);
  const router = useRouter();
  const { user } = useAuth();
  const { t } = useTranslation();

  // Check if we're in edit mode (coming from profile)
  useEffect(() => {
    const checkEditMode = async () => {
      if (user) {
        try {
          // Check if we're coming from edit profile
          const editMode = await AsyncStorage.getItem('editProfileMode');
          if (editMode === 'true') {
            setIsEditMode(true);
            
            // Fetch existing preference
            const userDocRef = doc(db, 'users', user.uid);
            const userDoc = await getDoc(userDocRef);
            
            if (userDoc.exists() && userDoc.data().travelCompanion) {
              setSelectedCompanion(userDoc.data().travelCompanion);
            }
          }
        } catch (error) {
          console.error('Error checking edit mode:', error);
        }
      }
    };
    
    checkEditMode();
  }, [user]);

  // Select a travel companion option
  const selectCompanion = (id: string) => {
    setSelectedCompanion(id);
  };

  // Save companion preference and go to next screen or back to profile
  const handleNext = async () => {
    if (!user || !selectedCompanion) return;
    
    setIsLoading(true);
    
    try {
      // Save companion preference to user profile
      const userDocRef = doc(db, 'users', user.uid);
      
      // Update user document with companion preference
      await setDoc(userDocRef, {
        travelCompanion: selectedCompanion,
        updatedAt: new Date().toISOString()
      }, { merge: true });
      
      if (isEditMode) {
        // Clear edit mode flag and return to profile
        await AsyncStorage.setItem('editProfileMode', 'false');
        router.back();
      } else {
        // Navigate to next screen in onboarding flow
        router.push('/onboarding/activity-level');
      }
    } catch (error) {
      console.error('Error saving companion preference:', error);
    } finally {
      setIsLoading(false);
    }
  };

  // Go back to previous screen
  const handleBack = () => {
    router.back();
  };

  return (
    <SafeAreaView style={styles.container}>
      <View style={styles.content}>
        {/* Progress indicator */}
        <Text style={styles.progressText}>
          {t('tripWizard.progressText', '{{current}}/{{total}}', { current: 3, total: 8 })}
        </Text>
        
        {/* Title and subtitle */}
        <View style={styles.headerContainer}>
          <Text style={styles.title}>
            {t('tripWizard.travelGroup.question')}
          </Text>
          <Text style={styles.subtitle}>
            {t('tripWizard.travelGroup.subtitle')}
          </Text>
        </View>
        
        {/* Companion options */}
        <View style={styles.optionsContainer}>
          {companionOptions.map((option) => (
            <TouchableOpacity
              key={option.id}
              style={[
                styles.companionOption,
                selectedCompanion === option.id && styles.selectedOption
              ]}
              onPress={() => selectCompanion(option.id)}
              activeOpacity={0.7}
            >
              <View style={[styles.iconContainer, { backgroundColor: option.color }]}>
                {option.iconComponent === 'MaterialIcons' ? (
                  <MaterialIcons name={option.iconName as any} size={22} color="#FFFFFF" />
                ) : (
                  <FontAwesome5 name={option.iconName as any} size={20} color="#FFFFFF" />
                )}
              </View>
              
              <Text style={[
                styles.companionLabel,
                selectedCompanion === option.id && styles.selectedLabel
              ]}>
                {t(option.labelKey)}
              </Text>
              
              {selectedCompanion === option.id && (
                <Feather name="check" size={20} color="#4285F4" style={styles.checkIcon} />
              )}
            </TouchableOpacity>
          ))}
        </View>
        
        {/* Next button */}
        <TouchableOpacity
          style={[
            styles.nextButton,
            !selectedCompanion && styles.disabledButton
          ]}
          onPress={handleNext}
          disabled={isLoading || !selectedCompanion}
        >
          {isLoading ? (
            <ActivityIndicator size="small" color="#FFFFFF" />
          ) : (
            <Text style={styles.nextButtonText}>{isEditMode ? 'Save' : 'Next'}</Text>
          )}
        </TouchableOpacity>
      </View>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#FFFFFF',
  },
  content: {
    flex: 1,
    padding: 24,
  },
  progressText: {
    textAlign: 'center',
    fontSize: 16,
    color: '#6B7280',
    marginBottom: 24,
  },
  headerContainer: {
    marginBottom: 32,
  },
  title: {
    fontSize: 28,
    fontWeight: 'bold',
    color: '#1F2937',
    textAlign: 'center',
    marginBottom: 12,
  },
  subtitle: {
    fontSize: 16,
    color: '#6B7280',
    textAlign: 'center',
    lineHeight: 22,
  },
  optionsContainer: {
    marginBottom: 40,
  },
  companionOption: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#FFFFFF',
    borderRadius: 16,
    padding: 16,
    marginBottom: 16,
    borderWidth: 1,
    borderColor: '#E5E7EB',
  },
  selectedOption: {
    backgroundColor: '#F0F7FF',
    borderColor: '#4285F4',
  },
  iconContainer: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 16,
  },

  companionLabel: {
    fontSize: 18,
    color: '#1F2937',
    flex: 1,
  },
  selectedLabel: {
    color: '#1F2937',
    fontWeight: '500',
  },
  checkIcon: {
    marginLeft: 8,
  },
  nextButton: {
    backgroundColor: '#4285F4',
    borderRadius: 28,
    paddingVertical: 16,
    alignItems: 'center',
    justifyContent: 'center',
    marginTop: 'auto',
  },
  disabledButton: {
    backgroundColor: '#A0AEC0',
  },
  nextButtonText: {
    color: '#FFFFFF',
    fontSize: 18,
    fontWeight: '600',
  },
});
