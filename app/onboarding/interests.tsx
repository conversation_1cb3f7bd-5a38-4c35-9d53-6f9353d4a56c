import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import {
  StyleSheet,
  View,
  Text,
  TouchableOpacity,
  SafeAreaView,
  ScrollView,
  ActivityIndicator,
  Alert,
} from 'react-native';
import { useRouter } from 'expo-router';
import { useAuth } from '../../contexts/AuthContext';
import { doc, setDoc, getDoc } from 'firebase/firestore';
import { db } from '../../services/firebase';
import { markOnboardingCompleted } from '../../utils/onboarding';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { FontAwesome5, MaterialIcons, Ionicons, MaterialCommunityIcons } from '@expo/vector-icons';

type InterestOption = {
  id: string;
  label: string;
  color: string;
  iconComponent: 'FontAwesome5' | 'MaterialIcons' | 'Ionicons' | 'MaterialCommunityIcons';
  iconName: string;
};

// Define interest options - will be populated with translations
const getInterestOptions = (t): InterestOption[] => [
  { id: 'history', label: t('tripWizard.interests.history', 'History'), color: '#4285F4', iconComponent: 'MaterialIcons', iconName: 'history' },
  { id: 'art', label: t('tripWizard.interests.art', 'Art'), color: '#EA4335', iconComponent: 'MaterialIcons', iconName: 'palette' },
  { id: 'gastronomy', label: t('tripWizard.interests.gastronomy', 'Gastronomy'), color: '#FBBC05', iconComponent: 'MaterialIcons', iconName: 'restaurant' },
  { id: 'fashion', label: t('tripWizard.interests.fashion', 'Fashion'), color: '#34A853', iconComponent: 'MaterialIcons', iconName: 'shopping-bag' },
  { id: 'nature', label: t('tripWizard.interests.nature', 'Nature'), color: '#4285F4', iconComponent: 'MaterialIcons', iconName: 'terrain' },
  { id: 'sports', label: t('tripWizard.interests.sportsEvents', 'Sports Events'), color: '#EA4335', iconComponent: 'MaterialIcons', iconName: 'sports-basketball' },
  { id: 'sightseeing', label: t('tripWizard.interests.sightseeing', 'Sightseeing'), color: '#FBBC05', iconComponent: 'MaterialIcons', iconName: 'photo-camera' },
  { id: 'nightlife', label: t('tripWizard.interests.nightlife', 'Nightlife'), color: '#34A853', iconComponent: 'MaterialIcons', iconName: 'nightlife' },
  { id: 'relaxation', label: t('tripWizard.interests.relaxation', 'Relaxation'), color: '#4285F4', iconComponent: 'MaterialIcons', iconName: 'spa' },
];

// Helper function to render the appropriate icon based on the interest option
const renderInterestIcon = (interest: InterestOption, isSelected: boolean) => {
  const iconColor = isSelected ? interest.color : '#FFFFFF';
  const iconSize = 18;
  
  switch (interest.iconComponent) {
    case 'FontAwesome5':
      return <FontAwesome5 name={interest.iconName as any} size={iconSize} color={iconColor} />;
    case 'MaterialIcons':
      return <MaterialIcons name={interest.iconName as any} size={iconSize} color={iconColor} />;
    case 'Ionicons':
      return <Ionicons name={interest.iconName as any} size={iconSize} color={iconColor} />;
    case 'MaterialCommunityIcons':
      return <MaterialCommunityIcons name={interest.iconName as any} size={iconSize} color={iconColor} />;
    default:
      return null;
  }
};

export default function InterestsScreen() {
  const [selectedInterests, setSelectedInterests] = useState<string[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const router = useRouter();
  const { user } = useAuth();
  const { t } = useTranslation();
  
  // Get translated interest options
  const interestOptions = getInterestOptions(t);
  
  // Interests is now the first screen in the onboarding flow
  useEffect(() => {
    console.log('[InterestsScreen] Interests is now the first screen in the onboarding flow');
    
    // Clean up any old redirect flags that might exist from previous versions
    const cleanupFlags = async () => {
      try {
        await AsyncStorage.removeItem('REDIRECTING_TO_LANGUAGE');
      } catch (error) {
        console.error('[InterestsScreen] Error cleaning up flags:', error);
      }
    };
    
    cleanupFlags();
  }, []);

  // Toggle interest selection
  const toggleInterest = (interestId: string) => {
    setSelectedInterests(prev => 
      prev.includes(interestId)
        ? prev.filter(id => id !== interestId)
        : [...prev, interestId]
    );
  };

  // Save interests to Firestore and continue to the next onboarding step
  const handleNext = async () => {
    // Require at least one interest to be selected
    if (selectedInterests.length === 0) {
      Alert.alert(
        t('tripWizard.interests.selectionRequired.title', 'Selection Required'),
        t('tripWizard.interests.selectionRequired.message', 'Please select at least one interest before continuing.')
      );
      return;
    }

    if (!user) {
      console.error('[InterestsScreen] Cannot proceed: No authenticated user');
      Alert.alert('Error', 'You must be logged in to continue.');
      return;
    }
    
    // No need to check for language selection since it's now the last step
    
    setIsLoading(true);
    
    try {
      console.log('[InterestsScreen] Saving interests and proceeding to next step');
      
      // Save selected interests to user profile
      const userDocRef = doc(db, 'users', user.uid);
      
      // Update user document with interests
      await setDoc(userDocRef, {
        interests: selectedInterests,
        updatedAt: new Date().toISOString(),
        // Ensure onboarding is still marked as incomplete
        onboardingCompleted: false
      }, { merge: true });
      
      console.log('[InterestsScreen] Interests saved, proceeding to step 3 of 8: Budget');
      
      // Navigate to the next onboarding screen - use replace to avoid navigation stack issues
      router.replace('/onboarding/budget');
    } catch (error) {
      console.error('[InterestsScreen] Error saving interests:', error);
      Alert.alert('Error', 'There was a problem saving your interests. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <SafeAreaView style={styles.container}>
      <ScrollView contentContainerStyle={styles.scrollContent}>
        {/* Progress indicator */}
        <Text style={styles.progressText}>
          {t('tripWizard.progressText', '{{current}}/{{total}}', { current: 1, total: 8 })}
        </Text>
        
        {/* Title and subtitle */}
        <View style={styles.headerContainer}>
          <Text style={styles.title}>
            {t('tripWizard.interestsQuestion', 'What are your travel interests?')}
          </Text>
          <Text style={styles.subtitle}>
            {t('tripWizard.interestsSubtitle', 'We\'ll match your interests with local activities.')}
          </Text>
        </View>
        
        {/* Interest options grid */}
        <View style={styles.optionsContainer}>
          {interestOptions.map((interest) => (
            <TouchableOpacity
              key={interest.id}
              style={[
                styles.interestButton,
                selectedInterests.includes(interest.id) && styles.selectedButton
              ]}
              onPress={() => toggleInterest(interest.id)}
              activeOpacity={0.7}
            >
              <View style={[styles.initialContainer, { backgroundColor: selectedInterests.includes(interest.id) ? '#FFFFFF' : interest.color }]}>
                {renderInterestIcon(interest, selectedInterests.includes(interest.id))}
              </View>
              <Text
                style={[
                  styles.interestLabel,
                  selectedInterests.includes(interest.id) && styles.selectedLabel
                ]}
              >
                {interest.label}
              </Text>
              {selectedInterests.includes(interest.id) && (
                <View style={styles.checkmarkContainer}>
                  <Text style={styles.checkmark}>✓</Text>
                </View>
              )}
            </TouchableOpacity>
          ))}
        </View>
        
        {/* Next button */}
        <TouchableOpacity
          style={[
            styles.nextButton,
            selectedInterests.length === 0 && styles.disabledButton
          ]}
          onPress={handleNext}
          disabled={isLoading || selectedInterests.length === 0}
        >
          {isLoading ? (
            <ActivityIndicator size="small" color="#FFFFFF" />
          ) : (
            <Text style={styles.nextButtonText}>Next</Text>
          )}
        </TouchableOpacity>
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F8F9FA',
  },
  scrollContent: {
    flexGrow: 1,
    padding: 24,
  },
  progressText: {
    textAlign: 'center',
    fontSize: 16,
    color: '#6B7280',
    marginBottom: 24,
  },
  headerContainer: {
    marginBottom: 32,
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#1F2937',
    textAlign: 'center',
    marginBottom: 12,
  },
  subtitle: {
    fontSize: 16,
    color: '#6B7280',
    textAlign: 'center',
  },
  optionsContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
    marginBottom: 40,
  },
  interestButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#FFFFFF',
    borderRadius: 12,
    paddingVertical: 12,
    paddingHorizontal: 16,
    marginBottom: 12,
    width: '48%',
    borderWidth: 1,
    borderColor: '#E5E7EB',
    position: 'relative',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  selectedButton: {
    backgroundColor: '#4285F4',
    borderColor: '#4285F4',
    shadowColor: '#4285F4',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.3,
    shadowRadius: 3,
    elevation: 4,
  },
  interestLabel: {
    fontSize: 14,
    color: '#1F2937',
    marginLeft: 8,
    fontWeight: '500',
  },
  selectedLabel: {
    color: '#FFFFFF',
  },
  checkmarkContainer: {
    position: 'absolute',
    top: 8,
    right: 8,
    backgroundColor: 'rgba(255, 255, 255, 0.3)',
    borderRadius: 10,
    width: 20,
    height: 20,
    alignItems: 'center',
    justifyContent: 'center',
  },
  checkmark: {
    color: '#FFFFFF',
    fontSize: 14,
    fontWeight: 'bold',
  },
  initialContainer: {
    width: 30,
    height: 30,
    borderRadius: 15,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 8,
  },

  nextButton: {
    backgroundColor: '#4285F4',
    borderRadius: 12,
    paddingVertical: 16,
    alignItems: 'center',
    justifyContent: 'center',
    marginTop: 'auto',
  },
  disabledButton: {
    backgroundColor: '#A0AEC0',
  },
  nextButtonText: {
    color: '#FFFFFF',
    fontSize: 16,
    fontWeight: '600',
  },
});
