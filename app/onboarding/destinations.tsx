import React, { useState } from 'react';
import { useTranslation } from 'react-i18next';
import {
  StyleSheet,
  View,
  Text,
  TouchableOpacity,
  SafeAreaView,
  ScrollView,
  ActivityIndicator,
  Image,
} from 'react-native';
import { useRouter } from 'expo-router';
import { useAuth } from '../../contexts/AuthContext';
import { doc, setDoc } from 'firebase/firestore';
import { db } from '../../services/firebase';
import { markOnboardingCompleted } from '../../utils/onboarding';
// No longer using PlaceholderImage component

// Define destination types with translation keys
const getDestinationTypes = (t) => [
  { id: 'beach', label: t('tripWizard.destinations.beach', 'Beach Destinations'), color: '#4285F4', initials: 'BD' },
  { id: 'city', label: t('tripWizard.destinations.city', 'City Breaks'), color: '#EA4335', initials: 'CB' },
  { id: 'mountain', label: t('tripWizard.destinations.mountain', 'Mountain Retreats'), color: '#FBBC05', initials: 'MR' },
  { id: 'countryside', label: t('tripWizard.destinations.countryside', 'Countryside'), color: '#34A853', initials: 'C' },
  { id: 'island', label: t('tripWizard.destinations.island', 'Islands'), color: '#4285F4', initials: 'I' },
  { id: 'historic', label: t('tripWizard.destinations.historic', 'Historic Sites'), color: '#EA4335', initials: 'HS' },
];

export default function DestinationsScreen() {
  const [selectedDestinations, setSelectedDestinations] = useState<string[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const router = useRouter();
  const { user } = useAuth();
  const { t } = useTranslation();
  
  // Get translated destination types
  const destinationTypes = getDestinationTypes(t);

  // Toggle destination selection
  const toggleDestination = (destinationId: string) => {
    setSelectedDestinations(prev => 
      prev.includes(destinationId)
        ? prev.filter(id => id !== destinationId)
        : [...prev, destinationId]
    );
  };

  // Save destinations to Firestore and proceed to next screen
  const handleNext = async () => {
    if (!user) return;
    
    setIsLoading(true);
    
    try {
      // Save selected destinations to user profile
      const userDocRef = doc(db, 'users', user.uid);
      
      // Update user document with destinations
      await setDoc(userDocRef, {
        preferredDestinations: selectedDestinations,
        updatedAt: new Date().toISOString()
      }, { merge: true });
      
      // Navigate to the next onboarding screen
      router.push('/onboarding/travel-companions');
    } catch (error) {
      console.error('Error saving destinations:', error);
    } finally {
      setIsLoading(false);
    }
  };

  // Go back to previous screen
  const handleBack = () => {
    router.back();
  };

  return (
    <SafeAreaView style={styles.container}>
      <ScrollView contentContainerStyle={styles.scrollContent}>
        {/* Progress indicator */}
        <Text style={styles.progressText}>
          {t('tripWizard.progressText', '{{current}}/{{total}}', { current: 4, total: 8 })}
        </Text>
        
        {/* Title and subtitle */}
        <View style={styles.headerContainer}>
          <Text style={styles.title}>
            {t('tripWizard.destinations.question', 'What types of destinations do you prefer?')}
          </Text>
          <Text style={styles.subtitle}>
            {t('tripWizard.destinations.subtitle', 'We\'ll suggest trips that match your preferences.')}
          </Text>
        </View>
        
        {/* Destination options */}
        <View style={styles.optionsContainer}>
          {destinationTypes.map((destination) => (
            <TouchableOpacity
              key={destination.id}
              style={[
                styles.destinationCard,
                selectedDestinations.includes(destination.id) && styles.selectedCard
              ]}
              onPress={() => toggleDestination(destination.id)}
              activeOpacity={0.7}
            >
              <View style={styles.imageContainer}>
                <View style={[styles.placeholderContainer, { backgroundColor: destination.color }]}>
                  <Text style={styles.initialText}>{destination.initials}</Text>
                </View>
                {selectedDestinations.includes(destination.id) && (
                  <View style={styles.checkmarkOverlay}>
                    <Text style={styles.checkmark}>✓</Text>
                  </View>
                )}
              </View>
              <View style={styles.destinationLabelContainer}>
                <Text style={styles.destinationLabel}>
                  {destination.label}
                </Text>
              </View>
            </TouchableOpacity>
          ))}
        </View>
        
        {/* Navigation buttons */}
        <View style={styles.buttonsContainer}>
          <TouchableOpacity
            style={styles.backButton}
            onPress={handleBack}
          >
            <Text style={styles.backButtonText}>{t('common.back', 'Back')}</Text>
          </TouchableOpacity>
          
          <TouchableOpacity
            style={styles.nextButton}
            onPress={handleNext}
            disabled={isLoading}
          >
            {isLoading ? (
              <ActivityIndicator size="small" color="#FFFFFF" />
            ) : (
              <Text style={styles.nextButtonText}>Next</Text>
            )}
          </TouchableOpacity>
        </View>
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F8F9FA',
  },
  scrollContent: {
    flexGrow: 1,
    padding: 24,
  },
  progressText: {
    textAlign: 'center',
    fontSize: 16,
    color: '#6B7280',
    marginBottom: 24,
  },
  headerContainer: {
    marginBottom: 32,
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#1F2937',
    textAlign: 'center',
    marginBottom: 12,
  },
  subtitle: {
    fontSize: 16,
    color: '#6B7280',
    textAlign: 'center',
  },
  optionsContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
    marginBottom: 40,
  },
  destinationCard: {
    width: '48%',
    marginBottom: 16,
    borderRadius: 12,
    backgroundColor: '#FFFFFF',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
    overflow: 'hidden',
  },
  selectedCard: {
    borderWidth: 2,
    borderColor: '#4285F4',
  },
  imageContainer: {
    width: '100%',
    height: 120,
    position: 'relative',
  },
  placeholderContainer: {
    width: '100%',
    height: '100%',
    justifyContent: 'center',
    alignItems: 'center',
  },
  initialText: {
    color: '#FFFFFF',
    fontSize: 36,
    fontWeight: 'bold',
  },
  destinationImage: {
    width: '100%',
    height: '100%',
  },
  checkmarkOverlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: 'rgba(66, 133, 244, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  checkmark: {
    color: '#FFFFFF',
    fontSize: 24,
    fontWeight: 'bold',
  },
  destinationLabelContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 12,
  },
  destinationIcon: {
    marginRight: 8,
  },
  destinationLabel: {
    fontSize: 14,
    color: '#1F2937',
    fontWeight: '500',
    flex: 1,
  },
  buttonsContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginTop: 'auto',
  },
  backButton: {
    backgroundColor: '#FFFFFF',
    borderRadius: 12,
    paddingVertical: 16,
    paddingHorizontal: 24,
    alignItems: 'center',
    justifyContent: 'center',
    borderWidth: 1,
    borderColor: '#E5E7EB',
    flex: 1,
    marginRight: 12,
  },
  backButtonText: {
    color: '#4B5563',
    fontSize: 16,
    fontWeight: '600',
  },
  nextButton: {
    backgroundColor: '#4285F4',
    borderRadius: 12,
    paddingVertical: 16,
    paddingHorizontal: 24,
    alignItems: 'center',
    justifyContent: 'center',
    flex: 1,
  },
  nextButtonText: {
    color: '#FFFFFF',
    fontSize: 16,
    fontWeight: '600',
  },
});
