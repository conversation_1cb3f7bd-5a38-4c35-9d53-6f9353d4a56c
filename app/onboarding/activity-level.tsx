import React, { useState } from 'react';
import { useTranslation } from 'react-i18next';
import {
  StyleSheet,
  View,
  Text,
  TouchableOpacity,
  SafeAreaView,
  ActivityIndicator,
} from 'react-native';
import { useRouter } from 'expo-router';
import { useAuth } from '../../contexts/AuthContext';
import { doc, setDoc } from 'firebase/firestore';
import { db } from '../../services/firebase';
import { Feather } from '@expo/vector-icons';

// Activity level options
const activityOptions = [
  { id: 'relax', labelKey: 'tripWizard.activityLevel.relax', descriptionKey: 'tripWizard.activityLevel.relaxDescription', icon: '🏖️' },
  { id: 'medium', labelKey: 'tripWizard.activityLevel.mediumActivity', descriptionKey: 'tripWizard.activityLevel.moderateDescription', icon: '🚶' },
  { id: 'active', labelKey: 'tripWizard.activityLevel.veryActive', descriptionKey: 'tripWizard.activityLevel.activeDescription', icon: '🏃' },
];

export default function ActivityLevelScreen() {
  const [selectedActivity, setSelectedActivity] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const router = useRouter();
  const { user } = useAuth();
  const { t } = useTranslation();

  // Select activity level
  const selectActivity = (id: string) => {
    setSelectedActivity(id);
  };

  // Save activity preference and go to next screen
  const handleNext = async () => {
    if (!user || !selectedActivity) return;
    
    setIsLoading(true);
    
    try {
      // Save activity preference to user profile
      const userDocRef = doc(db, 'users', user.uid);
      
      // Update user document with activity preference
      await setDoc(userDocRef, {
        activityLevel: selectedActivity,
        updatedAt: new Date().toISOString()
      }, { merge: true });
      
      // Navigate to next screen
      router.push('/onboarding/travel-challenges');
    } catch (error) {
      console.error('Error saving activity preference:', error);
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <SafeAreaView style={styles.container}>
      <View style={styles.content}>
        {/* Progress indicator */}
        <Text style={styles.progressText}>
          {t('tripWizard.progressText', '{{current}}/{{total}}', { current: 4, total: 8 })}
        </Text>
        
        {/* Title and subtitle */}
        <View style={styles.headerContainer}>
          <Text style={styles.title}>
            {t('tripWizard.activityLevel.question')}
          </Text>
          <Text style={styles.subtitle}>
            {t('tripWizard.activityLevel.subtitle')}
          </Text>
        </View>
        
        {/* Activity options */}
        <View style={styles.optionsContainer}>
          {activityOptions.map((option) => (
            <TouchableOpacity
              key={option.id}
              style={[
                styles.activityOption,
                selectedActivity === option.id && styles.selectedOption
              ]}
              onPress={() => selectActivity(option.id)}
              activeOpacity={0.7}
            >
              <View style={styles.iconContainer}>
                <Text style={styles.activityIcon}>
                  {option.icon}
                </Text>
              </View>
              
              <Text style={[
                styles.activityLabel,
                selectedActivity === option.id && styles.selectedLabel
              ]}>
                {t(option.labelKey)}
              </Text>
              
              <Text style={[
                styles.activityDescription,
                selectedActivity === option.id && styles.selectedDescription
              ]}>
                {t(option.descriptionKey)}
              </Text>
              
              {selectedActivity === option.id && (
                <Feather name="check" size={20} color="#4285F4" style={styles.checkIcon} />
              )}
            </TouchableOpacity>
          ))}
        </View>
        
        {/* Next button */}
        <TouchableOpacity
          style={[
            styles.nextButton,
            !selectedActivity && styles.disabledButton
          ]}
          onPress={handleNext}
          disabled={isLoading || !selectedActivity}
        >
          {isLoading ? (
            <ActivityIndicator size="small" color="#FFFFFF" />
          ) : (
            <Text style={styles.nextButtonText}>Next</Text>
          )}
        </TouchableOpacity>
      </View>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#FFFFFF',
  },
  content: {
    flex: 1,
    padding: 24,
  },
  progressText: {
    textAlign: 'center',
    fontSize: 16,
    color: '#6B7280',
    marginBottom: 24,
  },
  headerContainer: {
    marginBottom: 32,
  },
  title: {
    fontSize: 28,
    fontWeight: 'bold',
    color: '#1F2937',
    textAlign: 'center',
    marginBottom: 12,
  },
  subtitle: {
    fontSize: 16,
    color: '#6B7280',
    textAlign: 'center',
    lineHeight: 22,
  },
  optionsContainer: {
    marginBottom: 40,
  },
  activityOption: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#FFFFFF',
    borderRadius: 16,
    padding: 16,
    marginBottom: 16,
    borderWidth: 1,
    borderColor: '#E5E7EB',
  },
  selectedOption: {
    backgroundColor: '#F0F7FF',
    borderColor: '#4285F4',
  },
  iconContainer: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: '#4285F4',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 16,
  },
  activityIcon: {
    fontSize: 20,
  },
  activityLabel: {
    fontSize: 18,
    color: '#1F2937',
    fontWeight: '500',
  },
  activityDescription: {
    fontSize: 14,
    color: '#6B7280',
    flex: 1,
    marginTop: 4,
  },
  selectedLabel: {
    color: '#1F2937',
    fontWeight: '600',
  },
  selectedDescription: {
    color: '#4B5563',
  },
  checkIcon: {
    marginLeft: 8,
  },
  nextButton: {
    backgroundColor: '#4285F4',
    borderRadius: 28,
    paddingVertical: 16,
    alignItems: 'center',
    justifyContent: 'center',
    marginTop: 'auto',
  },
  disabledButton: {
    backgroundColor: '#A0AEC0',
  },
  nextButtonText: {
    color: '#FFFFFF',
    fontSize: 18,
    fontWeight: '600',
  },
});
