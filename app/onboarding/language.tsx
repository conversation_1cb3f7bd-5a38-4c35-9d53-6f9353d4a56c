import React, { useState, useEffect } from 'react';
import {
  StyleSheet,
  View,
  Text,
  TouchableOpacity,
  SafeAreaView,
  ScrollView,
  ActivityIndicator,
  Alert,
} from 'react-native';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { useRouter } from 'expo-router';
import { useAuth } from '../../contexts/AuthContext';
import { doc, setDoc, getDoc } from 'firebase/firestore';
import { db } from '../../services/firebase';
import { Feather } from '@expo/vector-icons';
import { changeLanguage } from '../../src/i18n';
import { useLanguage } from '../../src/hooks/useLanguage';
import { useTranslation } from 'react-i18next';

// Get language options directly from the app's supported languages
const getLanguageOptions = (t) => [
  { 
    id: 'hungarian', 
    label: t('languages.hungarian', 'Magyar'), // Native name with fallback
    flag: '🇭🇺',
    langCode: 'hu'
  },
  { 
    id: 'english', 
    label: t('languages.english', 'English'),
    flag: '🇬🇧',
    langCode: 'en'
  },
  { 
    id: 'spanish', 
    label: t('languages.spanish', 'Español'), // Native name with fallback
    flag: '🇪🇸',
    langCode: 'es'
  },
  { 
    id: 'german', 
    label: t('languages.german', 'Deutsch'), // Native name with fallback
    flag: '🇩🇪',
    langCode: 'de'
  },
  { 
    id: 'french', 
    label: t('languages.french', 'Français'), // Native name with fallback
    flag: '🇫🇷',
    langCode: 'fr'
  },
];

export default function LanguageScreen() {
  const [selectedLanguage, setSelectedLanguage] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const router = useRouter();
  const { user } = useAuth();
  const { currentLanguage } = useLanguage();
  const { t } = useTranslation();
  
  // Set a flag in AsyncStorage to indicate we're on the language selection screen
  // This helps prevent automatic navigation away from this screen
  useEffect(() => {
    const markLanguageScreenActive = async () => {
      try {
        // Set the language screen active flag
        await AsyncStorage.setItem('LANGUAGE_SCREEN_ACTIVE', 'true');
        console.log('[LanguageScreen] Marked language screen as active in AsyncStorage');
        
        // Check if we already have a language selected
        const languageSelected = await AsyncStorage.getItem('LANGUAGE_SELECTED');
        if (languageSelected === 'true') {
          console.log('[LanguageScreen] Language already selected, keeping flag');
        }
      } catch (error) {
        console.error('[LanguageScreen] Error managing language screen flags:', error);
      }
    };
    
    markLanguageScreenActive();
    
    // Clean up when unmounting
    return () => {
      const cleanupFlag = async () => {
        try {
          await AsyncStorage.removeItem('LANGUAGE_SCREEN_ACTIVE');
          console.log('[LanguageScreen] Removed language screen active flag from AsyncStorage');
          
          // Note: We do NOT remove the LANGUAGE_SELECTED flag here
          // because we want it to persist after the user selects a language and proceeds
        } catch (error) {
          console.error('[LanguageScreen] Error removing language screen active flag:', error);
        }
      };
      
      cleanupFlag();
    };
  }, []);
  
  // Get translated language options
  const languageOptions = getLanguageOptions(t);
  
  // Check for existing language selection and user data
  useEffect(() => {
    // Check if we're in the correct place in the onboarding flow
    if (user) {
      console.log(`[LanguageScreen] User authenticated: ${user.email}`);
      
      // Check if the user already has a language set but onboarding is not completed
      const checkUserLanguage = async () => {
        try {
          const userDocRef = doc(db, 'users', user.uid);
          const userDoc = await getDoc(userDocRef);
          
          if (userDoc.exists()) {
            const userData = userDoc.data();
            console.log('[LanguageScreen] User data:', {
              languageCode: userData.languageCode,
              preferredLanguage: userData.preferredLanguage,
              onboardingCompleted: userData.onboardingCompleted
            });
            
            // If user has a preferred language, pre-select it
            if (userData.preferredLanguage) {
              console.log(`[LanguageScreen] Pre-selecting user's preferred language: ${userData.preferredLanguage}`);
              setSelectedLanguage(userData.preferredLanguage);
              
              // Find the language option to get the language code
              const option = languageOptions.find(opt => opt.id === userData.preferredLanguage);
              if (option) {
                // Apply the language immediately
                await changeLanguage(option.langCode);
              }
            } else {
              // No language set, user needs to select one
              console.log('[LanguageScreen] No preferred language found, waiting for user selection');
              setSelectedLanguage(null);
            }
            
            console.log('[LanguageScreen] Waiting for user to confirm language selection');
          } else {
            // New user, no document exists yet
            console.log('[LanguageScreen] No user document found, waiting for language selection');
            setSelectedLanguage(null);
          }
        } catch (error) {
          console.error('[LanguageScreen] Error checking user language:', error);
          setSelectedLanguage(null);
        }
      };
      
      checkUserLanguage();
    } else {
      console.error('[LanguageScreen] No authenticated user found!');
      setSelectedLanguage(null);
    }
    
    // Return a cleanup function to prevent any pending navigation when unmounting
    return () => {
      console.log('[LanguageScreen] Component unmounting - cleaning up');
    };
  }, [user]);

  // Select language and apply it immediately
  const selectLanguage = async (id: string) => {
    console.log(`[LanguageScreen] User explicitly selected language: ${id}`);
    setSelectedLanguage(id);
    
    // Find the selected language option
    const selectedOption = languageOptions.find(option => option.id === id);
    if (selectedOption) {
      console.log(`[LanguageScreen] Applying selected language: ${id} (${selectedOption.langCode})`);
      // Change the app language immediately for UI only
      // This doesn't save the preference yet - that happens in handleNext
      await changeLanguage(selectedOption.langCode);
      
      // CRITICAL: Do not automatically proceed to the next screen
      // User must explicitly click the Next button to proceed
      console.log('[LanguageScreen] Language selected but waiting for user to click Next');
    }
  };

  // Save language preference and go to next screen
  const handleNext = async () => {
    console.log('[LanguageScreen] Next button clicked');
    
    // CRITICAL: Enforce explicit language selection before proceeding
    if (!user) {
      console.error('[LanguageScreen] Cannot proceed: No authenticated user');
      Alert.alert('Error', 'You must be logged in to continue.');
      return;
    }
    
    if (!selectedLanguage) {
      console.error('[LanguageScreen] Cannot proceed: No language selected');
      Alert.alert('Please select a language', 'You must select a language to continue.');
      return;
    }
    
    // Double-check that we have a valid language selection
    const selectedOption = languageOptions.find(option => option.id === selectedLanguage);
    if (!selectedOption) {
      console.error('[LanguageScreen] Cannot proceed: Selected language option not found');
      Alert.alert('Error', 'Please select a valid language to continue.');
      return;
    }
    
    // Set a flag to indicate that a language has been selected and the user should be allowed to proceed
    // This will be checked by the route protection logic
    try {
      await AsyncStorage.setItem('LANGUAGE_SELECTED', 'true');
      // Clear any redirect flags to prevent redirect loops
      await AsyncStorage.removeItem('REDIRECTING_TO_LANGUAGE');
      console.log('[LanguageScreen] Marked language as selected and cleared redirect flags');
    } catch (error) {
      console.error('[LanguageScreen] Error managing language flags:', error);
    }
    
    console.log(`[LanguageScreen] Proceeding to next screen with language: ${selectedLanguage}`);
    setIsLoading(true);
    
    try {
      // Save language preference to user profile
      const userDocRef = doc(db, 'users', user.uid);
      
      // Find the selected language option to get the language code
      const selectedOption = languageOptions.find(option => option.id === selectedLanguage);
      console.log('[LanguageScreen] Selected language option:', selectedOption);
      
      if (!selectedOption) {
        console.error('[LanguageScreen] Selected language option not found!');
        Alert.alert('Error', 'There was a problem with your language selection. Please try again.');
        setIsLoading(false);
        return;
      }
      
      // Update user document with language preference
      const languageData = {
        preferredLanguage: selectedLanguage,
        languageCode: selectedOption.langCode,
        updatedAt: new Date().toISOString()
      };
      
      console.log('[LanguageScreen] Saving language data to Firestore:', languageData);
      await setDoc(userDocRef, languageData, { merge: true });
      
      // Ensure the language is applied
      console.log(`[LanguageScreen] Applying language change to i18n: ${selectedOption.langCode}`);
      await changeLanguage(selectedOption.langCode);
      
      // Double check that language was saved correctly
      const userDoc = await getDoc(userDocRef);
      if (userDoc.exists()) {
        const userData = userDoc.data();
        console.log(`[LanguageScreen] Verified language saved to user profile: ${userData.languageCode}`);
      }
      
      // Navigate to congratulations screen (final onboarding step)
      console.log('[LanguageScreen] User explicitly clicked Next - navigating to congratulations');

      // Use replace instead of push to avoid navigation stack issues
      router.replace('/onboarding/congratulations');

      // Log that we've moved to the final onboarding step
      console.log('[LanguageScreen] Moving to congratulations (final onboarding step)');
    } catch (error) {
      console.error('[LanguageScreen] Error saving language preference:', error);
      Alert.alert('Error', 'There was a problem saving your language preference. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  // Add a debug log when the component renders
  useEffect(() => {
    console.log('[LanguageScreen] Component rendered with selectedLanguage:', selectedLanguage);
  }, [selectedLanguage]);
  
  return (
    <SafeAreaView style={styles.container}>
      <ScrollView contentContainerStyle={styles.scrollContent}>
        {/* Progress indicator */}
        <Text style={styles.progressText}>
          {t('onboarding.step', 'Step {{current}} of {{total}}', { current: 8, total: 8 })}
        </Text>
        
        {/* Title and subtitle */}
        <View style={styles.headerContainer}>
          <Text style={styles.title}>
            {t('languages.languageSelector')}
          </Text>
          <Text style={styles.subtitle}>
            {t('profile.languagePreferences')}
          </Text>
          <Text style={styles.importantNote}>
            {t('languages.selectLanguagePrompt', 'Please select your preferred language to continue')}
          </Text>
        </View>
        
        {/* Language options */}
        <View style={styles.optionsContainer}>
          {languageOptions.map((option) => (
            <TouchableOpacity
              key={option.id}
              style={[
                styles.languageOption,
                selectedLanguage === option.id && styles.selectedOption
              ]}
              onPress={() => selectLanguage(option.id)}
              activeOpacity={0.7}
            >
              <Text style={styles.flagIcon}>
                {option.flag}
              </Text>
              
              <Text style={[
                styles.languageLabel,
                selectedLanguage === option.id && styles.selectedLabel
              ]}>
                {option.label}
              </Text>
              
              {selectedLanguage === option.id && (
                <Feather name="check" size={20} color="#4285F4" style={styles.checkIcon} />
              )}
            </TouchableOpacity>
          ))}
        </View>
        
        {/* Next button with enhanced visibility */}
        <View style={styles.nextButtonContainer}>
          {!selectedLanguage && (
            <Text style={styles.selectionRequiredText}>
              ⚠️ {t('languages.selectionRequired', 'You must select a language to continue')}
            </Text>
          )}
          <TouchableOpacity
            style={[
              styles.nextButton,
              !selectedLanguage && styles.disabledButton
            ]}
            onPress={handleNext}
            disabled={isLoading || !selectedLanguage}
            accessibilityLabel={t('common.next')}
            accessibilityHint={!selectedLanguage ? 
              t('languages.selectLanguageAccessibilityHint', 'Please select a language first') : 
              t('common.nextScreenAccessibilityHint', 'Go to next screen')}
          >
            {isLoading ? (
              <ActivityIndicator size="small" color="#FFFFFF" />
            ) : (
              <Text style={[styles.nextButtonText, !selectedLanguage && styles.disabledButtonText]}>
                {!selectedLanguage ? t('languages.pleaseSelect', 'Please Select') : 'Next'}
              </Text>
            )}
          </TouchableOpacity>
        </View>
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#FFFFFF',
  },
  scrollContent: {
    flexGrow: 1,
    padding: 20,
  },
  progressText: {
    textAlign: 'center',
    fontSize: 14,
    color: '#666',
    marginBottom: 20,
  },
  importantNote: {
    textAlign: 'center',
    fontSize: 16,
    color: '#E53935',  // Red color for emphasis
    marginTop: 10,
    fontWeight: 'bold',
  },
  headerContainer: {
    marginBottom: 32,
  },
  title: {
    fontSize: 28,
    fontWeight: 'bold',
    color: '#1F2937',
    textAlign: 'center',
    marginBottom: 12,
  },
  subtitle: {
    fontSize: 16,
    color: '#6B7280',
    textAlign: 'center',
    lineHeight: 22,
  },
  optionsContainer: {
    marginBottom: 40,
  },
  languageOption: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#FFFFFF',
    borderRadius: 16,
    padding: 16,
    marginBottom: 16,
    borderWidth: 1,
    borderColor: '#E5E7EB',
  },
  selectedOption: {
    borderColor: '#4285F4',
    borderWidth: 2,
  },
  flagIcon: {
    fontSize: 24,
    marginRight: 16,
  },
  languageLabel: {
    fontSize: 18,
    color: '#1F2937',
    flex: 1,
  },
  selectedLabel: {
    color: '#1F2937',
    fontWeight: '500',
  },
  checkIcon: {
    marginLeft: 8,
  },
  nextButtonContainer: {
    marginTop: 32,
    alignItems: 'center',
  },
  selectionRequiredText: {
    color: '#E53935',
    fontSize: 16,
    fontWeight: 'bold',
    marginBottom: 12,
    textAlign: 'center',
  },
  nextButton: {
    backgroundColor: '#4285F4',
    paddingVertical: 16,
    borderRadius: 8,
    alignItems: 'center',
    justifyContent: 'center',
    width: '100%',
  },
  disabledButton: {
    backgroundColor: '#CCCCCC',
  },
  nextButtonText: {
    color: '#FFFFFF',
    fontSize: 16,
    fontWeight: 'bold',
  },
  disabledButtonText: {
    color: '#888888',
  },
});
