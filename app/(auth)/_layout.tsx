import React, { useEffect } from 'react';
import { Slot, Stack, useRouter } from 'expo-router';
import { useAuth } from '../../contexts/AuthContext';

function AuthLayout() {
  const { user, loading } = useAuth();
  const router = useRouter();
  
  // If user is already authenticated, redirect to dashboard
  useEffect(() => {
    console.log('AuthLayout - Auth check:', { user: user?.email || 'no user', loading });
    
    if (!loading && user) {
      console.log('AuthLayout - Already authenticated, redirecting to dashboard');
      router.replace('/(main)/dashboard');
    }
  }, [user, loading, router]);
  
  // Show the auth screens (login/signup)
  console.log('AuthLayout - Showing auth screens');
  return (
    <Stack screenOptions={{ headerShown: false }}>
      <Stack.Screen name="login" />
      <Stack.Screen name="signup" />
      <Slot />
    </Stack>
  );
}

export default AuthLayout;