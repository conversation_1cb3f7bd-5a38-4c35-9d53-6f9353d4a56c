import React, { useEffect, useState } from 'react';
import { Slot, Redirect, useSegments, useRouter } from 'expo-router';
import { PaperProvider, DefaultTheme } from 'react-native-paper';
import { ThemeProvider } from '../contexts/ThemeContext';
import { AuthProvider, useAuth } from '../contexts/AuthContext';
import { checkOnboardingStatus } from '../utils/onboarding';
import AsyncStorage from '@react-native-async-storage/async-storage';


import { useFonts } from 'expo-font';
import * as SplashScreen from 'expo-splash-screen';
import Constants from 'expo-constants';
import { Text, View } from 'react-native';
import { IconFontPaths, preloadIconFonts } from '../config/icons';
import LanguageProvider from '../src/i18n/LanguageProvider';
import { TutorialProvider } from '../contexts/TutorialContext';
import { CopilotProvider } from 'react-native-copilot';
import TutorialWrapper from '../components/tutorial/TutorialWrapper';

// Firebase is initialized in AuthContext.tsx

// Keep the splash screen visible while we fetch resources
SplashScreen.preventAutoHideAsync();

// Check if we should use Firebase emulators
const useEmulator = process.env.EXPO_PUBLIC_USE_FIREBASE_EMULATOR === 'true';

// Create a theme for react-native-paper
const theme = {
  ...DefaultTheme,
  colors: {
    ...DefaultTheme.colors,
    primary: '#2F80ED',
    accent: '#f1c40f',
  },
};

// Firebase is initialized at the top of the file

// Define types for onboarding segments for better type safety
type OnboardingSegment = 'language' | 'interests' | 'budget' | 'destinations' | 
  'travel-companions' | 'activity-level' | 'travel-challenges' | 'travel-preferences';

// This component protects routes and ensures users are authenticated
function ProtectedRouteProvider({ children }: { children: React.ReactNode }) {
  const segments = useSegments();
  const router = useRouter();
  const { user, loading } = useAuth();
  const [isRedirecting, setIsRedirecting] = useState(false);
  const [onboardingCompleted, setOnboardingCompleted] = useState<boolean | null>(null);
  
  // Ultra-simplified route protection logic
  useEffect(() => {
    // Skip if already redirecting or auth state is still loading
    if (isRedirecting || loading) return;
    
    const handleRouteProtection = async () => {
      // Define route group variables
      const inAuthGroup = segments[0] === '(auth)';
      const inOnboardingGroup = segments[0] === 'onboarding';
      const inMainGroup = segments[0] === '(main)';
      const currentScreen = segments[1];
      
      console.log(`[RouteProtection] Current route: ${segments.join('/')}, Auth: ${!!user}, Onboarding: ${onboardingCompleted}`);

      // First-time check for onboarding status
      if (user && onboardingCompleted === null) {
        try {
          const completed = await checkOnboardingStatus(user.uid);
          console.log(`[RouteProtection] Checked onboarding status: ${completed}`);
          setOnboardingCompleted(completed);

          // IMPORTANT: For new users, immediately redirect to interests
          if (!completed && !inOnboardingGroup) {
            console.log('[RouteProtection] New user detected, redirecting to interests');
            setIsRedirecting(true);
            router.replace('/onboarding/interests');
          }
          return;
        } catch (error) {
          console.error('[RouteProtection] Error checking status:', error);
          setOnboardingCompleted(false);

          // On error, still redirect to onboarding
          if (!inOnboardingGroup) {
            console.log('[RouteProtection] Error checking status, redirecting to interests');
            setIsRedirecting(true);
            router.replace('/onboarding/interests');
          }
          return;
        }
      }
      
      // CORE ROUTING LOGIC
      
      // Case 1: Not logged in - redirect to login (unless already in auth)
      if (!user && !inAuthGroup) {
        console.log('[RouteProtection] Not logged in, redirecting to login');
        setIsRedirecting(true);
        router.replace('/(auth)/login');
        return;
      }
      
      // Case 2: Logged in but in auth screens - redirect based on onboarding status
      if (user && inAuthGroup) {
        console.log('[RouteProtection] User in auth screens, redirecting based on onboarding');
        setIsRedirecting(true);
        if (onboardingCompleted === true) {
          router.replace('/(main)/dashboard');
        } else {
          router.replace('/onboarding/interests');
        }
        return;
      }
      
      // Case 3: In onboarding flow
      if (user && inOnboardingGroup) {
        const onboardingScreens = ['interests', 'budget', 'travel-companions',
                                 'activity-level', 'travel-challenges', 'travel-preferences', 'language', 'congratulations'];

        // If onboarding completed and not on congratulations screen, go to dashboard
        if (onboardingCompleted === true && currentScreen !== 'congratulations') {
          console.log('[RouteProtection] Onboarding completed but in onboarding flow');
          setIsRedirecting(true);
          router.replace('/(main)/dashboard');
          return;
        }
        
        // If in valid onboarding screen, allow access
        if (onboardingScreens.includes(currentScreen)) {
          console.log(`[RouteProtection] Valid onboarding screen: ${currentScreen}`);
          return; // Allow access
        }
        
        // Invalid onboarding screen, go to interests
        console.log(`[RouteProtection] Invalid onboarding screen: ${currentScreen}`);
        setIsRedirecting(true);
        router.replace('/onboarding/interests');
        return;
      }

      // Case 4: Authenticated user in main group but onboarding incomplete
      if (user && inMainGroup && onboardingCompleted === false) {
        console.log('[RouteProtection] User in main group but onboarding incomplete');
        setIsRedirecting(true);
        router.replace('/onboarding/interests');
        return;
      }

      // Case 5: Handle root route navigation after onboarding completion
      if (user && segments.length === 0 && onboardingCompleted === true) {
        console.log('[RouteProtection] Root route with completed onboarding');
        setIsRedirecting(true);
        router.replace('/(main)/dashboard');
        return;
      }

      // Case 6: Catch-all for any other screens when onboarding is incomplete
      if (user && onboardingCompleted === false && !inOnboardingGroup) {
        console.log('[RouteProtection] Catch-all: User with incomplete onboarding');
        setIsRedirecting(true);
        router.replace('/onboarding/interests');
        return;
      }
    };
    
    handleRouteProtection();
  }, [user, loading, segments, router, isRedirecting, onboardingCompleted]);
  
  return <>{children}</>;
}

function RootLayout() {
  const [isReady, setIsReady] = useState(false);
  const [fontError, setFontError] = useState<string | null>(null);

  // Load the icon fonts with error handling
  const [fontsLoaded, fontLoadError] = useFonts({
    // Load all the Expo Vector Icons fonts
    'Entypo': require('@expo/vector-icons/fonts/Entypo.ttf'),
    'AntDesign': require('@expo/vector-icons/fonts/AntDesign.ttf'),
    'EvilIcons': require('@expo/vector-icons/fonts/EvilIcons.ttf'),
    'Feather': require('@expo/vector-icons/fonts/Feather.ttf'),
    'FontAwesome': require('@expo/vector-icons/fonts/FontAwesome.ttf'),
    'FontAwesome5_Brands': require('@expo/vector-icons/fonts/FontAwesome5_Brands.ttf'),
    'FontAwesome5_Regular': require('@expo/vector-icons/fonts/FontAwesome5_Regular.ttf'),
    'FontAwesome5_Solid': require('@expo/vector-icons/fonts/FontAwesome5_Solid.ttf'),
    'Fontisto': require('@expo/vector-icons/fonts/Fontisto.ttf'),
    'Foundation': require('@expo/vector-icons/fonts/Foundation.ttf'),
    'Ionicons': require('@expo/vector-icons/fonts/Ionicons.ttf'),
    'MaterialCommunityIcons': require('@expo/vector-icons/fonts/MaterialCommunityIcons.ttf'),
    'MaterialIcons': require('@expo/vector-icons/fonts/MaterialIcons.ttf'),
    'Octicons': require('@expo/vector-icons/fonts/Octicons.ttf'),
    'SimpleLineIcons': require('@expo/vector-icons/fonts/SimpleLineIcons.ttf'),
    'Zocial': require('@expo/vector-icons/fonts/Zocial.ttf'),
  });

  useEffect(() => {
    const prepareApp = async () => {
      try {
        // Attempt to preload icons as a fallback
        await preloadIconFonts();
        
        if (fontLoadError) {
          console.warn('Font loading error:', fontLoadError);
          setFontError('Failed to load some fonts, but app can continue.');
        }
        
        // We can proceed even if there were font issues
        setIsReady(true);
        await SplashScreen.hideAsync();
      } catch (e) {
        console.error('Error in app initialization:', e);
        setFontError('App initialization error. Please restart the app.');
        await SplashScreen.hideAsync();
      }
    };

    if (fontsLoaded || fontLoadError) {
      prepareApp();
    }
  }, [fontsLoaded, fontLoadError]);

  if (!isReady) {
    return null;
  }

  // Render error message if there was a critical font loading error
  if (fontError && !fontsLoaded) {
    return (
      <View style={{ flex: 1, justifyContent: 'center', alignItems: 'center', padding: 20 }}>
        <Text style={{ fontSize: 16, textAlign: 'center' }}>
          {fontError}
        </Text>
      </View>
    );
  }

  return (
    <ThemeProvider>
      <PaperProvider>
        <LanguageProvider>
          <AuthProvider>
            <TutorialProvider>
              <CopilotProvider
                overlay="svg"
                animated={true}
                backdropColor="rgba(0, 0, 0, 0.7)"
                arrowColor="#FFFFFF"
                tooltipStyle={{
                  backgroundColor: '#FFFFFF',
                  borderRadius: 16,
                  padding: 20,
                  shadowColor: '#000',
                  shadowOffset: { width: 0, height: 8 },
                  shadowOpacity: 0.15,
                  shadowRadius: 20,
                  elevation: 8,
                  borderWidth: 1,
                  borderColor: 'rgba(0, 0, 0, 0.05)',
                  minWidth: 280,
                }}
                tooltipTextStyle={{
                  color: '#1F2937',
                  fontSize: 16,
                  fontWeight: '500',
                  lineHeight: 22,
                  textAlign: 'left',
                }}
                stepNumberStyle={{
                  backgroundColor: '#3A7BF8',
                  color: '#FFFFFF',
                  fontSize: 14,
                  fontWeight: '600',
                  width: 28,
                  height: 28,
                  borderRadius: 14,
                  textAlign: 'center',
                  lineHeight: 28,
                }}
                buttonStyle={{
                  backgroundColor: '#3A7BF8',
                  borderRadius: 12,
                  paddingHorizontal: 20,
                  paddingVertical: 12,
                  marginHorizontal: 8,
                  shadowColor: '#3A7BF8',
                  shadowOffset: { width: 0, height: 4 },
                  shadowOpacity: 0.3,
                  shadowRadius: 8,
                  elevation: 4,
                }}
                buttonTextStyle={{
                  color: '#FFFFFF',
                  fontSize: 15,
                  fontWeight: '600',
                  textAlign: 'center',
                }}
                labels={{
                  previous: '← Previous',
                  next: 'Next →',
                  skip: '',
                  finish: '✨ Got it!',
                }}
                maskOffset={0}
                verticalOffset={0}
              >
                <TutorialWrapper />
                <ProtectedRouteProvider>
                  <Slot />
                </ProtectedRouteProvider>
              </CopilotProvider>
            </TutorialProvider>
          </AuthProvider>
        </LanguageProvider>
      </PaperProvider>
    </ThemeProvider>
  );
}

export default RootLayout;