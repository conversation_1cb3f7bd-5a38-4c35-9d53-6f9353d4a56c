import React from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  Dimensions,
  SafeAreaView,
} from 'react-native';
import { useRouter } from 'expo-router';
import { LinearGradient } from 'expo-linear-gradient';
import { Ionicons } from '@expo/vector-icons';
import { useCopilot } from 'react-native-copilot';
import { useTutorial } from '../contexts/TutorialContext';
import { useTheme } from 'react-native-paper';
import { useAuth } from '../contexts/AuthContext';
import AsyncStorage from '@react-native-async-storage/async-storage';

const { width, height } = Dimensions.get('window');

// Congratulations status functions
const CONGRATULATIONS_STORAGE_KEY = 'congratulations_seen';

const setCongratulationsCompleted = async (userId: string): Promise<void> => {
  try {
    const userCongratulationsKey = `${CONGRATULATIONS_STORAGE_KEY}_${userId}`;
    await AsyncStorage.setItem(userCongratulationsKey, 'true');
  } catch (error) {
    console.error('Error setting congratulations status:', error);
  }
};

export default function CongratulationsScreen() {
  const router = useRouter();
  const { colors } = useTheme();
  const { start } = useCopilot();
  const { completeTutorial } = useTutorial();
  const { user } = useAuth();

  const handleStartTutorial = async () => {
    if (!user?.uid) return;

    // Mark congratulations as seen
    await setCongratulationsCompleted(user.uid);

    // Navigate to dashboard first
    router.replace('/(main)/dashboard');

    // Small delay to ensure dashboard is loaded, then start tutorial
    setTimeout(() => {
      start();
    }, 1000);
  };

  return (
    <SafeAreaView style={styles.container}>
      <LinearGradient
        colors={['#3A7BF8', '#6B9FFF', '#8BB5FF']}
        style={styles.gradient}
        start={{ x: 0, y: 0 }}
        end={{ x: 1, y: 1 }}
      >
        {/* Decorative Elements */}
        <View style={styles.decorativeContainer}>
          <View style={[styles.circle, styles.circle1]} />
          <View style={[styles.circle, styles.circle2]} />
          <View style={[styles.circle, styles.circle3]} />
        </View>

        {/* Main Content */}
        <View style={styles.contentContainer}>
          {/* Success Icon */}
          <View style={styles.iconContainer}>
            <Ionicons name="checkmark-circle" size={80} color="#FFFFFF" />
          </View>

          {/* Title */}
          <Text style={styles.title}>Congratulations!</Text>
          
          {/* Subtitle */}
          <Text style={styles.subtitle}>
            You are one step away from creating the best travel plans of your life
          </Text>

          {/* Features List */}
          <View style={styles.featuresContainer}>
            <View style={styles.featureItem}>
              <Ionicons name="map" size={24} color="#FFFFFF" />
              <Text style={styles.featureText}>Personalized itineraries</Text>
            </View>
            <View style={styles.featureItem}>
              <Ionicons name="heart" size={24} color="#FFFFFF" />
              <Text style={styles.featureText}>Save favorite places</Text>
            </View>
            <View style={styles.featureItem}>
              <Ionicons name="chatbubble-ellipses" size={24} color="#FFFFFF" />
              <Text style={styles.featureText}>AI travel assistant</Text>
            </View>
          </View>

          {/* Start Tutorial Button */}
          <TouchableOpacity
            style={styles.startButton}
            onPress={handleStartTutorial}
            activeOpacity={0.8}
          >
            <LinearGradient
              colors={['#FFFFFF', '#F8F9FA']}
              style={styles.buttonGradient}
              start={{ x: 0, y: 0 }}
              end={{ x: 1, y: 1 }}
            >
              <Text style={styles.buttonText}>Start Tutorial</Text>
              <Ionicons name="arrow-forward" size={20} color="#3A7BF8" />
            </LinearGradient>
          </TouchableOpacity>

          {/* Bottom Text */}
          <Text style={styles.bottomText}>
            Let's take a quick tour to get you started
          </Text>
        </View>
      </LinearGradient>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  gradient: {
    flex: 1,
    position: 'relative',
  },
  decorativeContainer: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
  },
  circle: {
    position: 'absolute',
    borderRadius: 1000,
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
  },
  circle1: {
    width: 200,
    height: 200,
    top: -50,
    right: -50,
  },
  circle2: {
    width: 150,
    height: 150,
    bottom: 100,
    left: -30,
  },
  circle3: {
    width: 100,
    height: 100,
    top: height * 0.3,
    right: 20,
  },
  contentContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 32,
    zIndex: 1,
  },
  iconContainer: {
    marginBottom: 32,
  },
  title: {
    fontSize: 32,
    fontWeight: 'bold',
    color: '#FFFFFF',
    textAlign: 'center',
    marginBottom: 16,
  },
  subtitle: {
    fontSize: 18,
    color: '#FFFFFF',
    textAlign: 'center',
    lineHeight: 26,
    marginBottom: 40,
    opacity: 0.9,
  },
  featuresContainer: {
    marginBottom: 48,
  },
  featureItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 16,
  },
  featureText: {
    fontSize: 16,
    color: '#FFFFFF',
    marginLeft: 12,
    opacity: 0.9,
  },
  startButton: {
    width: width - 64,
    marginBottom: 24,
  },
  buttonGradient: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 16,
    paddingHorizontal: 32,
    borderRadius: 12,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.3,
    shadowRadius: 8,
    elevation: 8,
  },
  buttonText: {
    fontSize: 18,
    fontWeight: '600',
    color: '#3A7BF8',
    marginRight: 8,
  },
  bottomText: {
    fontSize: 14,
    color: '#FFFFFF',
    textAlign: 'center',
    opacity: 0.8,
  },
});
