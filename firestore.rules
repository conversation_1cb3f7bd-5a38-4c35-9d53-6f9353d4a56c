rules_version = '2';

service cloud.firestore {
  match /databases/{database}/documents {    
    // Allow authenticated users to read and write their own data
    match /{document=**} {
      allow read, write: if request.auth != null && 
        (resource == null || 
         resource.data.userId == request.auth.uid || 
         request.resource.data.userId == request.auth.uid ||
         resource.data.user_id == request.auth.uid ||
         request.resource.data.user_id == request.auth.uid ||
         resource.id == request.auth.uid ||
         resource.id.matches(request.auth.uid + '_.*'));
    }
    
    // Original rules below for reference
    // User profiles - users can read and update their own profiles
    match /users/{userId} {
      allow read: if request.auth != null && request.auth.uid == userId;
      allow create: if request.auth != null && request.auth.uid == userId;
      allow update: if request.auth != null && request.auth.uid == userId;
      allow delete: if false; // Don't allow profile deletion through client
    }
    
    // Trip Plans - users can manage their own trip plans
    match /tripPlans/{userId} {
      allow read: if request.auth != null && request.auth.uid == userId;
      allow create: if request.auth != null && request.auth.uid == userId;
      allow update: if request.auth != null && request.auth.uid == userId;
      allow delete: if request.auth != null && request.auth.uid == userId;
      
      // Allow access to nested plans
      match /plans/{planId} {
        allow read: if request.auth != null && request.auth.uid == userId;
        allow create: if request.auth != null && request.auth.uid == userId;
        allow update: if request.auth != null && request.auth.uid == userId;
        allow delete: if request.auth != null && request.auth.uid == userId;
        
        // Allow access to saved places within plans
        match /savedPlaces/{placeId} {
          allow read: if request.auth != null && request.auth.uid == userId;
          allow create: if request.auth != null && request.auth.uid == userId;
          allow update: if request.auth != null && request.auth.uid == userId;
          allow delete: if request.auth != null && request.auth.uid == userId;
        }
      }
    }
    
    // Map Markers - users can manage their own map markers
    match /mapMarkers/{docId} {
      allow read, write: if request.auth != null && resource.data.userId == request.auth.uid;
      allow create: if request.auth != null && request.resource.data.userId == request.auth.uid;
    }
    
    // Trips - users can manage their own trips
    match /trips/{tripId} {
      allow read: if request.auth != null && resource.data.userId == request.auth.uid;
      allow create: if request.auth != null && request.resource.data.userId == request.auth.uid;
      allow update: if request.auth != null && resource.data.userId == request.auth.uid;
      allow delete: if request.auth != null && resource.data.userId == request.auth.uid;
    }
    
    // Places - users can manage their own saved places
    match /places/{placeId} {
      allow read: if request.auth != null && resource.data.userId == request.auth.uid;
      allow create: if request.auth != null && request.resource.data.userId == request.auth.uid;
      allow update: if request.auth != null && resource.data.userId == request.auth.uid;
      allow delete: if request.auth != null && resource.data.userId == request.auth.uid;
    }
    
    // News - all authenticated users can read news, but not modify
    match /news/{newsId} {
      allow read: if request.auth != null;
      allow write: if false; // Only server can update news
    }
    
    // AI logs - record usage but restrict access
    match /ai_logs/{logId} {
      allow create: if request.auth != null && request.resource.data.userId == request.auth.uid;
      allow read: if false; // Only server/admin should read logs
      allow update, delete: if false;
    }
  }
} 