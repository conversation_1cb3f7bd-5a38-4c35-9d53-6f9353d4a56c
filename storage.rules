rules_version = '2';

service firebase.storage {
  match /b/{bucket}/o {
    // User profile pictures with the base path 
    match /profile_pics/{userId}.jpg {
      allow read: if request.auth != null;
      allow write: if request.auth != null && request.auth.uid == userId;
    }
    
    // Allow alternative paths for the different upload approaches
    match /profile_pics/{userId}.jpg_blob {
      allow read: if request.auth != null;
      allow write: if request.auth != null && request.auth.uid == userId;
    }
    
    match /profile_pics/{userId}.jpg_direct {
      allow read: if request.auth != null;
      allow write: if request.auth != null && request.auth.uid == userId;
    }
    
    // Keep old rules for backward compatibility
    // User profile pictures with old paths
    match /users/{userId}/profile.jpg {
      allow read: if request.auth != null;
      allow write: if request.auth != null && request.auth.uid == userId;
    }
    
    // "Profile pics" folder with space
    match /Profile pics/{userId}.jpg {
      allow read: if request.auth != null;
      allow write: if request.auth != null && request.auth.uid == userId;
    }
    
    // Generic rule for all user content - allow users to access their own content
    match /users/{userId}/{allPaths=**} {
      allow read: if request.auth != null;
      allow write: if request.auth != null && request.auth.uid == userId;
    }
    
    // Trip images
    match /users/{userId}/trips/{tripId}/{allImages=**} {
      allow read: if request.auth != null;
      allow write: if request.auth != null && request.auth.uid == userId;
    }
    
    // Place images
    match /users/{userId}/places/{placeId}/{allImages=**} {
      allow read: if request.auth != null;
      allow write: if request.auth != null && request.auth.uid == userId;
    }
    
    // Public images like news images
    match /public/{allImages=**} {
      allow read: if request.auth != null;
      allow write: if false; // Only admin can upload public images
    }
  }
} 