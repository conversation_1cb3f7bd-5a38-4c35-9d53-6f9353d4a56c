declare module 'expo-image-manipulator' {
  export type ImageResult = {
    uri: string;
    width: number;
    height: number;
  };

  export enum SaveFormat {
    JPEG = 'jpeg',
    PNG = 'png',
    WEBP = 'webp'
  }

  export type ImageManipulatorOptions = {
    compress?: number;
    format?: SaveFormat;
    base64?: boolean;
  };

  export type ImageManipulationAction = 
    | { resize: { width?: number; height?: number } }
    | { rotate: number }
    | { flip: { horizontal?: boolean; vertical?: boolean } }
    | { crop: { originX: number; originY: number; width: number; height: number } };

  export function manipulateAsync(
    uri: string,
    actions: ImageManipulationAction[],
    options?: ImageManipulatorOptions
  ): Promise<ImageResult>;
} 