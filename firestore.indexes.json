{"indexes": [{"collectionGroup": "trips", "queryScope": "COLLECTION", "fields": [{"fieldPath": "userId", "order": "ASCENDING"}, {"fieldPath": "createdAt", "order": "DESCENDING"}]}, {"collectionGroup": "places", "queryScope": "COLLECTION", "fields": [{"fieldPath": "userId", "order": "ASCENDING"}, {"fieldPath": "category", "order": "ASCENDING"}]}, {"collectionGroup": "news", "queryScope": "COLLECTION", "fields": [{"fieldPath": "category", "order": "ASCENDING"}, {"fieldPath": "publishedAt", "order": "DESCENDING"}]}, {"collectionGroup": "mapMarkers", "queryScope": "COLLECTION", "fields": [{"fieldPath": "userId", "order": "ASCENDING"}]}], "fieldOverrides": []}