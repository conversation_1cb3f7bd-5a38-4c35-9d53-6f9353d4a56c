# iOS Offline Setup Guide

This guide explains how to run your React Native iOS app on a physical device without requiring the Metro bundler.

## Overview

The app has been configured to use a pre-built production JavaScript bundle (`main.jsbundle`) that is included directly in the iOS app bundle. This allows the app to run completely offline without needing a development server.

## Files Created/Modified

### Production Bundle
- `ios/main.jsbundle` - The production JavaScript bundle (3.8MB)
- `ios/assets/` - Static assets directory (55 files)

### Configuration Files
- `ios/.xcode.env` - Xcode environment configuration
  - `SKIP_BUNDLING=0` - Enables bundling
  - `FORCE_BUNDLING=1` - Forces production bundle usage

### Xcode Project
- `ios/TravelerApp.xcodeproj/project.pbxproj` - Updated to include the bundle as a resource

### Scripts
- `scripts/build-ios-bundle.sh` - Script to rebuild the production bundle
- `scripts/verify-offline-setup.sh` - Script to verify the offline setup

### Package.json Scripts
- `npm run build:ios-bundle` - Rebuild the production bundle
- `npm run verify:offline` - Verify offline setup
- `npm run ios:device` - Run on physical device

## How It Works

1. **Bundle Creation**: The `expo export:embed` command creates a production JavaScript bundle with all dependencies included
2. **Asset Copying**: Static assets are copied to the `ios/assets` directory
3. **Xcode Integration**: The bundle is included as a resource in the Xcode project
4. **Runtime Loading**: The app loads the pre-built bundle instead of connecting to Metro

## Usage

### Building and Running

1. **Open Xcode**:
   ```bash
   open ios/TravelerApp.xcworkspace
   ```

2. **Select Physical Device**: Choose your connected iPhone as the target

3. **Build and Run**: Press ⌘+R or click the Run button

4. **Alternative - Expo CLI**:
   ```bash
   npm run ios:device
   ```

### Rebuilding the Bundle

When you make code changes, rebuild the bundle:

```bash
npm run build:ios-bundle
```

### Verifying Setup

Check if everything is configured correctly:

```bash
npm run verify:offline
```

## Development Workflow

### For Development (with Metro)
```bash
npm start
npm run ios
```

### For Offline Testing/Production
```bash
npm run build:ios-bundle
npm run ios:device
```

## Troubleshooting

### Bundle Not Loading
- Verify the bundle exists: `ls -la ios/main.jsbundle`
- Check Xcode project includes the bundle
- Ensure `.xcode.env` has correct settings

### Build Errors
- Clean Xcode build folder: Product → Clean Build Folder
- Rebuild bundle: `npm run build:ios-bundle`
- Check CocoaPods: `cd ios && pod install`

### Metro Still Starting
- Check `.xcode.env` settings
- Ensure `SKIP_BUNDLING=0` and `FORCE_BUNDLING=1`
- Restart Xcode

## Technical Details

### Bundle Size
- Production bundle: ~3.8MB (minified)
- Assets: 55 files
- Total app size increase: ~4MB

### Performance
- Faster app startup (no Metro connection)
- No network dependency
- Production-optimized code

### Entry Point
- Uses `expo-router/entry` as the main entry point
- Configured in `package.json`: `"main": "expo-router/entry"`

## Notes

- The bundle is created in production mode (`--dev false`)
- Code is minified for optimal performance
- Assets are optimized and copied to the iOS bundle
- The setup works with Expo Router and all current dependencies
- RevenueCat integration is fully supported in offline mode
