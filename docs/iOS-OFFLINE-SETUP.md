# iOS Production Offline Setup Guide

This guide explains how to run your React Native iOS app on a physical device in full production mode without requiring the Metro bundler, exactly like an App Store app.

## Overview

The app has been configured to use a pre-built production JavaScript bundle (`main.jsbundle`) that is included directly in the iOS app bundle. This allows the app to run completely offline in Release configuration without needing a development server, with all dev-only code properly excluded.

## Files Created/Modified

### Entry Point
- `index.js` - Entry file that imports `expo-router/entry`
- `package.json` - Updated main field to use `index.js`

### Production Bundle
- `ios/main.jsbundle` - The production JavaScript bundle (4.5MB, optimized)
- `ios/assets/` - Static assets directory (57 files)

### Configuration Files
- `ios/.xcode.env` - Xcode environment configuration
  - `SKIP_BUNDLING=1` - Uses pre-built bundle instead of Metro
  - `NODE_BINARY=/opt/homebrew/bin/node` - Full Node.js path for builds
- `metro.config.js` - Production configuration
  - `__DEV__: false` - Disables development features in production builds
- `ios/TravelerApp.xcodeproj/xcshareddata/xcschemes/TravelerApp.xcscheme` - Release configuration
  - `buildConfiguration = "Release"` - Uses Release mode for Run action

### Xcode Project
- `ios/TravelerApp.xcodeproj/project.pbxproj` - Updated to include:
  - Bundle as a resource with "Copy items if needed"
  - Correct React Native build script: `../node_modules/react-native/scripts/react-native-xcode.sh`

### Scripts
- `scripts/build-ios-bundle.sh` - Script to rebuild the production bundle using React Native CLI
- `scripts/verify-offline-setup.sh` - Script to verify the offline setup

### Package.json Scripts
- `npm run build:ios-bundle` - Rebuild the production bundle
- `npm run verify:offline` - Verify offline setup
- `npm run ios:device` - Run on physical device

## How It Works

1. **Bundle Creation**: The `react-native bundle` command creates a production JavaScript bundle with `index.js` as entry point
2. **Asset Copying**: Static assets are copied to the `ios/assets` directory
3. **Xcode Integration**: The bundle is included as a resource in the Xcode project with target membership
4. **Runtime Loading**: The app loads the pre-built bundle instead of connecting to Metro
5. **Build Script**: Uses standard React Native build script for proper offline handling

## Usage

### Building and Running

1. **Open Xcode**:
   ```bash
   open ios/TravelerApp.xcworkspace
   ```

2. **Select Physical Device**: Choose your connected iPhone as the target

3. **Build and Run**: Press ⌘+R or click the Run button

4. **Alternative - Expo CLI**:
   ```bash
   npm run ios:device
   ```

### Rebuilding the Bundle

When you make code changes, rebuild the bundle:

```bash
npm run build:ios-bundle
```

### Verifying Setup

Check if everything is configured correctly:

```bash
npm run verify:offline
```

## Development Workflow

### For Development (with Metro)
```bash
npm start
npm run ios
```

### For Offline Testing/Production
```bash
npm run build:ios-bundle
npm run ios:device
```

## Troubleshooting

### Bundle Not Loading
- Verify the bundle exists: `ls -la ios/main.jsbundle`
- Check Xcode project includes the bundle
- Ensure `.xcode.env` has `SKIP_BUNDLING=1`

### Build Errors
- Clean Xcode build folder: Product → Clean Build Folder
- Rebuild bundle: `npm run build:ios-bundle`
- Check CocoaPods: `cd ios && pod install`

### SIGKILL Errors
- Ensure bundle is not empty (should be ~4MB)
- Verify bundle is included in Xcode project with target membership
- Check build script is correct React Native script
- Ensure `index.js` exists and imports `expo-router/entry`

### Metro Still Starting
- Check `.xcode.env` settings
- Ensure `SKIP_BUNDLING=1`
- Restart Xcode

## Technical Details

### Bundle Size
- Production bundle: ~4.0MB (minified)
- Assets: 55 files
- Total app size increase: ~4MB

### Performance
- Faster app startup (no Metro connection)
- No network dependency
- Production-optimized code

### Entry Point
- Uses `index.js` as entry point
- Imports `expo-router/entry` for Expo Router compatibility
- Configured in `package.json`: `"main": "index.js"`

### Build Script
- Uses standard React Native build script: `react-native-xcode.sh`
- Properly handles offline bundle loading
- Prevents SIGKILL crashes from missing bundles

## Notes

- The bundle is created in production mode (`--dev false`)
- Code is minified for optimal performance
- Assets are optimized and copied to the iOS bundle
- The setup works with Expo Router and all current dependencies
- RevenueCat integration is fully supported in offline mode
- Prevents "Thread 1: signal SIGKILL" errors by ensuring proper bundle inclusion
