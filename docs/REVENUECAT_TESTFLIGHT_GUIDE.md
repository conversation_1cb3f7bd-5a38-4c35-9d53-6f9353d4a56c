# RevenueCat TestFlight Testing Guide

## 🎯 Overview

This guide provides step-by-step instructions for testing the RevenueCat integration in TestFlight builds of the Tripzy app.

## ✅ Pre-TestFlight Verification

### 1. Local Development Testing

Before building for TestFlight, test locally:

```bash
# Run on iOS device/simulator
npx expo run:ios --device
```

**Test Steps:**
1. Open the app and navigate to Dashboard
2. Look for the yellow "🧪 RevenueCat Integration Test" section (only visible in development)
3. Tap "Test SDK" button - should show success alert
4. Tap "Test Paywall" button - should present paywall UI
5. Navigate to Profile screen
6. Tap "Upgrade to Premium" button - should present paywall

### 2. Build Verification

Run the verification script:

```bash
node scripts/verify-revenuecat-build.js
```

All checks should pass with ✅ before proceeding to TestFlight.

## 🚀 TestFlight Build Process

### 1. Create TestFlight Build

```bash
# Build for TestFlight
eas build --platform ios --profile production

# Or if using preview profile
eas build --platform ios --profile preview
```

### 2. Upload to App Store Connect

The build will automatically upload to App Store Connect when complete.

## 🧪 TestFlight Testing Checklist

### Core RevenueCat Functionality

#### ✅ SDK Initialization
- [ ] App launches without crashes
- [ ] No RevenueCat-related errors in logs
- [ ] Customer info is retrieved successfully

#### ✅ Paywall Presentation
- [ ] Navigate to Profile screen
- [ ] Tap "Upgrade to Premium" button
- [ ] Paywall appears with correct design
- [ ] Paywall shows configured products/prices
- [ ] Paywall can be dismissed

#### ✅ Purchase Flow (Sandbox Testing)
- [ ] Use sandbox Apple ID for testing
- [ ] Attempt to purchase subscription
- [ ] Purchase flow completes successfully
- [ ] App recognizes premium status
- [ ] Premium features are unlocked

#### ✅ Restore Purchases
- [ ] Test restore purchases functionality
- [ ] Previous purchases are restored correctly
- [ ] Premium status is updated

### Integration Points

#### ✅ Profile Screen
- [ ] Premium status displays correctly
- [ ] Upgrade button works
- [ ] Premium badge appears when subscribed

#### ✅ App Behavior
- [ ] Premium features are properly gated
- [ ] Non-premium users see upgrade prompts
- [ ] Premium users have full access

## 🔧 Troubleshooting

### Common Issues

#### 1. Paywall Not Appearing
**Symptoms:** Button tap doesn't show paywall
**Solutions:**
- Check RevenueCat dashboard for offering configuration
- Verify API key is correct for production
- Check console logs for errors

#### 2. Purchase Flow Fails
**Symptoms:** Purchase doesn't complete
**Solutions:**
- Ensure using sandbox Apple ID
- Check App Store Connect for product configuration
- Verify entitlements are properly configured

#### 3. Premium Status Not Updating
**Symptoms:** Purchase completes but app doesn't recognize premium
**Solutions:**
- Check entitlement identifier matches RevenueCat dashboard
- Verify customer info refresh after purchase
- Check for proper state management

### Debug Information

#### Console Logs to Monitor
```
✅ RevenueCat SDK configured successfully
✅ RevenueCat SDK test successful - Customer info retrieved
Opening RevenueCat paywall for paywall ID: pw1765de696362472e
Available offerings: [object details]
Paywall result: [PURCHASED/CANCELLED/etc]
```

#### Key Files to Check
- `config/revenuecat.ts` - API keys and configuration
- `app/_layout.tsx` - SDK initialization
- `app/(main)/profile/index.tsx` - Paywall implementation

## 📱 Testing Scenarios

### Scenario 1: New User
1. Install app from TestFlight
2. Complete onboarding
3. Navigate to Profile
4. Tap "Upgrade to Premium"
5. Verify paywall appears
6. Test purchase flow

### Scenario 2: Existing Premium User
1. Install app with existing premium subscription
2. Launch app
3. Verify premium status is recognized
4. Check that premium features are accessible

### Scenario 3: Subscription Management
1. Purchase subscription
2. Verify premium access
3. Test restore purchases on new device
4. Verify subscription status

## 🎯 Success Criteria

### ✅ Must Pass
- [ ] App launches without RevenueCat errors
- [ ] Paywall presents correctly
- [ ] Purchase flow works in sandbox
- [ ] Premium status updates properly
- [ ] Restore purchases works

### ✅ Nice to Have
- [ ] Paywall design matches expectations
- [ ] Purchase confirmation flows smoothly
- [ ] Error handling works gracefully

## 🔗 RevenueCat Dashboard

### Key Settings to Verify
1. **Products:** Ensure all subscription products are configured
2. **Offerings:** Verify current offering includes correct products
3. **Entitlements:** Check that 'premium' entitlement is properly set up
4. **Paywalls:** Confirm paywall ID `pw1765de696362472e` is configured

### API Keys
- **Sandbox:** Used for TestFlight testing
- **Production:** Used for App Store release

## 📞 Support

If issues arise during TestFlight testing:

1. Check console logs for specific error messages
2. Verify RevenueCat dashboard configuration
3. Test with different sandbox Apple IDs
4. Review this guide for troubleshooting steps

## 🚀 Ready for App Store

Once TestFlight testing passes all criteria:
- [ ] All purchase flows work correctly
- [ ] Premium features are properly gated
- [ ] Subscription management works
- [ ] No crashes or critical errors
- [ ] User experience is smooth

The app is ready for App Store submission! 🎉
