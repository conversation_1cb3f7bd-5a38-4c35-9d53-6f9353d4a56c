# Firebase Setup Guide for Traveler App

This guide will walk you through setting up Firebase for the Traveler App.

## Prerequisites

1. Node.js and npm installed
2. A Google account
3. Firebase CLI installed (`npm install -g firebase-tools`)

## Step 1: Create a Firebase Project

1. Go to [Firebase Console](https://console.firebase.google.com/) and sign in with your Google account
2. Click "Add project" and follow the steps to create a new project
3. Give your project a name (e.g., "TravelerApp")
4. Enable Google Analytics if desired (recommended)
5. Click "Create project"

## Step 2: Register Your App with Firebase

1. Once your project is created, click the "Web" icon (</>) to add a web app
2. Give your app a nickname (e.g., "TravelerApp Web")
3. Check the box for "Also set up Firebase Hosting" if desired
4. Click "Register app"
5. You'll see your Firebase configuration. Copy this information.

## Step 3: Update Firebase Configuration

1. Open the file `services/firebase/config.ts` in your project
2. Replace the placeholder values with your actual Firebase configuration:

```typescript
const firebaseConfig = {
  apiKey: "YOUR_API_KEY",
  authDomain: "YOUR_AUTH_DOMAIN",
  projectId: "YOUR_PROJECT_ID",
  storageBucket: "YOUR_STORAGE_BUCKET",
  messagingSenderId: "YOUR_MESSAGING_SENDER_ID",
  appId: "YOUR_APP_ID",
  measurementId: "YOUR_MEASUREMENT_ID"
};
```

## Step 4: Enable Authentication

1. In the Firebase Console, navigate to "Authentication"
2. Click "Get started"
3. In the "Sign-in method" tab, enable "Email/Password"
4. Optionally, enable other authentication methods as needed

## Step 5: Set Up Firestore Database

1. Navigate to "Firestore Database" in the Firebase Console
2. Click "Create database"
3. Start in production mode (or test mode if just testing)
4. Choose a location closest to your users
5. Wait for the database to be provisioned

## Step 6: Set Up Firebase Storage

1. Navigate to "Storage" in the Firebase Console
2. Click "Get started"
3. Follow the setup steps, selecting production or test mode
4. Set your storage location

## Step 7: Deploy Firebase Security Rules

Run the following command to deploy your Firestore security rules:

```bash
npm run firebase:deploy:rules
```

Or manually deploy from the Firebase Console by copying the rules from `firestore.rules` and `storage.rules`.

## Step 8: Deploy Firestore Indexes

Run the following command to deploy your Firestore indexes:

```bash
npm run firebase:deploy:indexes
```

## Using Firebase Emulators for Development

For local development, you can use Firebase emulators to avoid making changes to your production Firebase project:

1. Install the Firebase CLI globally if you haven't already:
   ```bash
   npm install -g firebase-tools
   ```

2. Log in to Firebase:
   ```bash
   firebase login
   ```

3. Initialize Firebase in your project directory:
   ```bash
   firebase init
   ```
   Select Firestore, Storage, and Emulators when prompted.

4. Start the emulators:
   ```bash
   npm run firebase:emulators
   ```

5. In another terminal, start your app in emulator mode:
   ```bash
   npm run firebase:dev
   ```

## Troubleshooting

- If you encounter CORS issues with Firebase, make sure your app's domain is added to the authorized domains in the Firebase Console.
- Check the Firebase documentation for the latest changes: [Firebase Docs](https://firebase.google.com/docs)
- For emulator issues, ensure ports 9099 (Auth), 8080 (Firestore), and 9199 (Storage) are available. 