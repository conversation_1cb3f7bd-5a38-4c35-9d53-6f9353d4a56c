# 🌍 TravelerApp

TravelerApp is an AI-powered travel planning application that helps users organize and manage their trips with personalized recommendations.

## 🚀 Features

- **AI-Powered Trip Planning**: Get personalized trip itineraries generated by AI
- **Quick Travel Suggestions**: Ask for instant travel recommendations
- **Interactive Map**: Save and organize places of interest
- **Trip Management**: Keep track of all your travel plans in one place

## 📱 Tech Stack

- **Frontend**: React Native with TypeScript, Expo Router
- **Backend/Database**: Firebase (Authentication, Firestore)
- **UI Framework**: React Native Paper
- **AI Integration**: OpenAI API (GPT-4 and GPT-3.5)
- **Maps**: React Native Maps with Google Maps integration

## 🛠️ Getting Started

### Prerequisites

- Node.js (v14 or higher)
- npm or yarn
- Expo CLI
- Firebase account
- OpenAI API key

### Installation

1. Clone the repository:
   ```
   git clone https://github.com/yourusername/traveler-app.git
   cd traveler-app
   ```

2. Install dependencies:
   ```
   npm install
   ```

3. Set up Firebase:
   - Create a Firebase project
   - Enable Authentication (Email/Password) and Firestore
   - Update the Firebase config in `services/firebase/config.ts`

4. Set up OpenAI API:
   - Get an API key from OpenAI
   - Update the API key in `services/ai/openai.ts`

5. Start the development server:
   ```
   npm start
   ```

6. Run on iOS/Android:
   ```
   npm run ios
   # or
   npm run android
   ```

## 📁 Project Structure

```
traveler-app/
├── app/                           # Main app folder (Expo Router)
│   ├── _layout.tsx                # Root layout with navigation
│   ├── index.tsx                  # Entry redirect
│   ├── (auth)/                    # Authentication screens
│   ├── (main)/                    # Main app screens
│   ├── trip/                      # Trip-related screens
│   └── modal/                     # Modal screens
├── components/                    # Reusable components
├── constants/                     # App constants
├── hooks/                         # Custom React hooks
├── services/                      # Service integrations
│   ├── firebase/                  # Firebase services
│   ├── ai/                        # AI services
│   └── maps/                      # Map services
├── utils/                         # Utility functions
├── contexts/                      # React contexts
└── types/                         # TypeScript type definitions
```

## 📊 Database Schema

The app uses Firebase Firestore with the following collections:
- `users`: User profile data and preferences
- `trips`: Trip plans with date and AI responses
- `places`: Saved locations from the map
- `ai_logs`: AI usage tracking
- `news`: Travel updates and alerts

## 📱 Screens

- **Authentication**: Login, Signup
- **Dashboard**: Overview of trips and travel news
- **Quick Plan**: AI assistant for quick travel suggestions
- **Map**: Interactive map with saved places
- **Profile**: User settings and preferences

## 🤝 Contributing

Contributions are welcome! Please feel free to submit a Pull Request.

## 📄 License

This project is licensed under the MIT License - see the LICENSE file for details.

## ✨ Acknowledgements

- OpenAI for providing the AI capabilities
- Firebase for the backend infrastructure
- Expo team for the excellent React Native tooling
