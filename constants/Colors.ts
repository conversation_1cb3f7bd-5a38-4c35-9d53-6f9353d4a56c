// Color palette inspired by the provided UI designs
const tintColorLight = '#3A7BF8'; // Primary blue from screenshots
const tintColorDark = '#4C8CFF';

export default {
  light: {
    primary: '#3A7BF8', // Primary blue for buttons and active elements
    secondary: '#F87A3A', // Orange accent for notifications and alerts
    text: '#000000',
    lightText: '#838383', // Gray text for secondary information
    background: '#FFFFFF',
    card: '#FFFFFF',
    border: '#E0E0E0',
    notification: '#FF3B30',
    accent: '#06C267', // Green for success states
    lightBlue: '#EBF2FF', // Light blue background
    mapMarker: '#3A7BF8',
    iconActive: '#3A7BF8',
    iconInactive: '#838383',
    surfaceVariant: '#EBF2FF', // Same as lightBlue for consistency
  },
  dark: {
    primary: '#4C8CFF',
    secondary: '#FF9C70',
    text: '#FFFFFF',
    lightText: '#BBBBBB',
    background: '#121212',
    card: '#1E1E1E',
    border: '#2C2C2C',
    notification: '#FF453A',
    accent: '#30D17C',
    lightBlue: '#1A2742',
    mapMarker: '#4C8CFF',
    iconActive: '#4C8CFF',
    iconInactive: '#7C7C7C',
    surfaceVariant: '#1A2742', // Same as lightBlue for consistency
  }
}; 