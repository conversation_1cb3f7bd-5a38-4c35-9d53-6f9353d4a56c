{"expo": {"scheme": "travelrapp", "plugins": ["expo-router", "expo-font", ["expo-location", {"locationAlwaysAndWhenInUsePermission": "Allow Traveler App to use your location."}]], "name": "Traveler App", "slug": "traveler-app", "version": "1.0.0", "orientation": "portrait", "userInterfaceStyle": "light", "splash": {"resizeMode": "contain", "backgroundColor": "#ffffff"}, "assetBundlePatterns": ["**/*", "node_modules/@expo/vector-icons/fonts/*"], "packagerOpts": {"config": "metro.config.js"}, "ios": {"supportsTablet": true, "config": {"googleMapsApiKey": "YOUR_IOS_API_KEY_HERE"}, "bundleIdentifier": "com.anonymous.traveler-app"}, "android": {"adaptiveIcon": {"backgroundColor": "#ffffff"}, "config": {"googleMaps": {"apiKey": "YOUR_ANDROID_API_KEY_HERE"}}, "permissions": ["android.permission.ACCESS_COARSE_LOCATION", "android.permission.ACCESS_FINE_LOCATION"]}, "web": {"bundler": "metro"}, "experiments": {"typedRoutes": true}}}