# Firebase Storage Helper Usage Guide

The Firebase Storage Helper provides utilities for uploading, managing, and deleting images in your Firebase Storage.

## Installation

All dependencies are already installed in the project:
- expo-file-system
- expo-image-manipulator
- firebase (storage)

## Usage Examples

### Basic Image Upload

```typescript
import { uploadImage } from './services/firebase/storage';

// Basic upload with default options
const uploadMyImage = async (uri: string) => {
  try {
    const downloadURL = await uploadImage(uri, 'path/to/save/image.jpg');
    console.log('Image uploaded successfully! URL:', downloadURL);
    return downloadURL;
  } catch (error) {
    console.error('Error uploading image:', error);
  }
};

// Upload with custom options
const uploadHighQuality = async (uri: string) => {
  const options = {
    resize: true,
    quality: 0.95, // Higher quality (95%)
    maxSize: 1600  // Larger max dimension
  };
  
  const downloadURL = await uploadImage(uri, 'path/to/high-quality.jpg', options);
  return downloadURL;
};
```

### Upload Profile Pictures

```typescript
import { uploadProfilePicture } from './services/firebase/storage';

const saveUserProfile = async (userId: string, imageUri: string) => {
  try {
    const profileImageURL = await uploadProfilePicture(userId, imageUri);
    
    // Save user profile data with the image URL
    // e.g., update Firestore user document
    return profileImageURL;
  } catch (error) {
    console.error('Error saving profile:', error);
  }
};
```

### Upload Trip Images

```typescript
import { uploadTripImage } from './services/firebase/storage';

const addTripImages = async (userId: string, tripId: string, imageUris: string[]) => {
  try {
    const imageURLs = [];
    
    // Upload cover image
    if (imageUris.length > 0) {
      const coverURL = await uploadTripImage(userId, tripId, imageUris[0]);
      imageURLs.push(coverURL);
    }
    
    // Upload additional images
    for (let i = 1; i < imageUris.length; i++) {
      const imageURL = await uploadTripImage(userId, tripId, imageUris[i], i);
      imageURLs.push(imageURL);
    }
    
    return imageURLs;
  } catch (error) {
    console.error('Error uploading trip images:', error);
  }
};
```

### Upload Place Images

```typescript
import { uploadPlaceImage } from './services/firebase/storage';

const savePlaceWithImages = async (userId: string, placeId: string, imageUris: string[]) => {
  const imageURLs = [];
  
  // Similar to trip images
  if (imageUris.length > 0) {
    const coverURL = await uploadPlaceImage(userId, placeId, imageUris[0]);
    imageURLs.push(coverURL);
    
    for (let i = 1; i < imageUris.length; i++) {
      const imageURL = await uploadPlaceImage(userId, placeId, imageUris[i], i);
      imageURLs.push(imageURL);
    }
  }
  
  return imageURLs;
};
```

### Deleting Images

```typescript
import { deleteImage } from './services/firebase/storage';

const removeImage = async (imageUrl: string) => {
  try {
    await deleteImage(imageUrl);
    console.log('Image deleted successfully!');
  } catch (error) {
    console.error('Error deleting image:', error);
  }
};
```

## Tips

1. **Image Compression**: By default, all images are resized and compressed to optimize storage usage and load time
   - Maximum dimension: 1200px
   - JPEG compression quality: 80%

2. **Error Handling**: Always handle errors when working with storage operations

3. **Security Rules**: Make sure your Firebase Storage security rules are properly configured to allow these operations

4. **Storage Paths**: The helper uses consistent paths:
   - User profiles: `users/{userId}/profile.jpg`
   - Trip images: `users/{userId}/trips/{tripId}/[cover.jpg or image_{index}.jpg]`
   - Place images: `users/{userId}/places/{placeId}/[cover.jpg or image_{index}.jpg]` 