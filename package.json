{"name": "traveler-app", "version": "1.0.0", "main": "expo-router/entry", "scripts": {"start": "expo start", "android": "expo run:android", "ios": "expo run:ios", "web": "expo start --web", "deploy": "npx expo export -p web && npx eas-cli@latest deploy", "firebase:emulators": "firebase emulators:start", "firebase:deploy:rules": "firebase deploy --only firestore:rules,storage:rules", "firebase:deploy:indexes": "firebase deploy --only firestore:indexes", "firebase:dev": "cross-env EXPO_PUBLIC_USE_FIREBASE_EMULATOR=true expo start", "postinstall": "node fix-vector-icons.js && node fix-firebase-modules.js", "fix-icons": "node fix-vector-icons.js", "fix-firebase": "node fix-firebase-modules.js", "clean": "expo start --clear"}, "dependencies": {"@expo/vector-icons": "^14.1.0", "@react-native-async-storage/async-storage": "^2.1.2", "@react-native-community/datetimepicker": "^8.3.0", "@react-navigation/bottom-tabs": "^7.3.13", "@react-navigation/native": "^7.1.9", "canvas": "^3.1.0", "expo": "^53.0.5", "expo-asset": "^11.0.5", "expo-constants": "~17.1.5", "expo-file-system": "^18.0.12", "expo-font": "~13.3.0", "expo-image-manipulator": "~13.1.5", "expo-image-picker": "~16.1.4", "expo-linear-gradient": "~14.1.4", "expo-linking": "~7.1.4", "expo-localization": "^16.1.5", "expo-location": "~18.1.4", "expo-router": "~5.0.4", "expo-splash-screen": "~0.30.8", "expo-status-bar": "~2.2.3", "expo-superwall": "^0.0.14", "firebase": "^9.22.2", "i18next": "^25.2.1", "idb": "7.1.1", "moment": "^2.30.1", "openai": "^4.95.1", "react": "19.0.0", "react-dom": "19.0.0", "react-i18next": "^15.5.2", "react-native": "0.79.2", "react-native-calendar-picker": "^6.1.5", "react-native-copilot": "^3.3.3", "react-native-draggable-flatlist": "^4.0.3", "react-native-gesture-handler": "~2.24.0", "react-native-ionicons": "^4.6.5", "react-native-maps": "1.20.1", "react-native-paper": "^5.13.3", "react-native-reanimated": "^3.17.5", "react-native-reanimated-carousel": "^4.0.2", "react-native-safe-area-context": "5.4.0", "react-native-screens": "~4.10.0", "react-native-sortable-list": "^0.0.25", "react-native-toast-message": "^2.3.0", "react-native-vector-icons": "^10.0.3", "react-native-web": "^0.20.0"}, "devDependencies": {"@babel/core": "^7.20.0", "@types/react": "~19.0.10", "babel-plugin-module-resolver": "^5.0.2", "cross-env": "^7.0.3", "firebase-tools": "^13.35.1", "typescript": "^5.3.3"}, "private": true, "resolutions": {"firebase": "9.22.2", "@firebase/*": "9.22.2"}}