import React, { createContext, useState, useContext } from 'react';
import { DefaultTheme, MD3DarkTheme, MD3Theme } from 'react-native-paper';
import { useColorScheme } from 'react-native';
import Colors from '../constants/Colors';

type ThemeContextType = {
  theme: MD3Theme;
  isDarkMode: boolean;
  toggleTheme: () => void;
};

// Define our custom theme based on React Native Paper and our Colors.ts file
const lightTheme = {
  ...DefaultTheme,
  colors: {
    ...DefaultTheme.colors,
    primary: Colors.light.primary,
    secondary: Colors.light.secondary,
    background: Colors.light.background,
    surface: Colors.light.card,
    error: Colors.light.notification,
    onSurface: Colors.light.text,
    outline: Colors.light.border,
    surfaceVariant: Colors.light.lightBlue,
    secondaryContainer: Colors.light.accent,
  },
  roundness: 12,
};

const darkTheme = {
  ...MD3DarkTheme,
  colors: {
    ...MD3DarkTheme.colors,
    primary: Colors.dark.primary,
    secondary: Colors.dark.secondary,
    background: Colors.dark.background,
    surface: Colors.dark.card,
    error: Colors.dark.notification,
    onSurface: Colors.dark.text,
    outline: Colors.dark.border,
    surfaceVariant: Colors.dark.lightBlue,
    secondaryContainer: Colors.dark.accent,
  },
  roundness: 12,
};

const ThemeContext = createContext<ThemeContextType>({
  theme: lightTheme,
  isDarkMode: false,
  toggleTheme: () => {},
});

export const useTheme = () => useContext(ThemeContext);

export const ThemeProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const colorScheme = useColorScheme();
  const [isDarkMode, setIsDarkMode] = useState(colorScheme === 'dark');

  const toggleTheme = () => {
    setIsDarkMode(!isDarkMode);
  };

  const theme = isDarkMode ? darkTheme : lightTheme;

  return (
    <ThemeContext.Provider value={{ theme, isDarkMode, toggleTheme }}>
      {children}
    </ThemeContext.Provider>
  );
}; 