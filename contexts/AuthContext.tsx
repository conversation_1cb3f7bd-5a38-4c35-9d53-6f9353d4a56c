import React, { createContext, useState, useEffect, useContext } from 'react';
// Import Firebase auth functions
import { getAuth, onAuthStateChanged, signInWithEmailAndPassword, createUserWithEmailAndPassword, signOut, User } from 'firebase/auth';
// Import Firebase app
import { initializeApp } from 'firebase/app';
import { registerUser, initializeUserProfile } from '../services/firebase';

// Firebase configuration
const firebaseConfig = {
  apiKey: "AIzaSyDyB2FJwBKOLq4KzRLUn8VPY6s-LJNBeZI",
  authDomain: "traveling-app-c19a8.firebaseapp.com",
  projectId: "traveling-app-c19a8",
  storageBucket: "traveling-app-c19a8.appspot.com",
  messagingSenderId: "991094040299",
  appId: "1:991094040299:web:06cfaa17634d4f840d00ae",
  measurementId: "G-ZYQ1EDRSVF"
};

// Initialize Firebase
const app = initializeApp(firebaseConfig);
const auth = getAuth(app);

type AuthResult = {
  success: boolean;
  error?: string;
  user?: User;
};

type AuthContextType = {
  user: User | null;
  loading: boolean;
  signIn: (email: string, password: string) => Promise<AuthResult>;
  signUp: (email: string, password: string, username?: string) => Promise<AuthResult>;
  logout: () => Promise<void>;
};

const AuthContext = createContext<AuthContextType>({
  user: null,
  loading: true,
  signIn: async () => ({ success: false }),
  signUp: async () => ({ success: false }),
  logout: async () => {},
});

export const useAuth = () => useContext(AuthContext);

export const AuthProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [user, setUser] = useState<User | null>(null);
  const [loading, setLoading] = useState(true);
  const [initialized, setInitialized] = useState(false);

  // First effect - force logout on initial load
  useEffect(() => {
    const initAuth = async () => {
      console.log('AuthProvider: Initializing and forcing logout');
      try {
        // Always force logout on app start to ensure clean auth state
        await signOut(auth);
        console.log('AuthProvider: Forced logout successful');
      } catch (error) {
        console.log('AuthProvider: Error during forced logout:', error);
      } finally {
        setInitialized(true);
      }
    };
    
    initAuth();
  }, []);
  
  // Second effect - set up auth state listener after forced logout
  useEffect(() => {
    if (!initialized) return;
    
    console.log('AuthProvider: Setting up auth state listener');
    
    const unsubscribe = onAuthStateChanged(auth, (user) => {
      console.log('AuthProvider: Auth state changed, user:', user?.email || 'no user');
      setUser(user);
      setLoading(false);
      
      // If the user exists but just logged in, ensure profile is initialized
      if (user) {
        initializeUserProfile(user).catch(error => 
          console.error('Error initializing user profile on auth state change:', error)
        );
      }
    });

    return unsubscribe;
  }, [initialized]);

  const signIn = async (email: string, password: string): Promise<AuthResult> => {
    try {
      const userCredential = await signInWithEmailAndPassword(auth, email, password);
      return { success: true, user: userCredential.user };
    } catch (error: any) {
      console.error('Sign in error:', error.code, error.message);
      return { success: false, error: error.message };
    }
  };

  const signUp = async (email: string, password: string, username?: string): Promise<AuthResult> => {
    try {
      console.log('AuthContext: Registering user with email', email);
      console.log('AuthContext: Username provided:', username);
      
      // Ensure username is not empty or undefined before passing it
      const cleanUsername = username?.trim() || null;
      console.log('AuthContext: Cleaned username:', cleanUsername);
      
      const result = await registerUser(email, password, cleanUsername);
      console.log('AuthContext: Registration result', result);
      
      if (result.success && result.user) {
        // Make sure we update the user profile with the username 
        await initializeUserProfile(result.user, cleanUsername);
        console.log('AuthContext: User profile initialized with username:', cleanUsername);
      }
      
      return result;
    } catch (error: any) {
      console.error('Sign up error in AuthContext:', error);
      return { success: false, error: error.message };
    }
  };

  const logout = async () => {
    try {
      await signOut(auth);
    } catch (error) {
      throw error;
    }
  };

  return (
    <AuthContext.Provider value={{ user, loading, signIn, signUp, logout }}>
      {children}
    </AuthContext.Provider>
  );
}; 