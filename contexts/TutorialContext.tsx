import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { useAuth } from './AuthContext';

const TUTORIAL_STORAGE_KEY = 'tripzy_tutorial_completed';

interface TutorialContextType {
  isTutorialCompleted: boolean;
  startTutorial: () => void;
  completeTutorial: () => void;
  resetTutorial: () => void;
}

const TutorialContext = createContext<TutorialContextType | undefined>(undefined);

export const TutorialProvider: React.FC<{ children: ReactNode }> = ({ children }) => {
  const [isTutorialCompleted, setIsTutorialCompleted] = useState(true); // Default to true to prevent auto-start
  const [isLoading, setIsLoading] = useState(true);
  const { user } = useAuth();

  // Load tutorial completion status
  useEffect(() => {
    const loadTutorialStatus = async () => {
      try {
        if (user?.uid) {
          const userTutorialKey = `${TUTORIAL_STORAGE_KEY}_${user.uid}`;
          const completed = await AsyncStorage.getItem(userTutorialKey);
          setIsTutorialCompleted(completed === 'true');
        }
      } catch (error) {
        console.error('Error loading tutorial status:', error);
        setIsTutorialCompleted(false);
      } finally {
        setIsLoading(false);
      }
    };

    loadTutorialStatus();
  }, [user]);

  const startTutorial = () => {
    // This will be called by the copilot start method
    console.log('Starting tutorial...');
  };

  const completeTutorial = async () => {
    try {
      if (user?.uid) {
        const userTutorialKey = `${TUTORIAL_STORAGE_KEY}_${user.uid}`;
        await AsyncStorage.setItem(userTutorialKey, 'true');
        setIsTutorialCompleted(true);
      }
    } catch (error) {
      console.error('Error saving tutorial completion:', error);
    }
  };

  const resetTutorial = async () => {
    try {
      if (user?.uid) {
        const userTutorialKey = `${TUTORIAL_STORAGE_KEY}_${user.uid}`;
        await AsyncStorage.removeItem(userTutorialKey);
        setIsTutorialCompleted(false);
      }
    } catch (error) {
      console.error('Error resetting tutorial:', error);
    }
  };

  const value: TutorialContextType = {
    isTutorialCompleted,
    startTutorial,
    completeTutorial,
    resetTutorial,
  };

  if (isLoading) {
    return <>{children}</>;
  }

  return (
    <TutorialContext.Provider value={value}>
      {children}
    </TutorialContext.Provider>
  );
};

export const useTutorial = (): TutorialContextType => {
  const context = useContext(TutorialContext);
  if (!context) {
    throw new Error('useTutorial must be used within a TutorialProvider');
  }
  return context;
};
