// Learn more https://docs.expo.io/guides/customizing-metro
const { getDefaultConfig } = require('expo/metro-config');
const path = require('path');

/** @type {import('expo/metro-config').MetroConfig} */
const config = getDefaultConfig(__dirname);

// Create a mock for the idb module to fix Firebase compatibility issues
const idbMock = `
module.exports = {
  openDB: () => Promise.resolve({
    get: () => Promise.resolve(null),
    put: () => Promise.resolve(),
    delete: () => Promise.resolve(),
    clear: () => Promise.resolve(),
    close: () => Promise.resolve()
  }),
  deleteDB: () => Promise.resolve(),
  unwrap: (value) => value,
  wrap: (value) => value
};
`;

// Add module mock for idb
config.resolver.extraNodeModules = {
  ...config.resolver.extraNodeModules,
  'idb': path.resolve(__dirname, 'shims/idb.js'),
  '@expo/vector-icons': path.resolve(__dirname, 'node_modules/@expo/vector-icons'),
};

// Add module mapper for idb
config.resolver.resolveRequest = (context, moduleName, platform) => {
  // Intercept requests for idb module
  if (moduleName === 'idb') {
    return {
      filePath: path.resolve(__dirname, 'shims/idb.js'),
      type: 'sourceFile',
    };
  }
  // Let Metro handle other modules
  return context.resolveRequest(context, moduleName, platform);
};

// Add additional customizations to resolve C++ exceptions
config.resolver.sourceExts = ['jsx', 'js', 'ts', 'tsx', 'json', 'mjs', 'cjs'];

// Disable package exports to fix Firebase auth issues
config.resolver.unstable_enablePackageExports = false;
config.resolver.assetExts = [
  'png', 'jpg', 'jpeg', 'gif', 'svg',
  // Add font extensions to properly handle font files
  'ttf', 'otf', 'woff', 'woff2', 'eot'
];

// Handle symlinks properly
config.resolver.resolverMainFields = ['react-native', 'browser', 'main'];

// Avoid crashes when processing large objects
config.maxWorkers = 2;
config.transformer.minifierConfig = {
  compress: {
    drop_console: false, // Keep console logs for debugging
    global_defs: {
      __DEV__: true, // Ensure __DEV__ is properly defined
    },
  },
};

// This is handled in the earlier extraNodeModules configuration

// Prevent out-of-memory issues
config.transformer.assetPlugins = ['expo-asset/tools/hashAssetFiles'];

// Increase the heap size for Node to avoid memory issues
process.env.NODE_OPTIONS = process.env.NODE_OPTIONS || '--max-old-space-size=4096';

module.exports = config; 