#!/bin/bash

# Verify iOS Offline Setup Script
# This script checks if the iOS app is properly configured for offline usage

echo "🔍 Verifying iOS offline setup..."
echo ""

# Check if bundle exists
if [ -f "ios/main.jsbundle" ]; then
    BUNDLE_SIZE=$(du -h ios/main.jsbundle | cut -f1)
    echo "✅ Production bundle found: ios/main.jsbundle ($BUNDLE_SIZE)"
else
    echo "❌ Production bundle not found: ios/main.jsbundle"
    echo "   Run: npm run build:ios-bundle"
    exit 1
fi

# Check if assets exist
if [ -d "ios/assets" ]; then
    ASSET_COUNT=$(find ios/assets -type f | wc -l | tr -d ' ')
    echo "✅ Assets directory found: ios/assets ($ASSET_COUNT files)"
else
    echo "❌ Assets directory not found: ios/assets"
    echo "   Run: npm run build:ios-bundle"
    exit 1
fi

# Check Xcode environment configuration
if [ -f "ios/.xcode.env" ]; then
    echo "✅ Xcode environment file found: ios/.xcode.env"
    
    if grep -q "SKIP_BUNDLING=0" ios/.xcode.env; then
        echo "✅ SKIP_BUNDLING is set to 0 (bundling enabled)"
    else
        echo "⚠️  SKIP_BUNDLING not set to 0"
    fi
    
    if grep -q "FORCE_BUNDLING=1" ios/.xcode.env; then
        echo "✅ FORCE_BUNDLING is set to 1 (force production bundle)"
    else
        echo "⚠️  FORCE_BUNDLING not set to 1"
    fi
else
    echo "❌ Xcode environment file not found: ios/.xcode.env"
    exit 1
fi

# Check if bundle is included in Xcode project
if grep -q "main.jsbundle" ios/TravelerApp.xcodeproj/project.pbxproj; then
    echo "✅ Bundle is included in Xcode project"
else
    echo "❌ Bundle is not included in Xcode project"
    echo "   The bundle needs to be added to the Xcode project manually"
    exit 1
fi

# Check if build script exists
if grep -q "Bundle React Native code and images" ios/TravelerApp.xcodeproj/project.pbxproj; then
    echo "✅ React Native build script found in Xcode project"
else
    echo "❌ React Native build script not found in Xcode project"
    exit 1
fi

echo ""
echo "🎉 iOS offline setup verification complete!"
echo ""
echo "📋 Next steps:"
echo "1. Open Xcode: open ios/TravelerApp.xcworkspace"
echo "2. Select your physical device as the target"
echo "3. Build and run the app (⌘+R)"
echo "4. The app should run without Metro bundler"
echo ""
echo "🔧 To rebuild the bundle:"
echo "   ./scripts/build-ios-bundle.sh"
echo ""
echo "📱 To run directly with Expo CLI:"
echo "   npx expo run:ios --device"
