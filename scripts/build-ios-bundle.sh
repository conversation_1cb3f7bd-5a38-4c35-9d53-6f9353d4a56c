#!/bin/bash

# Build iOS Production Bundle Script
# This script creates a production JS bundle for offline iOS usage

set -e

echo "🚀 Building iOS production bundle for offline usage..."

# Clean previous bundle
if [ -f "ios/main.jsbundle" ]; then
    echo "🧹 Removing previous bundle..."
    rm -f ios/main.jsbundle
fi

if [ -d "ios/assets" ]; then
    echo "🧹 Removing previous assets..."
    rm -rf ios/assets
fi

# Create production bundle
echo "📦 Creating production bundle..."
npx expo export:embed \
    --platform ios \
    --dev false \
    --minify true \
    --bundle-output ios/main.jsbundle \
    --assets-dest ios/

# Verify bundle was created
if [ -f "ios/main.jsbundle" ]; then
    BUNDLE_SIZE=$(du -h ios/main.jsbundle | cut -f1)
    echo "✅ Bundle created successfully!"
    echo "📊 Bundle size: $BUNDLE_SIZE"
    echo "📁 Bundle location: ios/main.jsbundle"
    
    if [ -d "ios/assets" ]; then
        ASSET_COUNT=$(find ios/assets -type f | wc -l | tr -d ' ')
        echo "🖼️  Assets copied: $ASSET_COUNT files"
    fi
    
    echo ""
    echo "🎯 Your app is now ready for offline usage!"
    echo "📱 Build and run on device: npx expo run:ios --device"
    echo "🏗️  Or build with Xcode directly from ios/TravelerApp.xcworkspace"
else
    echo "❌ Failed to create bundle!"
    exit 1
fi
