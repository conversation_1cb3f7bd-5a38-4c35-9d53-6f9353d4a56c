module.exports = function (api) {
  api.cache(true);
  return {
    presets: ['babel-preset-expo'],
    plugins: [
      'react-native-reanimated/plugin',
      [
        'module-resolver',
        {
          alias: {
            '@': '.',
          },
        },
      ],
    ],
  };
}; 
// Added by fix-firebase-final.js to support .mjs files
// This helps with Firebase compatibility in React Native
