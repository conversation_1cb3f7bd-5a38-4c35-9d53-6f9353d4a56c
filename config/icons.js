/**
 * This file ensures that all vector icon fonts are properly loaded
 * It helps resolve import issues with @expo/vector-icons
 */

// This is a workaround for the "Unable to resolve module" error for icon fonts
export const IconFontPaths = {
  Entypo: require('@expo/vector-icons/fonts/Entypo.ttf'),
  AntDesign: require('@expo/vector-icons/fonts/AntDesign.ttf'),
  EvilIcons: require('@expo/vector-icons/fonts/EvilIcons.ttf'),
  Feather: require('@expo/vector-icons/fonts/Feather.ttf'),
  FontAwesome: require('@expo/vector-icons/fonts/FontAwesome.ttf'),
  FontAwesome5_Brands: require('@expo/vector-icons/fonts/FontAwesome5_Brands.ttf'),
  FontAwesome5_Regular: require('@expo/vector-icons/fonts/FontAwesome5_Regular.ttf'),
  FontAwesome5_Solid: require('@expo/vector-icons/fonts/FontAwesome5_Solid.ttf'),
  Fontisto: require('@expo/vector-icons/fonts/Fontisto.ttf'),
  Foundation: require('@expo/vector-icons/fonts/Foundation.ttf'),
  Ionicons: require('@expo/vector-icons/fonts/Ionicons.ttf'),
  MaterialCommunityIcons: require('@expo/vector-icons/fonts/MaterialCommunityIcons.ttf'),
  MaterialIcons: require('@expo/vector-icons/fonts/MaterialIcons.ttf'),
  Octicons: require('@expo/vector-icons/fonts/Octicons.ttf'),
  SimpleLineIcons: require('@expo/vector-icons/fonts/SimpleLineIcons.ttf'),
  Zocial: require('@expo/vector-icons/fonts/Zocial.ttf'),
};

// This function can be used to preload all icon fonts if needed
export const preloadIconFonts = async () => {
  // The fonts are already preloaded when imported above
  return true;
}; 