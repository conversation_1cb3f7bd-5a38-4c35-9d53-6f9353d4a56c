import { getFunctions, httpsCallable } from 'firebase/functions';
import AsyncStorage from '@react-native-async-storage/async-storage';

// Cache key prefix for AsyncStorage
const ENGLISH_CITY_CACHE_PREFIX = 'english_city_';

// In-memory cache for faster access during app session
const englishCityCache: Record<string, string> = {};

/**
 * Normalizes a city name to its English version using Firebase Cloud Function
 * Caches results in both memory and AsyncStorage for efficiency
 * 
 * @param originalCity - City name in any language
 * @returns Promise resolving to the English version of the city name
 */
export const getEnglishCityName = async (originalCity: string): Promise<string> => {
  if (!originalCity) {
    throw new Error('City name is required');
  }

  const normalizedOriginalCity = originalCity.trim().toLowerCase();
  
  try {
    // First check in-memory cache (fastest)
    if (englishCityCache[normalizedOriginalCity]) {
      console.log(`Using in-memory cached English name for ${originalCity}`);
      return englishCityCache[normalizedOriginalCity];
    }
    
    // Then check AsyncStorage cache
    const cacheKey = `${ENGLISH_CITY_CACHE_PREFIX}${normalizedOriginalCity}`;
    const cachedCity = await AsyncStorage.getItem(cacheKey);
    
    if (cachedCity) {
      console.log(`Using AsyncStorage cached English name for ${originalCity}`);
      // Update in-memory cache
      englishCityCache[normalizedOriginalCity] = cachedCity;
      return cachedCity;
    }
    
    // If not in any cache, call Firebase function
    console.log(`Fetching English name for ${originalCity} from Firebase function`);
    const functions = getFunctions(undefined, 'europe-west1');
    const normalizeCity = httpsCallable(functions, 'getEnglishCityName');
    
    try {
      const response = await normalizeCity({ city: originalCity });
      if (response.data && typeof response.data === 'object' && 'englishCity' in response.data) {
        const englishCity = response.data.englishCity as string;
        
        // Save to both caches
        englishCityCache[normalizedOriginalCity] = englishCity;
        await AsyncStorage.setItem(cacheKey, englishCity);
        
        return englishCity;
      } else {
        console.error('Invalid response format from Firebase function');
        return originalCity;
      }
    } catch (error) {
      console.error('Error calling Firebase function:', error);
      return originalCity;
    }
  } catch (error) {
    console.error('Error normalizing city name:', error);
    // If the function fails, return the original city name as fallback
    return originalCity;
  }
};
