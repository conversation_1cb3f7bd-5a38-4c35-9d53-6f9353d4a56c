/**
 * React Native specific Firebase Auth initialization for v9.22.2
 * This approach prevents the "Component auth has not been registered yet" error
 * by using the correct import path for React Native
 */
import { initializeApp } from 'firebase/app';
// The key fix: import from 'firebase/auth/react-native' instead of 'firebase/auth'
import { getAuth } from 'firebase/auth/react-native';

// Firebase configuration
const firebaseConfig = {
  apiKey: "AIzaSyDyB2FJwBKOLq4KzRLUn8VPY6s-LJNBeZI",
  authDomain: "traveling-app-c19a8.firebaseapp.com",
  projectId: "traveling-app-c19a8",
  storageBucket: "traveling-app-c19a8.appspot.com",
  messagingSenderId: "991094040299",
  appId: "1:991094040299:web:06cfaa17634d4f840d00ae",
  measurementId: "G-ZYQ1EDRSVF"
};

// Initialize Firebase
console.log('Initializing Firebase app with React Native specific auth...');
const app = initializeApp(firebaseConfig);
console.log('Firebase app initialized successfully');

// Initialize Firebase Auth using the React Native specific import
console.log('Initializing Firebase Auth for React Native...');
const auth = getAuth(app);
console.log('Firebase Auth initialized successfully');

export { auth };
export default auth;
