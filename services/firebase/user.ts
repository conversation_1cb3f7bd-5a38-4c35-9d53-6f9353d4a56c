import { doc, getDoc, updateDoc, increment, arrayUnion, arrayRemove, serverTimestamp } from 'firebase/firestore';
import { db } from './config';

/**
 * Fetch a user's profile from Firestore
 * @param userId User ID
 * @returns User profile data or null if not found
 */
export async function getUserProfile(userId: string) {
  try {
    console.log(`[getUserProfile] Fetching profile for user ${userId}`);
    const userDoc = await getDoc(doc(db, 'users', userId));
    
    if (userDoc.exists()) {
      const userData = userDoc.data();
      console.log('[getUserProfile] User data found:', {
        username: userData.username || 'not set',
        displayName: userData.displayName || 'not set',
        email: userData.email || 'not set'
      });
      return userData;
    }
    
    console.log('[getUserProfile] No user document found for this ID');
    return null;
  } catch (error) {
    console.error('Error getting user profile:', error);
    throw error;
  }
}

/**
 * Update user profile data
 * @param userId User ID
 * @param profileData Profile data to update
 */
export async function updateUserProfile(userId: string, profileData: any) {
  try {
    await updateDoc(doc(db, 'users', userId), {
      ...profileData,
      updatedAt: serverTimestamp(),
    });
    
    return true;
  } catch (error) {
    console.error('Error updating user profile:', error);
    throw error;
  }
}

/**
 * Update user preferences
 * @param userId User ID
 * @param preferences Preferences to update
 */
export async function updateUserPreferences(userId: string, preferences: any) {
  try {
    const preferencesUpdate = Object.entries(preferences).reduce((acc, [key, value]) => {
      acc[`preferences.${key}`] = value;
      return acc;
    }, {} as Record<string, any>);
    
    await updateDoc(doc(db, 'users', userId), {
      ...preferencesUpdate,
      updatedAt: serverTimestamp(),
    });
    
    return true;
  } catch (error) {
    console.error('Error updating user preferences:', error);
    throw error;
  }
}

/**
 * Add a visited country to the user's profile
 * @param userId User ID
 * @param countryCode ISO country code
 */
export async function addVisitedCountry(userId: string, countryCode: string) {
  try {
    await updateDoc(doc(db, 'users', userId), {
      visitedCountries: arrayUnion({
        countryCode,
        visitedAt: serverTimestamp(),
      }),
      'userStats.countriesCount': increment(1),
      updatedAt: serverTimestamp(),
    });
    
    return true;
  } catch (error) {
    console.error('Error adding visited country:', error);
    throw error;
  }
}

/**
 * Remove a visited country from the user's profile
 * @param userId User ID
 * @param countryCode ISO country code
 */
export async function removeVisitedCountry(userId: string, countryData: any) {
  try {
    await updateDoc(doc(db, 'users', userId), {
      visitedCountries: arrayRemove(countryData),
      'userStats.countriesCount': increment(-1),
      updatedAt: serverTimestamp(),
    });
    
    return true;
  } catch (error) {
    console.error('Error removing visited country:', error);
    throw error;
  }
}

/**
 * Update user statistics
 * @param userId User ID
 * @param field Statistic field to update (tripCount, placesCount)
 * @param amount Amount to increment by (default: 1)
 */
export async function updateUserStats(userId: string, field: 'tripCount' | 'placesCount', amount = 1) {
  try {
    await updateDoc(doc(db, 'users', userId), {
      [`userStats.${field}`]: increment(amount),
      updatedAt: serverTimestamp(),
    });
    
    return true;
  } catch (error) {
    console.error(`Error updating user ${field}:`, error);
    throw error;
  }
} 