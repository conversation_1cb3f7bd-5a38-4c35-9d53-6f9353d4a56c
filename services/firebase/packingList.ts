import { 
  doc, 
  getDoc, 
  setDoc, 
  updateDoc, 
  serverTimestamp,
  onSnapshot,
  Unsubscribe
} from 'firebase/firestore';
import { db } from './firebase-core';

// Type definition for packing list item
export interface PackingItem {
  id: string;
  text: string;
  packed: boolean;
  category?: string;
  createdAt?: string;
  updatedAt?: string;
}

// Type definition for packing list document
export interface PackingListDocument {
  userId: string;
  items: PackingItem[];
  createdAt: any;
  lastUpdated: any;
}

/**
 * Get user's packing list from Firestore
 */
export const getPackingList = async (userId: string): Promise<PackingItem[]> => {
  try {
    if (!userId) {
      console.error('getPackingList: Missing userId');
      return [];
    }

    const docRef = doc(db, 'packingLists', userId);
    const docSnap = await getDoc(docRef);

    if (!docSnap.exists()) {
      console.log('No packing list found for user:', userId);
      return [];
    }

    const data = docSnap.data() as PackingListDocument;
    return data.items || [];
  } catch (error) {
    console.error('Error getting packing list:', error);
    return [];
  }
};

/**
 * Save packing list to Firestore
 */
export const savePackingList = async (
  userId: string, 
  items: PackingItem[]
): Promise<boolean> => {
  try {
    if (!userId) {
      console.error('savePackingList: Missing userId');
      return false;
    }

    const docRef = doc(db, 'packingLists', userId);
    const docSnap = await getDoc(docRef);

    const packingListData: PackingListDocument = {
      userId,
      items,
      createdAt: docSnap.exists() ? docSnap.data().createdAt : serverTimestamp(),
      lastUpdated: serverTimestamp(),
    };

    if (docSnap.exists()) {
      // Update existing document
      await updateDoc(docRef, {
        items,
        lastUpdated: serverTimestamp(),
      });
    } else {
      // Create new document
      await setDoc(docRef, packingListData);
    }

    console.log(`Successfully saved packing list with ${items.length} items for user: ${userId}`);
    return true;
  } catch (error) {
    console.error('Error saving packing list:', error);
    return false;
  }
};

/**
 * Add item to packing list
 */
export const addPackingItem = async (
  userId: string,
  itemText: string,
  category?: string
): Promise<boolean> => {
  try {
    const currentItems = await getPackingList(userId);
    
    const newItem: PackingItem = {
      id: Date.now().toString(),
      text: itemText.trim(),
      packed: false,
      category,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
    };

    const updatedItems = [...currentItems, newItem];
    return await savePackingList(userId, updatedItems);
  } catch (error) {
    console.error('Error adding packing item:', error);
    return false;
  }
};

/**
 * Update packing item (toggle packed status or edit text)
 */
export const updatePackingItem = async (
  userId: string,
  itemId: string,
  updates: Partial<PackingItem>
): Promise<boolean> => {
  try {
    const currentItems = await getPackingList(userId);
    
    const updatedItems = currentItems.map(item => 
      item.id === itemId 
        ? { ...item, ...updates, updatedAt: new Date().toISOString() }
        : item
    );

    return await savePackingList(userId, updatedItems);
  } catch (error) {
    console.error('Error updating packing item:', error);
    return false;
  }
};

/**
 * Remove item from packing list
 */
export const removePackingItem = async (
  userId: string,
  itemId: string
): Promise<boolean> => {
  try {
    const currentItems = await getPackingList(userId);
    const updatedItems = currentItems.filter(item => item.id !== itemId);
    
    return await savePackingList(userId, updatedItems);
  } catch (error) {
    console.error('Error removing packing item:', error);
    return false;
  }
};

/**
 * Subscribe to real-time packing list updates
 */
export const subscribeToPackingList = (
  userId: string,
  callback: (items: PackingItem[]) => void
): Unsubscribe => {
  const docRef = doc(db, 'packingLists', userId);
  
  return onSnapshot(docRef, (docSnap) => {
    if (docSnap.exists()) {
      const data = docSnap.data() as PackingListDocument;
      callback(data.items || []);
    } else {
      callback([]);
    }
  }, (error) => {
    console.error('Error in packing list subscription:', error);
    callback([]);
  });
};
