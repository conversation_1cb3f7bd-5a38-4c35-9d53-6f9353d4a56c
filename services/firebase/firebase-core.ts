/**
 * Core Firebase initialization file
 * This is the single source of truth for Firebase initialization
 */
import { initializeApp } from 'firebase/app';
import { initializeAuth, getAuth } from 'firebase/auth';
// For Firebase v11.6.1, we need to use a different approach for persistence
import AsyncStorage from '@react-native-async-storage/async-storage';
import { getFirestore } from 'firebase/firestore';
import { getStorage } from 'firebase/storage';
import { getAnalytics, isSupported } from "firebase/analytics";
import { Platform } from 'react-native';

// Firebase configuration
const firebaseConfig = {
  apiKey: "AIzaSyDyB2FJwBKOLq4KzRLUn8VPY6s-LJNBeZI",
  authDomain: "traveling-app-c19a8.firebaseapp.com",
  projectId: "traveling-app-c19a8",
  storageBucket: "traveling-app-c19a8.appspot.com",
  messagingSenderId: "991094040299",
  appId: "1:991094040299:web:06cfaa17634d4f840d00ae",
  measurementId: "G-ZYQ1EDRSVF"
};

// Fix for the postinstall.getDefaultsFromPostinstall error
if (typeof global !== 'undefined') {
  // @ts-ignore
  global.__FIREBASE_DEFAULTS__ = {
    "config": {
      "projectId": firebaseConfig.projectId,
      "appId": firebaseConfig.appId,
      "apiKey": firebaseConfig.apiKey
    }
  };
  
  // Define a global _postinstall object with getDefaultsFromPostinstall function
  // @ts-ignore
  global._postinstall = {
    getDefaultsFromPostinstall: () => ({
      projectId: firebaseConfig.projectId,
      appId: firebaseConfig.appId,
      apiKey: firebaseConfig.apiKey
    })
  };
}

// Initialize Firebase
console.log('Initializing Firebase app...');
const app = initializeApp(firebaseConfig);
console.log('Firebase app initialized successfully');

// Initialize Firebase Auth with AsyncStorage persistence for React Native
let auth;

// For Firebase v11.6.1, we need to use a different approach for persistence
if (Platform.OS !== 'web') {
  console.log('Initializing Firebase Auth for React Native...');
  try {
    // First, try to dynamically import the React Native persistence module
    const reactNativeAuth = require('@firebase/auth/react-native');
    if (reactNativeAuth && reactNativeAuth.getReactNativePersistence) {
      console.log('Using @firebase/auth/react-native for persistence');
      auth = initializeAuth(app, {
        persistence: reactNativeAuth.getReactNativePersistence(AsyncStorage)
      });
    } else {
      // Fallback to standard auth if the module is not available
      console.log('React Native persistence not available, using standard auth');
      auth = getAuth(app);
    }
  } catch (error) {
    console.log('Error initializing Auth with persistence:', error);
    // Fallback to standard auth
    auth = getAuth(app);
  }
} else {
  // For web platform, use standard auth
  auth = getAuth(app);
  console.log('Firebase Auth initialized for web');
}

// Initialize Firestore
console.log('Initializing Firestore...');
const db = getFirestore(app);
console.log('Firestore initialized');

// Initialize Storage
console.log('Initializing Firebase Storage...');
const storage = getStorage(app);
console.log('Firebase Storage initialized');

// Initialize Analytics conditionally
let analytics = null;
if (Platform.OS === 'web') {
  isSupported().then(supported => {
    if (supported) {
      analytics = getAnalytics(app);
      console.log('Firebase Analytics initialized');
    }
  }).catch(err => {
    console.log('Analytics support check failed:', err);
  });
}

export { app, auth, db, storage, analytics };
export default app;
