/**
 * Firebase Services Index
 * This file exports all the Firebase services
 */

import { initializeApp } from 'firebase/app';
import { getFirestore, collection, doc, getDoc, setDoc, updateDoc, addDoc, query, where, getDocs, initializeFirestore as initFirestore, enableIndexedDbPersistence } from 'firebase/firestore';
import { getStorage, ref, uploadBytes, getDownloadURL, deleteObject } from 'firebase/storage';
import { getAuth, createUserWithEmailAndPassword, User } from 'firebase/auth';
import { auth } from './auth-native';

// Firebase configuration
const firebaseConfig = {
  apiKey: "AIzaSyDyB2FJwBKOLq4KzRLUn8VPY6s-LJNBeZI",
  authDomain: "traveling-app-c19a8.firebaseapp.com",
  projectId: "traveling-app-c19a8",
  storageBucket: "traveling-app-c19a8.appspot.com",
  messagingSenderId: "991094040299",
  appId: "1:991094040299:web:06cfaa17634d4f840d00ae",
  measurementId: "G-ZYQ1EDRSVF"
};

// Initialize Firebase app
const app = initializeApp(firebaseConfig);

// Initialize Firestore with persistence enabled from the start
let db;

try {
  console.log('Initializing Firestore');
  // Create Firestore instance with settings
  db = initFirestore(app, {
    experimentalForceLongPolling: true,
  });
  
  // We'll skip persistence for Expo Go since it causes issues
  // In a production app, you would enable this only in the native build
  console.log('Skipping Firestore persistence in Expo Go environment');
} catch (error) {
  console.error('Error initializing Firestore:', error);
  // Fallback to regular Firestore if initialization fails
  db = getFirestore(app);
}

// Export a function that just returns the already initialized db
export const initializeFirestore = () => {
  console.log('Returning already initialized Firestore instance');
  return db;
};
const storage = getStorage(app);

// Connect to Firebase emulators if in development
export const connectToEmulators = (forceEmulators = false) => {
  const isEmulator = process.env.EXPO_PUBLIC_USE_FIREBASE_EMULATOR === 'true' || forceEmulators;
  
  if (isEmulator) {
    console.log('Connecting to Firebase emulators');
    // Add emulator connection code here if needed
  }
  
  return { auth, firestore: db, storage };
};

// User profile functions
export const getUserProfile = async (userId: string) => {
  try {
    const userDocRef = doc(db, 'users', userId);
    const userDoc = await getDoc(userDocRef);
    
    if (userDoc.exists()) {
      return { uid: userId, ...userDoc.data() };
    } else {
      console.log(`No profile found for user: ${userId}`);
      return null;
    }
  } catch (error) {
    console.error('Error getting user profile:', error);
    throw error;
  }
};

// Initialize user profile
export const initializeUserProfile = async (user: User, username?: string | null) => {
  try {
    console.log(`[initializeUserProfile] Starting for user: ${user.uid}`);
    const userDocRef = doc(db, 'users', user.uid);
    const userDoc = await getDoc(userDocRef);
    
    if (!userDoc.exists()) {
      console.log(`[initializeUserProfile] Creating new user profile for: ${user.uid}`);
      const userData = {
        username: username || user.displayName || user.email?.split('@')[0] || 'User',
        email: user.email,
        premium: false,
        joinDate: new Date().toISOString(),
        tripsCount: 0,
        countriesVisited: 0,
        bio: '',
        profilePicture: user.photoURL || '',
        // CRITICAL: Explicitly set onboardingCompleted to false for new users
        // This ensures they must go through the language selection screen first
        onboardingCompleted: false,
        // IMPORTANT: Don't set any language preferences - force user to select in onboarding
        languageCode: null,
        preferredLanguage: null,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      };
      
      console.log('[initializeUserProfile] New user data:', {
        email: userData.email,
        onboardingCompleted: userData.onboardingCompleted,
        languageCode: userData.languageCode,
        preferredLanguage: userData.preferredLanguage
      });
      
      await setDoc(userDocRef, userData);
      console.log('User profile created successfully');
      return { uid: user.uid, ...userData };
    }
    
    return { uid: user.uid, ...userDoc.data() };
  } catch (error) {
    console.error('Error initializing user profile:', error);
    throw error;
  }
};

// Register a new user
export const registerUser = async (email: string, password: string, username?: string | null) => {
  try {
    const userCredential = await createUserWithEmailAndPassword(auth, email, password);
    console.log('User registered successfully');
    return { success: true, user: userCredential.user };
  } catch (error: any) {
    console.error('Error registering user:', error);
    return { success: false, error: error.message };
  }
};

export { app, auth, db, storage };