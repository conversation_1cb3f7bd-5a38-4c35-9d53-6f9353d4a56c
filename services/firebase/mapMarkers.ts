import { db } from './firebase-core';
import { 
  doc, 
  getDoc, 
  setDoc, 
  updateDoc, 
  arrayUnion, 
  collection, 
  getDocs,
  serverTimestamp,
  query,
  where
} from 'firebase/firestore';

// Type definition for place to save
export interface PlaceToSave {
  title: string;
  description?: string;
  location: {
    lat: number;
    lng: number;
  };
  type: string;
}

// Type definition for map markers
export interface MapMarker {
  id: string;
  title: string;
  description?: string;
  location: {
    lat: number;
    lng: number;
  };
  type: string;
  savedAt?: string;
  planId?: string;
}

/**
 * Check if a place is already saved to the map
 */
export const isPlaceSavedToMap = async (
  userId: string,
  planId: string,
  placeTitle: string
): Promise<boolean> => {
  try {
    if (!userId || !planId || !placeTitle) {
      return false;
    }

    const docId = `${userId}_${planId}`;
    const docRef = doc(db, 'mapMarkers', docId);
    const docSnap = await getDoc(docRef);

    if (!docSnap.exists()) {
      return false;
    }

    const data = docSnap.data();
    if (!data.places || !Array.isArray(data.places)) {
      return false;
    }

    // Check if a place with the same title exists
    return data.places.some(
      (place: any) => 
        place.title && 
        place.title.toLowerCase() === placeTitle.toLowerCase()
    );
  } catch (error) {
    console.error('Error checking if place is saved:', error);
    return false;
  }
};

/**
 * Save a place to the map
 */
export const savePlaceToMap = async (
  userId: string,
  planId: string,
  place: {
    title: string;
    description?: string;
    location: { lat: number; lng: number };
    type: string;
  }
): Promise<boolean> => {
  console.log('Saving place to map:', JSON.stringify(place, null, 2));
  try {
    if (!userId || !planId) {
      console.error('savePlaceToMap: Missing userId or planId');
      return false;
    }

    if (!place || !place.title) {
      console.error('savePlaceToMap: Invalid place data');
      return false;
    }

    console.log(`Saving place to map: ${place.title}`);
    
    // Validate location data
    if (!place.location.lat || !place.location.lng) {
      console.error('savePlaceToMap: Invalid location data', place.location);
      return false;
    }

    // Create a document ID using userId and planId
    const docId = `${userId}_${planId}`;
    const docRef = doc(db, 'mapMarkers', docId);

    // Check if the document exists
    const docSnap = await getDoc(docRef);

    // Create a unique ID using title and coordinates for consistency
    // Use toFixed(4) to ensure consistent decimal precision for coordinates
    const uniqueId = `${place.title.replace(/\s+/g, '_')}_${place.location.lat.toFixed(4)}_${place.location.lng.toFixed(4)}`;
    
    // Prepare the place object to save
    const placeToSave = {
      id: uniqueId, // Unique ID that can be used to find this place later
      title: place.title,
      description: place.description || '',
      location: {
        lat: place.location.lat,
        lng: place.location.lng
      },
      type: place.type || 'other',
      savedAt: new Date().toISOString(),
    };

    if (docSnap.exists()) {
      // Document exists, check if place already exists to avoid duplicates
      const data = docSnap.data();
      const places = data.places || [];

      // Check if a place with the same ID already exists
      const existingPlace = places.find((p: any) => p.id === uniqueId);
      if (existingPlace) {
        console.log(`Place "${place.title}" already exists in the map with ID: ${uniqueId}`);
        return true; // Return true since the place is already saved
      }

      // Update the document with the new place
      await updateDoc(docRef, {
        places: arrayUnion(placeToSave),
        lastUpdated: serverTimestamp(),
      });
    } else {
      // Document doesn't exist, create it
      await setDoc(docRef, {
        userId,
        planId,
        places: [placeToSave],
        createdAt: serverTimestamp(),
        lastUpdated: serverTimestamp(),
      });
    }
    
    console.log(`Successfully saved place "${place.title}" to map with ID: ${uniqueId}`);
    return true;
  } catch (error) {
    console.error('Error saving place to map:', error);
    return false;
  }
};

/**
 * Get map markers for a user and plan
 */
export const getMapMarkers = async (
  userId: string,
  planId: string
): Promise<MapMarker[]> => {
  try {
    if (!userId || !planId) {
      console.error('getMapMarkers: Missing userId or planId');
      return [];
    }

    const docId = `${userId}_${planId}`;
    const docRef = doc(db, 'mapMarkers', docId);
    const docSnap = await getDoc(docRef);

    if (!docSnap.exists()) {
      return [];
    }

    const data = docSnap.data();
    if (!data.places || !Array.isArray(data.places)) {
      return [];
    }

    return data.places.map((place: any) => ({
      id: place.id || `place-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
      title: place.title,
      description: place.description || '',
      location: place.location,
      type: place.type || 'other',
      savedAt: place.savedAt || new Date().toISOString()
    }));
  } catch (error) {
    console.error('Error getting map markers:', error);
    return [];
  }
};

/**
 * Remove a place from the map
 */
/**
 * Remove a place from the map by loading the document and filtering out the place by ID or title
 * This approach is more reliable than using arrayRemove which requires exact object matching
 */
export const removePlaceFromMap = async (
  userId: string,
  placeIdOrTitle: string,
  placeTitle?: string // Optional title parameter for fallback matching
): Promise<boolean> => {
  try {
    if (!userId || !placeIdOrTitle) {
      console.error('removePlaceFromMap: Missing userId or placeIdOrTitle');
      return false;
    }

    console.log(`Attempting to remove place with ID/Title: ${placeIdOrTitle} for user: ${userId}`);

    // Get all documents in the mapMarkers collection for this user using secure query
    const markersRef = collection(db, 'mapMarkers');
    const q = query(
      markersRef,
      where('__name__', '>=', `${userId}_`),
      where('__name__', '<', `${userId}_\uf8ff`)
    );
    const markersSnapshot = await getDocs(q);

    // All documents returned are already filtered for this user
    const userMarkerDocs = markersSnapshot.docs;
    
    if (userMarkerDocs.length === 0) {
      console.log('No map marker documents found for user:', userId);
      return false;
    }
    
    let placeRemoved = false;
    
    // Check each document to find and remove the place
    for (const docSnapshot of userMarkerDocs) {
      const data = docSnapshot.data();
      
      if (!data.places || !Array.isArray(data.places) || data.places.length === 0) {
        continue;
      }
      
      const places = data.places;
      let placeToRemoveIndex = -1;
      
      // First try to find by ID
      placeToRemoveIndex = places.findIndex((place: any) => place.id === placeIdOrTitle);
      
      // If not found by ID, try to find by title (if title is provided or placeIdOrTitle might be a title)
      if (placeToRemoveIndex === -1) {
        const searchTitle = placeTitle || placeIdOrTitle;
        placeToRemoveIndex = places.findIndex((place: any) => 
          place.title && place.title.toLowerCase() === searchTitle.toLowerCase()
        );
      }
      
      // If we found a place to remove
      if (placeToRemoveIndex !== -1) {
        console.log(`Found place to remove at index ${placeToRemoveIndex} in document ${docSnapshot.id}`);
        
        // Create a new array without the place to remove
        const filteredPlaces = [...places];
        filteredPlaces.splice(placeToRemoveIndex, 1);
        
        // Update the document with the filtered places array
        await updateDoc(docSnapshot.ref, { 
          places: filteredPlaces,
          lastUpdated: serverTimestamp()
        });
        
        console.log(`Place removed from document: ${docSnapshot.id}`);
        placeRemoved = true;
        break; // Exit the loop once the place is found and removed
      }
    }
    
    if (!placeRemoved) {
      console.error(`Place with ID/Title: ${placeIdOrTitle} not found in any document`);
      return false;
    }
    
    return true;
  } catch (error) {
    console.error('Error removing place from map:', error);
    return false;
  }
};
/**
 * Get all map markers for a user across all trips
 */
export const getAllMapMarkers = async (userId: string): Promise<MapMarker[]> => {
  try {
    if (!userId) {
      console.error('getAllMapMarkers: Missing userId');
      return [];
    }

    // Get all documents in the mapMarkers collection for this user using secure query
    const markersRef = collection(db, 'mapMarkers');
    const q = query(
      markersRef,
      where('__name__', '>=', `${userId}_`),
      where('__name__', '<', `${userId}_\uf8ff`)
    );
    const markersSnapshot = await getDocs(q);

    // All documents returned are already filtered for this user
    const userMarkerDocs = markersSnapshot.docs;
    
    if (userMarkerDocs.length === 0) {
      console.log('No map marker documents found for user:', userId);
      return [];
    }
    
    console.log(`Found ${userMarkerDocs.length} map marker documents for user: ${userId}`);
    
    // Collect all places from all documents
    const allPlaces: MapMarker[] = [];
    
    for (const markerDoc of userMarkerDocs) {
      const data = markerDoc.data();
      
      if (!data.places || !Array.isArray(data.places) || data.places.length === 0) {
        console.log(`Document ${markerDoc.id} has no places array or it is empty`);
        continue;
      }
      
      // Extract planId from document ID (format: userId_planId)
      const planId = markerDoc.id.split('_')[1];
      
      console.log(`Processing ${data.places.length} places from document ${markerDoc.id} with planId: ${planId}`);
      
      // Convert raw Firestore data to MapMarker objects
      const docPlaces = data.places.map((place: any) => {
        // Ensure each place has a unique ID
        const placeId = place.id || `place-${Date.now()}-${Math.floor(Math.random() * 10000)}`;
        
        return {
          id: placeId,
          title: place.title,
          description: place.description || '',
          location: place.location,
          type: place.type || 'other',
          savedAt: place.savedAt || new Date().toISOString(),
          planId: planId // Explicitly set the planId for each place
        };
      });
      
      allPlaces.push(...docPlaces);
    }
    
    console.log(`Successfully loaded ${allPlaces.length} places from all trips`);
    return allPlaces;
  } catch (error) {
    console.error('Error getting all map markers:', error);
    return [];
  }
};
