import { ref, uploadBytes, getDownloadURL, deleteObject, uploadBytesResumable, uploadString as firebaseUploadString } from 'firebase/storage';
import { storage } from './config';
import * as FileSystem from 'expo-file-system';
import { manipulateAsync, SaveFormat } from 'expo-image-manipulator';
import { Platform } from 'react-native';
import { getAuth } from 'firebase/auth';

// Constants for image processing
const MAX_IMAGE_WIDTH = 400;  // Maximum width
const MAX_IMAGE_HEIGHT = 400; // Maximum height
const COMPRESSION_QUALITY = 0.5; // Compression quality (lower = smaller file)

// Get Firebase configuration for direct API access
import app from './config';

/**
 * Compresses an image using expo-image-manipulator
 * Returns the URI of the compressed image
 */
async function compressImage(
  uri: string, 
  options: { maxWidth?: number; maxHeight?: number; quality?: number } = {}
): Promise<string> {
  console.log('Starting image compression...');
  
  const {
    maxWidth = MAX_IMAGE_WIDTH,
    maxHeight = MAX_IMAGE_HEIGHT,
    quality = COMPRESSION_QUALITY
  } = options;
  
  // Check if file exists
  const fileInfo = await FileSystem.getInfoAsync(uri);
  if (!fileInfo.exists) {
    throw new Error(`Image file not found: ${uri}`);
  }
  
  try {
    const result = await manipulateAsync(
      uri,
      [{ resize: { width: maxWidth, height: maxHeight } }],
      { 
        compress: quality, 
        format: SaveFormat.JPEG 
      }
    );
    
    console.log('Image compression successful');
    return result.uri;
  } catch (error) {
    console.error('Image compression failed:', error);
    throw new Error(`Failed to compress image: ${error.message || String(error)}`);
  }
}

/**
 * Converts an image URI to a Blob object using fetch
 * This is crucial for React Native compatibility
 */
async function uriToBlob(uri: string): Promise<Blob> {
  console.log('Converting image URI to Blob...');
  
  try {
    // Use XMLHttpRequest for better compatibility in some cases
    return new Promise((resolve, reject) => {
      const xhr = new XMLHttpRequest();
      xhr.onload = function() {
        resolve(xhr.response);
      };
      xhr.onerror = function(e) {
        console.error('XHR error:', e);
        reject(new Error('Network request failed'));
      };
      xhr.responseType = 'blob';
      xhr.open('GET', uri, true);
      xhr.send(null);
    });
  } catch (xhrError) {
    console.log('XHR approach failed, trying fetch API...');
    
    // Fallback to fetch API
    try {
      const response = await fetch(uri);
      if (!response.ok) {
        throw new Error(`HTTP error! Status: ${response.status}`);
      }
      const blob = await response.blob();
      console.log('Blob created successfully via fetch');
      return blob;
    } catch (fetchError) {
      console.error('Fetch blob creation failed:', fetchError);
      throw new Error(`Failed to create blob: ${fetchError.message || String(fetchError)}`);
    }
  }
}

/**
 * Upload a trip image
 */
export async function uploadTripImage(
  userId: string,
  tripId: string,
  uri: string,
  index?: number
): Promise<string> {
  try {
    const filename = index !== undefined ? `image_${index}.jpg` : `cover.jpg`;
    const path = `users/${userId}/trips/${tripId}/${filename}`;
    
    // Step 1: Compress the image
    const compressedUri = await compressImage(uri);
    
    // Step 2: Convert the image to a Blob
    const blob = await uriToBlob(compressedUri);
    
    // Step 3: Create a storage reference
    const storageRef = ref(storage, path);
    
    // Step 4: Upload the Blob to Firebase Storage
    await uploadBytes(storageRef, blob, {
      contentType: 'image/jpeg'
    });
    
    // Step 5: Get the download URL
    const downloadURL = await getDownloadURL(storageRef);
    
    return downloadURL;
  } catch (error: any) {
    console.error('Trip image upload failed:', error);
    throw new Error(`Failed to upload trip image: ${error.message || String(error)}`);
  }
}

/**
 * Upload a place image
 */
export async function uploadPlaceImage(
  userId: string,
  placeId: string,
  uri: string,
  index?: number
): Promise<string> {
  try {
    const filename = index !== undefined ? `image_${index}.jpg` : `cover.jpg`;
    const path = `users/${userId}/places/${placeId}/${filename}`;
    
    // Step 1: Compress the image
    const compressedUri = await compressImage(uri);
    
    // Step 2: Convert the image to a Blob
    const blob = await uriToBlob(compressedUri);
    
    // Step 3: Create a storage reference
    const storageRef = ref(storage, path);
    
    // Step 4: Upload the Blob to Firebase Storage
    await uploadBytes(storageRef, blob, {
      contentType: 'image/jpeg'
    });
    
    // Step 5: Get the download URL
    const downloadURL = await getDownloadURL(storageRef);
    
    return downloadURL;
  } catch (error: any) {
    console.error('Place image upload failed:', error);
    throw new Error(`Failed to upload place image: ${error.message || String(error)}`);
  }
}

/**
 * Delete an image from Firebase Storage
 */
export async function deleteImage(url: string): Promise<void> {
  try {
    // Check if the URL is a full download URL or a storage path
    let path = url;
    
    // If it's a download URL, we need to extract the path
    if (url.startsWith('https://')) {
      // Get the path from the URL
      const urlObj = new URL(url);
      const fullPath = urlObj.pathname;
      
      // Remove the /v0/b/PROJECT_ID.appspot.com/o/ prefix and decode the URL
      const pathStartIndex = fullPath.indexOf('/o/') + 3;
      path = decodeURIComponent(fullPath.substring(pathStartIndex));
    }
    
    const imageRef = ref(storage, path);
    await deleteObject(imageRef);
  } catch (error) {
    console.error('Error deleting image:', error);
    throw error;
  }
}

/**
 * Convert base64 string to ArrayBuffer
 */
function base64ToArrayBuffer(base64: string): ArrayBuffer {
  const binary_string = atob(base64);
  const len = binary_string.length;
  const bytes = new Uint8Array(len);
  
  for (let i = 0; i < len; i++) {
    bytes[i] = binary_string.charCodeAt(i);
  }
  
  return bytes.buffer;
} 