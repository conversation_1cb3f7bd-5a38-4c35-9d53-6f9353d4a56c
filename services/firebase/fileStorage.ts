import { ref, uploadBytes, getDownloadURL, deleteObject, listAll, list, getMetadata, uploadString } from 'firebase/storage';
import { storage } from './config';
import * as FileSystem from 'expo-file-system';
import { Platform } from 'react-native';
import { uriToBlob } from './storage'; // Reuse the blob conversion function

// File size constants
const MAX_FILE_SIZE_MB = 50; // Maximum file size in MB

/**
 * Uploads any type of file to Firebase Storage
 * 
 * @param uri Local URI of the file
 * @param path Storage path where file will be saved
 * @param contentType Optional content type of the file
 * @returns Download URL of the uploaded file
 */
export async function uploadFile(
  uri: string,
  path: string,
  contentType?: string
): Promise<string> {
  try {
    console.log(`Starting file upload for: ${path}`);
    
    // Get file info
    const fileInfo = await FileSystem.getInfoAsync(uri);
    if (!fileInfo.exists) {
      throw new Error('File does not exist');
    }

    // Check file size (if available)
    if (fileInfo.size) {
      const fileSizeMB = fileInfo.size / (1024 * 1024);
      if (fileSizeMB > MAX_FILE_SIZE_MB) {
        throw new Error(`File size exceeds maximum allowed size of ${MAX_FILE_SIZE_MB}MB`);
      }
    }

    // Create storage reference
    const storageRef = ref(storage, path);

    // Try multiple approaches to upload the file
    
    // APPROACH 1: Use uriToBlob and uploadBytes
    try {
      console.log('Attempting to upload using blob...');
      const blob = await uriToBlob(uri);
      
      // Upload file with metadata if contentType is provided
      const metadata = contentType ? { contentType } : undefined;
      const snapshot = await uploadBytes(storageRef, blob, metadata);
      console.log('File uploaded successfully with blob:', snapshot.metadata.fullPath);

      // Get download URL
      const downloadURL = await getDownloadURL(storageRef);
      return downloadURL;
    } catch (error) {
      console.log('Blob upload approach failed, trying alternative method:', error);
    }
    
    // APPROACH 2: Use base64 and uploadString
    try {
      console.log('Attempting to upload using base64...');
      
      // Read file as base64
      const base64 = await FileSystem.readAsStringAsync(uri, {
        encoding: FileSystem.EncodingType.Base64
      });
      
      // Determine content type if not provided
      const fileContentType = contentType || getContentTypeFromUri(uri);
      
      // Create data URL
      const dataUrl = `data:${fileContentType};base64,${base64}`;
      
      // Upload string
      const snapshot = await uploadString(storageRef, dataUrl, 'data_url');
      console.log('File uploaded successfully with base64:', snapshot.metadata.fullPath);
      
      // Get download URL
      const downloadURL = await getDownloadURL(storageRef);
      return downloadURL;
    } catch (error) {
      console.error('All upload approaches failed:', error);
      throw new Error(`Failed to upload file: ${error.message}`);
    }
  } catch (error: any) {
    console.error('Error uploading file:', error);
    throw new Error(`Failed to upload file: ${error.message}`);
  }
}

/**
 * Helper function to guess content type from URI
 */
function getContentTypeFromUri(uri: string): string {
  const extension = uri.split('.').pop()?.toLowerCase();
  
  switch(extension) {
    case 'jpg':
    case 'jpeg':
      return 'image/jpeg';
    case 'png':
      return 'image/png';
    case 'gif':
      return 'image/gif';
    case 'pdf':
      return 'application/pdf';
    case 'doc':
      return 'application/msword';
    case 'docx':
      return 'application/vnd.openxmlformats-officedocument.wordprocessingml.document';
    case 'xls':
      return 'application/vnd.ms-excel';
    case 'xlsx':
      return 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet';
    case 'txt':
      return 'text/plain';
    default:
      return 'application/octet-stream';
  }
}

/**
 * Upload a document for a user
 * 
 * @param userId User ID
 * @param uri Local URI of the document
 * @param fileName Name to save the file as
 * @param contentType Optional content type
 * @returns Download URL of the uploaded document
 */
export async function uploadDocument(
  userId: string,
  uri: string,
  fileName: string,
  contentType?: string
): Promise<string> {
  const path = `users/${userId}/documents/${fileName}`;
  return uploadFile(uri, path, contentType);
}

/**
 * Upload a file related to a trip
 * 
 * @param userId User ID
 * @param tripId Trip ID
 * @param uri Local URI of the file
 * @param fileName Name to save the file as
 * @param contentType Optional content type
 * @returns Download URL of the uploaded file
 */
export async function uploadTripFile(
  userId: string,
  tripId: string,
  uri: string,
  fileName: string,
  contentType?: string
): Promise<string> {
  const path = `users/${userId}/trips/${tripId}/files/${fileName}`;
  return uploadFile(uri, path, contentType);
}

/**
 * List all files in a directory
 * 
 * @param path Firebase Storage path to list files from
 * @returns Array of file references
 */
export async function listFiles(path: string) {
  try {
    const directoryRef = ref(storage, path);
    const fileList = await listAll(directoryRef);
    
    // Get details for each file
    const files = await Promise.all(
      fileList.items.map(async (itemRef) => {
        try {
          const url = await getDownloadURL(itemRef);
          const metadata = await getMetadata(itemRef);
          
          return {
            name: itemRef.name,
            fullPath: itemRef.fullPath,
            url,
            contentType: metadata.contentType,
            size: metadata.size,
            timeCreated: metadata.timeCreated,
            updated: metadata.updated,
          };
        } catch (error) {
          console.error(`Error getting details for ${itemRef.fullPath}:`, error);
          return {
            name: itemRef.name,
            fullPath: itemRef.fullPath,
            error: 'Failed to get details',
          };
        }
      })
    );
    
    return files;
  } catch (error: any) {
    console.error('Error listing files:', error);
    throw new Error(`Failed to list files: ${error.message}`);
  }
}

/**
 * Get user's files (paginated)
 * 
 * @param userId User ID
 * @param maxResults Maximum number of results to return
 * @param pageToken Pagination token for getting next page
 * @returns List of files and next page token if available
 */
export async function getUserFiles(
  userId: string,
  maxResults: number = 100,
  pageToken?: string
) {
  try {
    const userFilesRef = ref(storage, `users/${userId}`);
    const result = await list(userFilesRef, { maxResults, pageToken });

    // Get details for each file
    const files = await Promise.all(
      result.items.map(async (itemRef) => {
        try {
          const url = await getDownloadURL(itemRef);
          const metadata = await getMetadata(itemRef);
          
          return {
            name: itemRef.name,
            fullPath: itemRef.fullPath,
            url,
            contentType: metadata.contentType,
            size: metadata.size,
            timeCreated: metadata.timeCreated,
          };
        } catch (error) {
          return {
            name: itemRef.name,
            fullPath: itemRef.fullPath,
            error: 'Failed to get details',
          };
        }
      })
    );

    return {
      files,
      nextPageToken: result.nextPageToken,
    };
  } catch (error: any) {
    console.error('Error getting user files:', error);
    throw new Error(`Failed to get user files: ${error.message}`);
  }
} 