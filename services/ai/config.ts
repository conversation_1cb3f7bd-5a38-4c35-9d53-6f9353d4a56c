import Constants from 'expo-constants';

// OpenAI API configuration
export const OPENAI_API_KEY = 
  Constants.expoConfig?.extra?.OPENAI_API_KEY || 
  process.env.EXPO_PUBLIC_OPENAI_API_KEY || 
  'YOUR_OPENAI_API_KEY';

// AI Models
export enum AIModel {
  GPT3 = 'gpt-3.5-turbo',
  GPT4 = 'gpt-4-turbo',
}

// Default model to use
export const DEFAULT_MODEL = 
  process.env.EXPO_PUBLIC_DEFAULT_AI_MODEL === 'gpt-4-turbo' 
    ? AIModel.GPT4 
    : AIModel.GPT3;

// Rate limiting
export const RATE_LIMIT = {
  // Max number of requests per user per day
  MAX_REQUESTS_PER_DAY: 25,
  // Max number of trip plan generations per user per day
  MAX_TRIP_PLANS_PER_DAY: 3,
};

// Feature flags
export const AI_FEATURES = {
  // Enable trip planning
  TRIP_PLANNING: true,
  // Enable quick suggestions
  QUICK_SUGGESTIONS: true,
  // Enable cost tracking
  COST_TRACKING: true,
};

// Model token limits
export const TOKEN_LIMITS = {
  [AIModel.GPT3]: 4096,
  [AIModel.GPT4]: 8192,
};

// Cache settings
export const CACHE_CONFIG = {
  // Enable caching of AI responses
  ENABLED: true,
  // Cache expiration in milliseconds (default: 1 hour)
  EXPIRATION: 3600000,
};

// System prompts
export const SYSTEM_PROMPTS = {
  TRIP_PLANNER: 'You are an expert travel planner with experience in destinations worldwide. You create detailed, personalized travel itineraries that include activities, restaurants, and practical tips. Format your response in clear sections by day, with each day including morning, afternoon, and evening activities.',
  QUICK_ASSISTANT: 'You are a helpful travel assistant. Provide quick, specific, and practical travel suggestions. Keep your response under 100 words.',
}; 