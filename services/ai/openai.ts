import OpenAI from 'openai';
import { addDoc, collection, serverTimestamp, query, where, getDocs, Timestamp } from 'firebase/firestore';
import { db } from '../firebase/config';
import { 
  OPENAI_API_KEY, 
  AIModel, 
  DEFAULT_MODEL, 
  SYSTEM_PROMPTS,
  CACHE_CONFIG,
  RATE_LIMIT,
  AI_FEATURES
} from './config';

// Re-export AIModel for use in other files
export { AIModel } from './config';

// Initialize OpenAI (for client-side usage if needed)
// Note: For Firebase Cloud Functions, the API key is configured server-side
const openai = new OpenAI({
  apiKey: OPENAI_API_KEY,
});

/**
 * Generate a safe cache key from a string
 * @param input The input string to generate a cache key from
 * @returns A safe cache key string
 */
function generateCacheKey(input: string): string {
  try {
    // Use a safer method to create cache key to avoid base64 encoding issues
    return input
      .replace(/[^a-zA-Z0-9]/g, '') // Remove special characters
      .substring(0, 20) // Take first 20 characters
      .toLowerCase();
  } catch (error) {
    // Fallback to a simple hash if the above fails
    console.warn('Error generating cache key, using fallback:', error);
    return Math.abs(input.split('').reduce((a, b) => {
      a = ((a << 5) - a) + b.charCodeAt(0);
      return a & a;
    }, 0)).toString().substring(0, 20);
  }
}

/**
 * Check if the user has reached their rate limit
 * @param userId User ID to check
 * @param type Type of request ('any', 'trip_plan')
 * @returns Whether the user has reached their rate limit
 */
async function checkRateLimit(userId: string, type: 'any' | 'trip_plan' = 'any'): Promise<boolean> {
  try {
    // Check if db is properly initialized
    if (!db) {
      console.error('Firestore database not initialized');
      return false; // Allow request if we can't check rate limit
    }

    // Get the start of today
    const today = new Date();
    today.setHours(0, 0, 0, 0);
    const startOfToday = Timestamp.fromDate(today);

    // Build query to get today's requests
    let aiLogsQuery = query(
      collection(db, 'ai_logs'),
      where('userId', '==', userId),
      where('timestamp', '>=', startOfToday)
    );
    
    // Get all requests made today
    const querySnapshot = await getDocs(aiLogsQuery);
    const requestsToday = querySnapshot.size;
    
    // Check against appropriate limit
    if (type === 'trip_plan') {
      // For trip plans, count only trip plan requests
      const tripPlans = querySnapshot.docs.filter(doc => 
        doc.data().requestType === 'trip_plan'
      ).length;
      
      return tripPlans >= RATE_LIMIT.MAX_TRIP_PLANS_PER_DAY;
    }
    
    // For any request type, check against total limit
    return requestsToday >= RATE_LIMIT.MAX_REQUESTS_PER_DAY;
  } catch (error) {
    console.error('Error checking rate limit:', error);
    // If there's an error, allow the request to proceed
    return false;
  }
}

/**
 * Check cache for a previous response to the same query
 * @param userId User ID
 * @param prompt The prompt to check
 * @returns Cached response or null
 */
async function checkCache(userId: string, prompt: string): Promise<any> {
  if (!CACHE_CONFIG.ENABLED) return null;

  try {
    // Check if db is properly initialized
    if (!db) {
      console.error('Firestore database not initialized');
      return null;
    }

    // Create a simple hash of the prompt for cache key
    const cacheKey = generateCacheKey(prompt);

    // Query for cached responses
    const cacheQuery = query(
      collection(db, 'ai_cache'),
      where('userId', '==', userId),
      where('cacheKey', '==', cacheKey)
    );
    
    const cacheSnapshot = await getDocs(cacheQuery);
    
    if (cacheSnapshot.empty) return null;
    
    const cacheDoc = cacheSnapshot.docs[0].data();
    const cacheTime = cacheDoc.timestamp.toMillis();
    const now = Date.now();
    
    // Check if cache has expired
    if (now - cacheTime > CACHE_CONFIG.EXPIRATION) {
      return null;
    }
    
    return {
      data: cacheDoc.response,
      fromCache: true,
      cacheKey,
    };
  } catch (error) {
    console.error('Error checking cache:', error);
    return null;
  }
}

/**
 * Generate a travel itinerary using AI
 */
export async function generateTripPlan(
  userId: string,
  destination: string,
  startDate: string,
  endDate: string,
  preferences: string,
  budget: string,
  model: AIModel = DEFAULT_MODEL
) {
  // Check if trip planning is enabled
  if (!AI_FEATURES.TRIP_PLANNING) {
    return {
      success: false,
      error: 'Trip planning feature is currently disabled.'
    };
  }

  // Check rate limits
  const isRateLimited = await checkRateLimit(userId, 'trip_plan');
  if (isRateLimited) {
    return {
      success: false,
      error: 'You have reached your daily limit for trip plans. Please try again tomorrow.'
    };
  }
  
  try {
    const prompt = createTripPlanPrompt(
      destination,
      startDate,
      endDate,
      preferences,
      budget
    );

    // Check cache first
    const cachedResponse = await checkCache(userId, prompt);
    if (cachedResponse) {
      // Log the cache hit
      await logAIUsage(
        userId,
        model,
        prompt,
        { prompt: 0, completion: 0, total: 0 },
        0,
        'success',
        'trip_plan',
        '',
        true,
        cachedResponse.cacheKey
      );
      
      return { success: true, data: cachedResponse.data, fromCache: true };
    }
    
    const startTime = Date.now();
    
    const completion = await openai.chat.completions.create({
      model: model,
      messages: [
        {
          role: 'system',
          content: SYSTEM_PROMPTS.TRIP_PLANNER,
        },
        {
          role: 'user',
          content: prompt,
        },
      ],
      temperature: 0.7,
      max_tokens: 2000,
    });

    const responseTime = Date.now() - startTime;
    const response = completion.choices[0].message.content || '';
    const tokensUsed = {
      prompt: completion.usage?.prompt_tokens || 0,
      completion: completion.usage?.completion_tokens || 0,
      total: completion.usage?.total_tokens || 0,
    };

    // Generate a cache key
    const cacheKey = generateCacheKey(prompt);
    
    // Cache the response
    if (CACHE_CONFIG.ENABLED) {
      try {
        await addDoc(collection(db, 'ai_cache'), {
          userId,
          cacheKey,
          prompt,
          response,
          model,
          timestamp: serverTimestamp(),
        });
      } catch (cacheError) {
        console.error('Error caching AI response:', cacheError);
      }
    }

    // Log AI usage for tracking
    await logAIUsage(
      userId,
      model,
      prompt,
      tokensUsed,
      responseTime,
      'success',
      'trip_plan'
    );

    return { success: true, data: response };
  } catch (error: any) {
    await logAIUsage(
      userId,
      model,
      'Error generating trip plan',
      { prompt: 0, completion: 0, total: 0 },
      0,
      'error',
      'trip_plan',
      error.message
    );
    return { success: false, error: error.message };
  }
}

/**
 * Generate a quick travel suggestion
 */
export async function generateQuickSuggestion(
  userId: string,
  query: string,
  model: AIModel = DEFAULT_MODEL
) {
  // Check if quick suggestions are enabled
  if (!AI_FEATURES.QUICK_SUGGESTIONS) {
    return {
      success: false,
      error: 'Quick suggestions feature is currently disabled.'
    };
  }

  // Check rate limits
  const isRateLimited = await checkRateLimit(userId);
  if (isRateLimited) {
    return {
      success: false,
      error: 'You have reached your daily limit for AI requests. Please try again tomorrow.'
    };
  }
  
  try {
    // Check cache first
    const cachedResponse = await checkCache(userId, query);
    if (cachedResponse) {
      // Log the cache hit
      await logAIUsage(
        userId,
        model,
        query,
        { prompt: 0, completion: 0, total: 0 },
        0,
        'success',
        'quick_suggestion',
        '',
        true,
        cachedResponse.cacheKey
      );
      
      return { success: true, data: cachedResponse.data, fromCache: true };
    }
    
    const startTime = Date.now();
    
    const completion = await openai.chat.completions.create({
      model: model,
      messages: [
        {
          role: 'system',
          content: SYSTEM_PROMPTS.QUICK_ASSISTANT,
        },
        {
          role: 'user',
          content: query,
        },
      ],
      temperature: 0.7,
      max_tokens: 150,
    });

    const responseTime = Date.now() - startTime;
    const response = completion.choices[0].message.content || '';
    const tokensUsed = {
      prompt: completion.usage?.prompt_tokens || 0,
      completion: completion.usage?.completion_tokens || 0,
      total: completion.usage?.total_tokens || 0,
    };
    
    // Generate a cache key
    const cacheKey = generateCacheKey(query);
    
    // Cache the response
    if (CACHE_CONFIG.ENABLED) {
      try {
        await addDoc(collection(db, 'ai_cache'), {
          userId,
          cacheKey,
          prompt: query,
          response,
          model,
          timestamp: serverTimestamp(),
        });
      } catch (cacheError) {
        console.error('Error caching AI response:', cacheError);
      }
    }

    // Log AI usage for tracking
    await logAIUsage(
      userId,
      model,
      query,
      tokensUsed,
      responseTime,
      'success',
      'quick_suggestion'
    );

    return { success: true, data: response };
  } catch (error: any) {
    await logAIUsage(
      userId,
      model,
      query,
      { prompt: 0, completion: 0, total: 0 },
      0,
      'error',
      'quick_suggestion',
      error.message
    );
    return { success: false, error: error.message };
  }
}

/**
 * Generate a travel itinerary for specific attractions using AI
 */
export async function generateSpecificAttractionsPlan(
  userId: string,
  destination: string,
  startDate: string,
  endDate: string,
  cityAttractions: string[],
  mustSee: string[],
  customRequests: string[],
  budget: string,
  travelGroup: string,
  activityLevel: string,
  interests: string[],
  foodPreferences: string[],
  model: AIModel = DEFAULT_MODEL
) {
  // Check if trip planning is enabled
  if (!AI_FEATURES.TRIP_PLANNING) {
    return {
      success: false,
      error: 'Trip planning feature is currently disabled.'
    };
  }

  // Check rate limits
  const isRateLimited = await checkRateLimit(userId, 'trip_plan');
  if (isRateLimited) {
    return {
      success: false,
      error: 'You have reached your daily limit for trip plans. Please try again tomorrow.'
    };
  }

  try {
    const prompt = createSpecificAttractionsPlanPrompt(
      destination,
      startDate,
      endDate,
      cityAttractions,
      mustSee,
      customRequests,
      budget,
      travelGroup,
      activityLevel,
      interests,
      foodPreferences
    );

    // Check cache first
    const cachedResponse = await checkCache(userId, prompt);
    if (cachedResponse) {
      // Log the cache hit
      await logAIUsage(
        userId,
        model,
        prompt,
        { prompt: 0, completion: 0, total: 0 },
        0,
        'success',
        'trip_plan',
        '',
        true,
        cachedResponse.cacheKey
      );

      return { success: true, data: cachedResponse.data, fromCache: true };
    }

    const startTime = Date.now();

    const completion = await openai.chat.completions.create({
      model: model,
      messages: [
        {
          role: 'system',
          content: SYSTEM_PROMPTS.TRIP_PLANNER,
        },
        {
          role: 'user',
          content: prompt,
        },
      ],
      temperature: 0.7,
      max_tokens: 2000,
    });

    const responseTime = Date.now() - startTime;
    const response = completion.choices[0].message.content || '';
    const tokensUsed = {
      prompt: completion.usage?.prompt_tokens || 0,
      completion: completion.usage?.completion_tokens || 0,
      total: completion.usage?.total_tokens || 0,
    };

    // Generate a cache key
    const cacheKey = generateCacheKey(prompt);

    // Cache the response
    if (CACHE_CONFIG.ENABLED) {
      try {
        await addDoc(collection(db, 'ai_cache'), {
          userId,
          cacheKey,
          prompt,
          response,
          model,
          timestamp: serverTimestamp(),
        });
      } catch (cacheError) {
        console.error('Error caching AI response:', cacheError);
      }
    }

    // Log AI usage for tracking
    await logAIUsage(
      userId,
      model,
      prompt,
      tokensUsed,
      responseTime,
      'success',
      'trip_plan'
    );

    return { success: true, data: response };
  } catch (error: any) {
    await logAIUsage(
      userId,
      model,
      '',
      { prompt: 0, completion: 0, total: 0 },
      0,
      'error',
      'trip_plan',
      error.message
    );
    return { success: false, error: error.message };
  }
}

/**
 * Create a prompt for trip planning
 */
function createTripPlanPrompt(
  destination: string,
  startDate: string,
  endDate: string,
  preferences: string,
  budget: string
): string {
  return `Please create a detailed travel itinerary for a trip to ${destination} from ${startDate} to ${endDate}.

PREFERENCES:
${preferences}

BUDGET:
${budget}

Please provide a day-by-day itinerary with the following for each day:
1. Morning activity recommendations
2. Lunch recommendation
3. Afternoon activity recommendations
4. Dinner recommendation
5. Evening activity (if applicable)
6. Transportation tips between locations
7. Estimated costs for activities and meals

Format the response clearly with day headers and organize activities chronologically.`;
}

/**
 * Create a prompt for specific attractions trip planning
 */
function createSpecificAttractionsPlanPrompt(
  destination: string,
  startDate: string,
  endDate: string,
  cityAttractions: string[],
  mustSee: string[],
  customRequests: string[],
  budget: string,
  travelGroup: string,
  activityLevel: string,
  interests: string[],
  foodPreferences: string[]
): string {
  return `Please create a detailed travel itinerary for a trip to ${destination} from ${startDate} to ${endDate}.

**IMPORTANT: Start your response with the following header format:**
Trip Plan: ${destination}
Dates: ${startDate} to ${endDate}

SPECIFIC ATTRACTIONS TO INCLUDE:
${cityAttractions.length > 0 ? cityAttractions.join(', ') : 'None specified'}

MUST-SEE PLACES:
${mustSee.length > 0 ? mustSee.join(', ') : 'None specified'}

CUSTOM REQUESTS:
${customRequests.length > 0 ? customRequests.join(', ') : 'None specified'}

BUDGET:
${budget || 'Not specified'}

TRAVEL GROUP:
${travelGroup || 'Not specified'}

ACTIVITY LEVEL:
${activityLevel || 'Not specified'}

INTERESTS:
${interests.length > 0 ? interests.join(', ') : 'Not specified'}

FOOD PREFERENCES:
${foodPreferences.length > 0 ? foodPreferences.join(', ') : 'Not specified'}

Please create a day-by-day itinerary that incorporates the specified attractions and must-see places. For each day include:
1. Morning activity recommendations
2. Lunch recommendation
3. Afternoon activity recommendations
4. Dinner recommendation
5. Evening activity (if applicable)
6. Transportation tips between locations
7. Estimated costs for activities and meals

Make sure to include the specified attractions and must-see places in appropriate days and consider the custom requests, travel group, activity level, interests, and food preferences when making recommendations.

Format the response clearly with day headers and organize activities chronologically.`;
}

/**
 * Log AI usage for tracking and billing
 */
async function logAIUsage(
  userId: string,
  model: AIModel,
  prompt: string,
  tokensUsed: { prompt: number; completion: number; total: number },
  responseTime: number,
  status: 'success' | 'error',
  requestType: 'trip_plan' | 'quick_suggestion' = 'quick_suggestion',
  errorMessage: string = '',
  wasCached: boolean = false,
  cacheKey: string = ''
) {
  if (!AI_FEATURES.COST_TRACKING) return;

  try {
    // Check if db is properly initialized
    if (!db) {
      console.error('Firestore database not initialized');
      return;
    }

    // Calculate approximate cost based on model and tokens
    const cost = calculateCost(model, tokensUsed.total);

    await addDoc(collection(db, 'ai_logs'), {
      userId,
      timestamp: serverTimestamp(),
      model,
      prompt,
      tokensUsed,
      responseTime,
      status,
      requestType,
      errorMessage,
      cost,
      cache: {
        wasCached,
        cacheKey,
      },
    });
  } catch (error) {
    console.error('Failed to log AI usage:', error);
  }
}

/**
 * Calculate the cost of an AI request based on tokens used
 */
function calculateCost(model: AIModel, totalTokens: number): number {
  // Approximate costs in USD per 1000 tokens (as of my knowledge cutoff)
  const rates: Record<AIModel, { input: number; output: number }> = {
    [AIModel.GPT3]: { input: 0.0015, output: 0.002 },
    [AIModel.GPT4]: { input: 0.03, output: 0.06 },
  };

  // For simplicity, we're using an average rate and the total tokens
  const avgRate = (rates[model].input + rates[model].output) / 2;
  return (avgRate * totalTokens) / 1000;
} 