import React from 'react';
import { View, StyleSheet, Platform } from 'react-native';
import { Marker } from 'react-native-maps';
import { MaterialIcons } from '@expo/vector-icons';
import { SavedPlace, PlaceCategory } from '../../types/SavedPlace';

interface SavedPlaceMarkerProps {
  place: SavedPlace;
  onPress: (place: SavedPlace) => void;
}

/**
 * Get the appropriate color for a place category
 */
const getCategoryColor = (category: PlaceCategory): string => {
  switch (category) {
    case 'restaurant':
      return '#FF7D7D'; // Red
    case 'hotel':
      return '#8B71F8'; // Purple
    case 'attraction':
      return '#4285F4'; // Blue
    case 'event':
      return '#FFCA28'; // Yellow
    case 'other':
    default:
      return '#34A853'; // Green
  }
};

/**
 * Get the appropriate icon name for a place category
 */
const getCategoryIcon = (category: PlaceCategory) => {
  switch (category) {
    case 'restaurant':
      return 'restaurant' as const;
    case 'hotel':
      return 'hotel' as const;
    case 'attraction':
      return 'place' as const;
    case 'event':
      return 'event' as const;
    case 'other':
    default:
      return 'star' as const;
  }
};

/**
 * A marker component for saved places on the map
 */
const SavedPlaceMarker: React.FC<SavedPlaceMarkerProps> = ({ place, onPress }) => {
  const { id, latitude, longitude, category } = place;
  const color = getCategoryColor(category);
  const iconName = getCategoryIcon(category);

  // Validate coordinates to prevent map crashes
  if (isNaN(latitude) || isNaN(longitude)) {
    console.warn(`Invalid coordinates for marker ${id}: ${latitude}, ${longitude}`);
    return null;
  }

  return (
    <Marker
      key={id}
      identifier={id}
      coordinate={{ latitude, longitude }}
      tracksViewChanges={false} // Performance optimization
      onPress={() => onPress(place)}
    >
      <View style={styles.markerContainer}>
        <View style={[styles.markerBubble, { backgroundColor: color }]}>
          <MaterialIcons name={iconName} size={18} color="white" />
        </View>
        <View style={[styles.markerArrow, { borderTopColor: color }]} />
      </View>
    </Marker>
  );
};

const styles = StyleSheet.create({
  markerContainer: {
    alignItems: 'center',
  },
  markerBubble: {
    width: 36,
    height: 36,
    borderRadius: 18,
    justifyContent: 'center',
    alignItems: 'center',
    ...Platform.select({
      ios: {
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.3,
        shadowRadius: 4,
      },
      android: {
        elevation: 4,
      },
    }),
    borderWidth: 2,
    borderColor: 'white',
  },
  markerArrow: {
    width: 0,
    height: 0,
    borderLeftWidth: 6,
    borderRightWidth: 6,
    borderTopWidth: 10,
    borderLeftColor: 'transparent',
    borderRightColor: 'transparent',
    marginTop: -2,
  },
});

export default SavedPlaceMarker;
