import React from 'react';
import { View, Text, StyleSheet, TouchableOpacity, Platform } from 'react-native';
import { MaterialIcons } from '@expo/vector-icons';
import { SavedPlace, PlaceCategory } from '../../types/SavedPlace';

interface PlaceDetailCardProps {
  place: SavedPlace;
  onClose: () => void;
  onUnsave?: (placeId: string) => void;
}

/**
 * Get the appropriate color for a place category
 */
const getCategoryColor = (category: PlaceCategory): string => {
  switch (category) {
    case 'restaurant':
      return '#FF7D7D'; // Red
    case 'hotel':
      return '#8B71F8'; // Purple
    case 'attraction':
      return '#4285F4'; // Blue
    case 'event':
      return '#FFCA28'; // Yellow
    case 'other':
    default:
      return '#34A853'; // Green
  }
};

/**
 * Get the appropriate icon name for a place category
 */
const getCategoryIcon = (category: PlaceCategory): string => {
  switch (category) {
    case 'restaurant':
      return 'restaurant';
    case 'hotel':
      return 'hotel';
    case 'attraction':
      return 'place';
    case 'event':
      return 'event';
    case 'other':
    default:
      return 'star';
  }
};

/**
 * Get a human-readable label for a place category
 */
const getCategoryLabel = (category: PlaceCategory): string => {
  switch (category) {
    case 'restaurant':
      return 'Restaurant';
    case 'hotel':
      return 'Hotel';
    case 'attraction':
      return 'Attraction';
    case 'event':
      return 'Event';
    case 'other':
    default:
      return 'Place';
  }
};

/**
 * Format date string to a readable format
 */
const formatDate = (dateString?: string): string => {
  if (!dateString) return '';
  
  try {
    const date = new Date(dateString);
    return date.toLocaleDateString(undefined, {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
    });
  } catch (e) {
    return '';
  }
};

/**
 * A card component that displays details about a saved place
 */
const PlaceDetailCard: React.FC<PlaceDetailCardProps> = ({ place, onClose, onUnsave }) => {
  const { id, name, description, category, savedAt } = place;
  const color = getCategoryColor(category);
  const iconName = getCategoryIcon(category) as any; // Type assertion to fix TypeScript error
  const categoryLabel = getCategoryLabel(category);
  const formattedDate = formatDate(savedAt);

  // Handle unsave button press
  const handleUnsave = () => {
    if (onUnsave) {
      onUnsave(id);
      onClose(); // Close the detail card after unsaving
    }
  };

  return (
    <View style={styles.container}>
      <View style={styles.header}>
        <View style={[styles.iconContainer, { backgroundColor: color }]}>
          <MaterialIcons name={iconName} size={24} color="white" />
        </View>
        <View style={styles.titleContainer}>
          <Text style={styles.title} numberOfLines={2}>{name}</Text>
          <Text style={[styles.category, { color }]}>{categoryLabel}</Text>
        </View>
        <TouchableOpacity style={styles.closeButton} onPress={onClose}>
          <MaterialIcons name="close" size={24} color="#666" />
        </TouchableOpacity>
      </View>

      {description && (
        <View style={styles.section}>
          <Text style={styles.description}>{description}</Text>
        </View>
      )}

      {savedAt && (
        <View style={styles.footer}>
          <Text style={styles.savedDate}>Saved on {formattedDate}</Text>
        </View>
      )}

      <View style={styles.actions}>
        <TouchableOpacity style={styles.actionButton}>
          <MaterialIcons name="directions" size={20} color="#4285F4" />
          <Text style={styles.actionText}>Directions</Text>
        </TouchableOpacity>
        
        <TouchableOpacity style={styles.actionButton}>
          <MaterialIcons name="share" size={20} color="#4285F4" />
          <Text style={styles.actionText}>Share</Text>
        </TouchableOpacity>
        
        {onUnsave && (
          <TouchableOpacity 
            style={[styles.actionButton, styles.unsaveButton]} 
            onPress={handleUnsave}
          >
            <MaterialIcons name="delete" size={20} color="#FF5252" />
            <Text style={[styles.actionText, styles.unsaveText]}>Unsave</Text>
          </TouchableOpacity>
        )}
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    backgroundColor: 'white',
    borderRadius: 12,
    margin: 16,
    ...Platform.select({
      ios: {
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.1,
        shadowRadius: 4,
      },
      android: {
        elevation: 4,
      },
    }),
    overflow: 'hidden',
  },
  header: {
    flexDirection: 'row',
    padding: 16,
    alignItems: 'center',
    borderBottomWidth: 1,
    borderBottomColor: '#f0f0f0',
  },
  iconContainer: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  titleContainer: {
    flex: 1,
  },
  title: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 4,
  },
  category: {
    fontSize: 14,
    fontWeight: '500',
  },
  closeButton: {
    padding: 4,
  },
  section: {
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#f0f0f0',
  },
  description: {
    fontSize: 16,
    color: '#666',
    lineHeight: 22,
  },
  footer: {
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#f0f0f0',
  },
  savedDate: {
    fontSize: 14,
    color: '#999',
    fontStyle: 'italic',
  },
  actions: {
    flexDirection: 'row',
    padding: 12,
    justifyContent: 'space-around',
  },
  actionButton: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 8,
  },
  actionText: {
    marginLeft: 4,
    color: '#4285F4',
    fontWeight: '500',
  },
  unsaveButton: {
    borderLeftWidth: 1,
    borderLeftColor: '#f0f0f0',
    paddingLeft: 12,
  },
  unsaveText: {
    color: '#FF5252',
  },
});

export default PlaceDetailCard;
