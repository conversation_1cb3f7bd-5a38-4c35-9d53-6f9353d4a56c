import React from 'react';
import { View, Text, StyleSheet } from 'react-native';
import { Marker } from 'react-native-maps';

/**
 * A simple debug marker component that always shows up on the map
 * to help diagnose map rendering issues
 */
const DebugMarker: React.FC = () => {
  // Use a fixed position in the center of Europe
  const position = {
    latitude: 48.8566,
    longitude: 2.3522
  };

  return (
    <Marker
      coordinate={position}
      pinColor="purple"
      title="Debug Marker"
      description="If you can see this, map markers are working"
    >
      <View style={styles.debugMarker}>
        <Text style={styles.debugText}>DEBUG</Text>
      </View>
    </Marker>
  );
};

const styles = StyleSheet.create({
  debugMarker: {
    backgroundColor: 'purple',
    padding: 8,
    borderRadius: 4,
  },
  debugText: {
    color: 'white',
    fontWeight: 'bold',
  }
});

export default DebugMarker;
