import React, { useState } from 'react';
import { View, StyleSheet, Alert, Platform } from 'react-native';
import { Button, Text, ActivityIndicator } from 'react-native-paper';
import * as DocumentPicker from 'expo-document-picker';
import * as ImagePicker from 'expo-image-picker';
import { uploadFile, uploadDocument } from '../services/firebase/fileStorage';
import { uploadImage } from '../services/firebase/storage';
import { useAuth } from '../contexts/AuthContext';

type FileUploaderProps = {
  onFileUploaded?: (url: string) => void;
  allowedTypes?: string[];
  maxSizeMB?: number;
  title?: string;
  includeImages?: boolean;
};

const FileUploader: React.FC<FileUploaderProps> = ({
  onFileUploaded,
  allowedTypes = ['*/*'],
  maxSizeMB = 50,
  title = 'Upload File',
  includeImages = true,
}) => {
  const [loading, setLoading] = useState(false);
  const { user } = useAuth();

  // Request permissions for gallery access (for image picking)
  const requestMediaLibraryPermissions = async () => {
    if (Platform.OS !== 'web') {
      const { status } = await ImagePicker.requestMediaLibraryPermissionsAsync();
      if (status !== 'granted') {
        Alert.alert('Permission Denied', 'Sorry, we need camera roll permissions to upload images.');
        return false;
      }
      return true;
    }
    return true;
  };

  // Pick and upload a document
  const pickDocument = async () => {
    try {
      const result = await DocumentPicker.getDocumentAsync({
        type: allowedTypes,
        copyToCacheDirectory: true,
      });

      if (result.canceled) {
        return;
      }

      const file = result.assets[0];
      
      // Check file size
      if (file.size && file.size > maxSizeMB * 1024 * 1024) {
        Alert.alert('File Too Large', `File size should be less than ${maxSizeMB}MB`);
        return;
      }

      uploadSelectedFile(file.uri, file.name, file.mimeType);
    } catch (error) {
      console.error('Error picking document:', error);
      Alert.alert('Error', 'Failed to pick document');
    }
  };

  // Pick and upload an image
  const pickImage = async () => {
    const hasPermission = await requestMediaLibraryPermissions();
    if (!hasPermission) return;

    try {
      const result = await ImagePicker.launchImageLibraryAsync({
        mediaTypes: ImagePicker.MediaTypeOptions.Images,
        allowsEditing: true,
        quality: 1,
      });

      if (result.canceled) {
        return;
      }

      const imageUri = result.assets[0].uri;
      
      // Generate a filename with timestamp to avoid overwrites
      const timestamp = new Date().getTime();
      const filename = `image_${timestamp}.jpg`;
      
      uploadSelectedFile(imageUri, filename, 'image/jpeg');
    } catch (error) {
      console.error('Error picking image:', error);
      Alert.alert('Error', 'Failed to pick image');
    }
  };

  // Handle the upload process
  const uploadSelectedFile = async (uri: string, filename: string, contentType?: string) => {
    if (!user) {
      Alert.alert('Error', 'You must be logged in to upload files');
      return;
    }

    setLoading(true);
    try {
      let downloadURL;
      
      if (contentType?.startsWith('image/')) {
        // Use image uploader for images (with compression)
        const path = `users/${user.uid}/files/${filename}`;
        downloadURL = await uploadImage(uri, path);
      } else {
        // Use document uploader for other file types
        downloadURL = await uploadDocument(user.uid, uri, filename, contentType);
      }
      
      Alert.alert('Success', 'File uploaded successfully!');
      
      // Call the callback with the download URL
      if (onFileUploaded) {
        onFileUploaded(downloadURL);
      }
    } catch (error: any) {
      console.error('Upload error:', error);
      Alert.alert('Upload Failed', error.message || 'Failed to upload file');
    } finally {
      setLoading(false);
    }
  };

  return (
    <View style={styles.container}>
      <Text style={styles.title}>{title}</Text>
      
      <View style={styles.buttonContainer}>
        <Button 
          mode="contained" 
          onPress={pickDocument}
          disabled={loading}
          style={styles.button}
        >
          Select Document
        </Button>
        
        {includeImages && (
          <Button 
            mode="contained" 
            onPress={pickImage}
            disabled={loading}
            style={styles.button}
          >
            Select Image
          </Button>
        )}
      </View>
      
      {loading && (
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color="#3498db" />
          <Text style={styles.loadingText}>Uploading...</Text>
        </View>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    padding: 16,
    backgroundColor: '#f5f5f5',
    borderRadius: 8,
    marginVertical: 10,
  },
  title: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 16,
  },
  buttonContainer: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    flexWrap: 'wrap',
  },
  button: {
    marginVertical: 8,
    minWidth: 150,
  },
  loadingContainer: {
    marginTop: 20,
    alignItems: 'center',
  },
  loadingText: {
    marginTop: 8,
    fontSize: 16,
  },
});

export default FileUploader; 