import React, { useState } from 'react';
import { 
  View, 
  Text, 
  StyleSheet, 
  TouchableOpacity, 
  Modal, 
  ScrollView, 
  Platform,
  TextInput 
} from 'react-native';
import { MaterialIcons } from '@expo/vector-icons';

interface TravelDatesProps {
  startDate: Date | null;
  endDate: Date | null;
  onStartDateChange: (date: Date) => void;
  onEndDateChange: (date: Date) => void;
  onMonthSelect?: (month: string, year: number) => void;
  onDurationSelect?: (days: number) => void;
}

// Duration options in days
const DURATION_OPTIONS = [
  { label: '3-5 days', value: 4 },
  { label: '1 week', value: 7 },
  { label: '10 days', value: 10 },
  { label: '2 weeks', value: 14 },
  { label: '3 weeks', value: 21 },
  { label: '1 month', value: 30 },
  { label: 'Not sure yet', value: 0 },
];

// Month options
const MONTHS = [
  'January', 'February', 'March', 'April', 'May', 'June', 
  'July', 'August', 'September', 'October', 'November', 'December'
];

const TravelDates: React.FC<TravelDatesProps> = ({
  startDate,
  endDate,
  onStartDateChange,
  onEndDateChange,
  onMonthSelect,
  onDurationSelect,
}) => {
  // State for direct input values
  const [startDay, setStartDay] = useState('22');
  const [startMonth, setStartMonth] = useState('Apr');
  const [startYear, setStartYear] = useState('2025');
  const [endDay, setEndDay] = useState('29');
  const [endMonth, setEndMonth] = useState('Apr');
  const [endYear, setEndYear] = useState('2025');
  
  // States for dropdowns
  const [showMonthPicker, setShowMonthPicker] = useState(false);
  const [showDurationPicker, setShowDurationPicker] = useState(false);
  const [selectedMonth, setSelectedMonth] = useState<string | null>(null);
  const [selectedYearValue, setSelectedYearValue] = useState<number>(new Date().getFullYear());
  const [selectedDuration, setSelectedDuration] = useState<number | null>(null);

  // Generate array of next 3 years for month selection
  const years = [
    new Date().getFullYear(),
    new Date().getFullYear() + 1,
    new Date().getFullYear() + 2,
  ];

  const formatDate = (date: Date | null) => {
    if (!date) return 'Select Date';
    return date.toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric',
      year: 'numeric'
    });
  };

  const parseAndUpdateStartDate = () => {
    try {
      // Try to parse the date from components
      const dateStr = `${startMonth} ${startDay}, ${startYear}`;
      const newDate = new Date(dateStr);
      
      if (!isNaN(newDate.getTime())) {
        onStartDateChange(newDate);
      }
    } catch (error) {
      console.log('Invalid date format');
    }
  };

  const parseAndUpdateEndDate = () => {
    try {
      // Try to parse the date from components
      const dateStr = `${endMonth} ${endDay}, ${endYear}`;
      const newDate = new Date(dateStr);
      
      if (!isNaN(newDate.getTime())) {
        onEndDateChange(newDate);
      }
    } catch (error) {
      console.log('Invalid date format');
    }
  };

  const handleMonthSelect = (month: string, year: number) => {
    setSelectedMonth(month);
    setSelectedYearValue(year);
    setShowMonthPicker(false);
    if (onMonthSelect) {
      onMonthSelect(month, year);
    }
  };

  const handleDurationSelect = (days: number) => {
    setSelectedDuration(days);
    setShowDurationPicker(false);
    if (onDurationSelect) {
      onDurationSelect(days);
    }
  };

  const getMonthYearText = () => {
    if (!selectedMonth) return 'Select Month';
    return `${selectedMonth} ${selectedYearValue}`;
  };

  const getDurationText = () => {
    if (!selectedDuration) return 'Select Duration';
    const option = DURATION_OPTIONS.find(opt => opt.value === selectedDuration);
    return option ? option.label : 'Select Duration';
  };

  return (
    <View style={styles.container}>
      <Text style={styles.title}>Travel Dates</Text>
      
      {/* Date Input Boxes */}
      <View style={styles.dateBoxesContainer}>
        <View style={styles.dateBox}>
          <MaterialIcons name="calendar-today" size={18} color="#667085" style={styles.calendarIcon} />
          <View style={styles.dateInputGroup}>
            <TextInput 
              style={styles.dateInputDay}
              value={startDay}
              onChangeText={setStartDay}
              keyboardType="numeric"
              maxLength={2}
              onBlur={parseAndUpdateStartDate}
            />
            <TextInput 
              style={styles.dateInputMonth}
              value={startMonth}
              onChangeText={setStartMonth}
              maxLength={3}
              onBlur={parseAndUpdateStartDate}
            />
            <TextInput 
              style={styles.dateInputYear}
              value={startYear}
              onChangeText={setStartYear}
              keyboardType="numeric"
              maxLength={4}
              onBlur={parseAndUpdateStartDate}
            />
          </View>
        </View>
        
        <Text style={styles.toText}>to</Text>
        
        <View style={styles.dateBox}>
          <MaterialIcons name="calendar-today" size={18} color="#667085" style={styles.calendarIcon} />
          <View style={styles.dateInputGroup}>
            <TextInput 
              style={styles.dateInputDay}
              value={endDay}
              onChangeText={setEndDay}
              keyboardType="numeric"
              maxLength={2}
              onBlur={parseAndUpdateEndDate}
            />
            <TextInput 
              style={styles.dateInputMonth}
              value={endMonth}
              onChangeText={setEndMonth}
              maxLength={3}
              onBlur={parseAndUpdateEndDate}
            />
            <TextInput 
              style={styles.dateInputYear}
              value={endYear}
              onChangeText={setEndYear}
              keyboardType="numeric"
              maxLength={4}
              onBlur={parseAndUpdateEndDate}
            />
          </View>
        </View>
      </View>

      {/* Month and Duration Selection */}
      <Text style={styles.orText}>- OR -</Text>
      
      <View style={styles.flexibleDatesContainer}>
        <Text style={styles.flexibleTitle}>Flexible with dates?</Text>
        
        <View style={styles.dropdownContainer}>
          <TouchableOpacity 
            style={styles.dropdown}
            onPress={() => setShowMonthPicker(true)}
          >
            <Text style={styles.dropdownText}>{getMonthYearText()}</Text>
            <MaterialIcons name="arrow-drop-down" size={24} color="#667085" />
          </TouchableOpacity>
          
          <TouchableOpacity 
            style={styles.dropdown}
            onPress={() => setShowDurationPicker(true)}
          >
            <Text style={styles.dropdownText}>{getDurationText()}</Text>
            <MaterialIcons name="arrow-drop-down" size={24} color="#667085" />
          </TouchableOpacity>
        </View>
      </View>

      {/* Month Picker Modal */}
      <Modal
        transparent={true}
        animationType="slide"
        visible={showMonthPicker}
        onRequestClose={() => setShowMonthPicker(false)}
      >
        <View style={styles.modalContainer}>
          <View style={styles.modalContent}>
            <Text style={styles.modalTitle}>Select Month</Text>
            <ScrollView style={styles.optionsList}>
              {years.map(year => (
                <View key={year}>
                  <Text style={styles.yearLabel}>{year}</Text>
                  {MONTHS.map(month => (
                    <TouchableOpacity
                      key={`${month}-${year}`}
                      style={styles.optionItem}
                      onPress={() => handleMonthSelect(month, year)}
                    >
                      <Text style={styles.optionText}>{month}</Text>
                    </TouchableOpacity>
                  ))}
                </View>
              ))}
            </ScrollView>
            <TouchableOpacity 
              style={styles.modalButton}
              onPress={() => setShowMonthPicker(false)}
            >
              <Text style={styles.modalButtonText}>Cancel</Text>
            </TouchableOpacity>
          </View>
        </View>
      </Modal>

      {/* Duration Picker Modal */}
      <Modal
        transparent={true}
        animationType="slide"
        visible={showDurationPicker}
        onRequestClose={() => setShowDurationPicker(false)}
      >
        <View style={styles.modalContainer}>
          <View style={styles.modalContent}>
            <Text style={styles.modalTitle}>Select Trip Duration</Text>
            <ScrollView style={styles.optionsList}>
              {DURATION_OPTIONS.map(option => (
                <TouchableOpacity
                  key={option.value}
                  style={styles.optionItem}
                  onPress={() => handleDurationSelect(option.value)}
                >
                  <Text style={styles.optionText}>{option.label}</Text>
                </TouchableOpacity>
              ))}
            </ScrollView>
            <TouchableOpacity 
              style={styles.modalButton}
              onPress={() => setShowDurationPicker(false)}
            >
              <Text style={styles.modalButtonText}>Cancel</Text>
            </TouchableOpacity>
          </View>
        </View>
      </Modal>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    padding: 16,
    backgroundColor: '#FFFFFF',
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    marginBottom: 16,
    color: '#1A2C4E',
  },
  dateBoxesContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginBottom: 24,
  },
  dateBox: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    padding: 12,
    backgroundColor: '#F8FAFC',
    borderRadius: 12,
    borderWidth: 1,
    borderColor: '#E2E8F0',
  },
  calendarIcon: {
    marginRight: 8,
  },
  dateInputGroup: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  dateInputDay: {
    width: 28,
    fontSize: 16,
    color: '#1A2C4E',
    textAlign: 'center',
  },
  dateInputMonth: {
    width: 40,
    fontSize: 16,
    color: '#1A2C4E',
    textAlign: 'center',
    marginHorizontal: 4,
  },
  dateInputYear: {
    width: 50,
    fontSize: 16,
    color: '#1A2C4E',
    textAlign: 'center',
  },
  toText: {
    marginHorizontal: 10,
    color: '#667085',
    fontSize: 16,
  },
  orText: {
    textAlign: 'center',
    color: '#667085',
    marginVertical: 16,
    fontSize: 16,
  },
  flexibleDatesContainer: {
    marginTop: 8,
  },
  flexibleTitle: {
    fontSize: 18,
    fontWeight: '600',
    marginBottom: 12,
    color: '#1A2C4E',
  },
  dropdownContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    gap: 12,
  },
  dropdown: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    padding: 16,
    backgroundColor: '#F8FAFC',
    borderRadius: 12,
    borderWidth: 1,
    borderColor: '#E2E8F0',
  },
  dropdownText: {
    fontSize: 16,
    color: '#1A2C4E',
  },
  modalContainer: {
    flex: 1,
    justifyContent: 'flex-end',
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
  },
  modalContent: {
    backgroundColor: 'white',
    borderTopLeftRadius: 24,
    borderTopRightRadius: 24,
    padding: 20,
    maxHeight: '70%',
  },
  modalTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    marginBottom: 16,
    textAlign: 'center',
    color: '#1A2C4E',
  },
  modalButton: {
    backgroundColor: '#3A7BF8',
    padding: 16,
    borderRadius: 12,
    alignItems: 'center',
    marginTop: 16,
  },
  modalButtonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: '600',
  },
  optionsList: {
    maxHeight: 350,
  },
  yearLabel: {
    fontSize: 18,
    fontWeight: '600',
    color: '#3A7BF8',
    marginVertical: 12,
    paddingHorizontal: 8,
  },
  optionItem: {
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#F1F5F9',
  },
  optionText: {
    fontSize: 16,
    color: '#1A2C4E',
  },
});

export default TravelDates; 