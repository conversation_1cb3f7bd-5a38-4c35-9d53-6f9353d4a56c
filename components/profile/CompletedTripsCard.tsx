import React from 'react';
import { StyleSheet, View, TouchableOpacity } from 'react-native';
import { Text } from 'react-native-paper';
import { Feather } from '@expo/vector-icons';
import { useRouter } from 'expo-router';

interface CompletedTripsCardProps {
  tripsCount: number;
}

const CompletedTripsCard = ({ tripsCount }: CompletedTripsCardProps) => {
  const router = useRouter();

  const handlePress = () => {
    router.push('/completed-trips');
  };

  return (
    <TouchableOpacity style={styles.card} onPress={handlePress}>
      <View style={styles.contentContainer}>
        <View>
          <Text style={styles.cardTitle}>Completed Trips</Text>
          <Text style={styles.cardSubtitle}>View your past adventures</Text>
        </View>
        <View style={styles.rightContainer}>
          <Text style={styles.tripsCount}>{tripsCount} trips</Text>
          <Feather name="chevron-right" size={20} color="#CCCCCC" style={styles.chevron} />
        </View>
      </View>
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  card: {
    backgroundColor: '#FFFFFF',
    borderRadius: 12,
    padding: 20,
    marginBottom: 20,
    shadowColor: '#000000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.05,
    shadowRadius: 4,
    elevation: 2,
  },
  contentContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  cardTitle: {
    fontSize: 22,
    fontWeight: 'bold',
    color: '#000000',
    marginBottom: 4,
  },
  cardSubtitle: {
    fontSize: 14,
    color: '#666666',
  },
  rightContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  tripsCount: {
    fontSize: 16,
    color: '#666666',
    marginRight: 8,
  },
  chevron: {
    marginLeft: 4,
  },
});

export default CompletedTripsCard;
