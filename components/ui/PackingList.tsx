import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  TextInput,
  TouchableOpacity,
  ScrollView,
  StyleSheet,
  Alert,
  ActivityIndicator,
} from 'react-native';
import { MaterialIcons } from '@expo/vector-icons';
import { useTranslation } from 'react-i18next';
import { useAuth } from '../../contexts/AuthContext';
import {
  PackingItem,
  getPackingList,
  addPackingItem,
  updatePackingItem,
  removePackingItem,
  subscribeToPackingList
} from '../../services/firebase/packingList';

interface PackingListProps {
  style?: any;
}

const PackingList: React.FC<PackingListProps> = ({ style }) => {
  const { t } = useTranslation();
  const { user } = useAuth();
  const [items, setItems] = useState<PackingItem[]>([]);
  const [inputText, setInputText] = useState('');
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);

  // Load packing list on component mount
  useEffect(() => {
    if (!user) {
      setLoading(false);
      return;
    }

    // Subscribe to real-time updates
    const unsubscribe = subscribeToPackingList(user.uid, (packingItems) => {
      setItems(packingItems);
      setLoading(false);
    });

    return () => unsubscribe();
  }, [user]);

  const addItem = async () => {
    if (!user || !inputText.trim() || saving) return;

    setSaving(true);
    try {
      const success = await addPackingItem(user.uid, inputText.trim());
      if (success) {
        setInputText('');
      } else {
        Alert.alert('Error', 'Failed to add item to packing list');
      }
    } catch (error) {
      console.error('Error adding item:', error);
      Alert.alert('Error', 'Failed to add item to packing list');
    } finally {
      setSaving(false);
    }
  };

  const togglePacked = async (id: string) => {
    if (!user || saving) return;

    const item = items.find(item => item.id === id);
    if (!item) return;

    setSaving(true);
    try {
      const success = await updatePackingItem(user.uid, id, { packed: !item.packed });
      if (!success) {
        Alert.alert('Error', 'Failed to update item');
      }
    } catch (error) {
      console.error('Error updating item:', error);
      Alert.alert('Error', 'Failed to update item');
    } finally {
      setSaving(false);
    }
  };

  const deleteItem = (id: string) => {
    Alert.alert(
      'Delete Item',
      'Are you sure you want to remove this item from your packing list?',
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Delete',
          style: 'destructive',
          onPress: () => handleDeleteItem(id)
        }
      ]
    );
  };

  const handleDeleteItem = async (id: string) => {
    if (!user || saving) return;

    setSaving(true);
    try {
      const success = await removePackingItem(user.uid, id);
      if (!success) {
        Alert.alert('Error', 'Failed to delete item');
      }
    } catch (error) {
      console.error('Error deleting item:', error);
      Alert.alert('Error', 'Failed to delete item');
    } finally {
      setSaving(false);
    }
  };

  const packedCount = items.filter(item => item.packed).length;
  const totalCount = items.length;

  // Show loading state if user is not authenticated
  if (!user) {
    return (
      <View style={[styles.container, style]}>
        <View style={styles.header}>
          <Text style={styles.title}>Packing List</Text>
        </View>
        <View style={styles.emptyState}>
          <MaterialIcons name="login" size={48} color="#CCCCCC" />
          <Text style={styles.emptyStateText}>Please log in to use packing list</Text>
        </View>
      </View>
    );
  }

  return (
    <View style={[styles.container, style]}>
      {/* Header */}
      <View style={styles.header}>
        <Text style={styles.title}>Packing List</Text>
        {totalCount > 0 && (
          <Text style={styles.counter}>
            {packedCount}/{totalCount} packed
          </Text>
        )}
        {saving && (
          <ActivityIndicator size="small" color="#007AFF" style={styles.savingIndicator} />
        )}
      </View>

      {/* Add Item Input */}
      <View style={styles.inputContainer}>
        <TextInput
          style={styles.textInput}
          placeholder="Add item to pack..."
          placeholderTextColor="#8E8E93"
          value={inputText}
          onChangeText={setInputText}
          onSubmitEditing={addItem}
          returnKeyType="done"
        />
        <TouchableOpacity
          style={[styles.addButton, (!inputText.trim() || saving) && styles.addButtonDisabled]}
          onPress={addItem}
          disabled={!inputText.trim() || saving}
        >
          {saving ? (
            <ActivityIndicator size="small" color="#FFFFFF" />
          ) : (
            <MaterialIcons
              name="add"
              size={24}
              color={inputText.trim() && !saving ? "#FFFFFF" : "#CCCCCC"}
            />
          )}
        </TouchableOpacity>
      </View>

      {/* Items List */}
      {loading ? (
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="small" color="#007AFF" />
          <Text style={styles.loadingText}>Loading packing list...</Text>
        </View>
      ) : items.length > 0 ? (
        <ScrollView
          style={styles.itemsList}
          showsVerticalScrollIndicator={false}
          nestedScrollEnabled={true}
        >
          {items.map((item) => (
            <View key={item.id} style={styles.itemCard}>
              {/* Packed Circle */}
              <TouchableOpacity
                style={[
                  styles.packedCircle,
                  item.packed && styles.packedCircleFilled
                ]}
                onPress={() => togglePacked(item.id)}
                disabled={saving}
              >
                {item.packed && (
                  <MaterialIcons name="check" size={16} color="#FFFFFF" />
                )}
              </TouchableOpacity>

              {/* Item Text */}
              <Text 
                style={[
                  styles.itemText,
                  item.packed && styles.itemTextPacked
                ]}
                numberOfLines={2}
              >
                {item.text}
              </Text>

              {/* Delete Button */}
              <TouchableOpacity
                style={styles.deleteButton}
                onPress={() => deleteItem(item.id)}
                hitSlop={{ top: 10, right: 10, bottom: 10, left: 10 }}
                disabled={saving}
              >
                <MaterialIcons name="close" size={20} color="#FF3B30" />
              </TouchableOpacity>
            </View>
          ))}
        </ScrollView>
      ) : (
        <View style={styles.emptyState}>
          <MaterialIcons name="luggage" size={48} color="#CCCCCC" />
          <Text style={styles.emptyStateText}>No items in your packing list</Text>
          <Text style={styles.emptyStateSubtext}>Add items you want to pack for your trip</Text>
        </View>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    backgroundColor: '#FFFFFF',
    borderRadius: 12,
    padding: 16,
    marginHorizontal: 16,
    marginVertical: 8,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 3.84,
    elevation: 5,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  title: {
    fontSize: 20,
    fontWeight: '600',
    color: '#333333',
  },
  counter: {
    fontSize: 14,
    color: '#8E8E93',
    fontWeight: '500',
  },
  inputContainer: {
    flexDirection: 'row',
    marginBottom: 16,
    gap: 8,
  },
  textInput: {
    flex: 1,
    backgroundColor: '#F2F2F7',
    borderRadius: 8,
    paddingHorizontal: 12,
    paddingVertical: 10,
    fontSize: 16,
    color: '#000000',
    borderWidth: 1,
    borderColor: '#E5E5EA',
  },
  addButton: {
    backgroundColor: '#007AFF',
    borderRadius: 8,
    width: 44,
    height: 44,
    justifyContent: 'center',
    alignItems: 'center',
  },
  addButtonDisabled: {
    backgroundColor: '#CCCCCC',
  },
  savingIndicator: {
    marginLeft: 8,
  },
  loadingContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 20,
    gap: 8,
  },
  loadingText: {
    fontSize: 14,
    color: '#8E8E93',
  },
  itemsList: {
    maxHeight: 300,
  },
  itemCard: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#F9F9F9',
    borderRadius: 8,
    padding: 12,
    marginBottom: 8,
    gap: 12,
  },
  packedCircle: {
    width: 24,
    height: 24,
    borderRadius: 12,
    borderWidth: 2,
    borderColor: '#007AFF',
    justifyContent: 'center',
    alignItems: 'center',
  },
  packedCircleFilled: {
    backgroundColor: '#007AFF',
  },
  itemText: {
    flex: 1,
    fontSize: 16,
    color: '#333333',
    lineHeight: 20,
  },
  itemTextPacked: {
    textDecorationLine: 'line-through',
    color: '#8E8E93',
  },
  deleteButton: {
    padding: 4,
  },
  emptyState: {
    alignItems: 'center',
    paddingVertical: 32,
  },
  emptyStateText: {
    fontSize: 16,
    fontWeight: '600',
    color: '#8E8E93',
    marginTop: 12,
    textAlign: 'center',
  },
  emptyStateSubtext: {
    fontSize: 14,
    color: '#CCCCCC',
    marginTop: 4,
    textAlign: 'center',
  },
});

export default PackingList;
