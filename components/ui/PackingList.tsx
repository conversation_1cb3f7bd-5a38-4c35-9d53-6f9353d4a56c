import React, { useState } from 'react';
import {
  View,
  Text,
  TextInput,
  TouchableOpacity,
  ScrollView,
  StyleSheet,
  Alert,
} from 'react-native';
import { MaterialIcons } from '@expo/vector-icons';
import { useTranslation } from 'react-i18next';

interface PackingItem {
  id: string;
  text: string;
  packed: boolean;
  category?: string;
}

interface PackingListProps {
  style?: any;
}

const PackingList: React.FC<PackingListProps> = ({ style }) => {
  const { t } = useTranslation();
  const [items, setItems] = useState<PackingItem[]>([]);
  const [inputText, setInputText] = useState('');

  const addItem = () => {
    if (inputText.trim()) {
      const newItem: PackingItem = {
        id: Date.now().toString(),
        text: inputText.trim(),
        packed: false,
      };
      setItems([...items, newItem]);
      setInputText('');
    }
  };

  const togglePacked = (id: string) => {
    setItems(items.map(item => 
      item.id === id ? { ...item, packed: !item.packed } : item
    ));
  };

  const deleteItem = (id: string) => {
    Alert.alert(
      'Delete Item',
      'Are you sure you want to remove this item from your packing list?',
      [
        { text: 'Cancel', style: 'cancel' },
        { 
          text: 'Delete', 
          style: 'destructive',
          onPress: () => setItems(items.filter(item => item.id !== id))
        }
      ]
    );
  };

  const packedCount = items.filter(item => item.packed).length;
  const totalCount = items.length;

  return (
    <View style={[styles.container, style]}>
      {/* Header */}
      <View style={styles.header}>
        <Text style={styles.title}>Packing List</Text>
        {totalCount > 0 && (
          <Text style={styles.counter}>
            {packedCount}/{totalCount} packed
          </Text>
        )}
      </View>

      {/* Add Item Input */}
      <View style={styles.inputContainer}>
        <TextInput
          style={styles.textInput}
          placeholder="Add item to pack..."
          placeholderTextColor="#8E8E93"
          value={inputText}
          onChangeText={setInputText}
          onSubmitEditing={addItem}
          returnKeyType="done"
        />
        <TouchableOpacity 
          style={styles.addButton}
          onPress={addItem}
          disabled={!inputText.trim()}
        >
          <MaterialIcons 
            name="add" 
            size={24} 
            color={inputText.trim() ? "#FFFFFF" : "#CCCCCC"} 
          />
        </TouchableOpacity>
      </View>

      {/* Items List */}
      {items.length > 0 ? (
        <ScrollView 
          style={styles.itemsList}
          showsVerticalScrollIndicator={false}
          nestedScrollEnabled={true}
        >
          {items.map((item) => (
            <View key={item.id} style={styles.itemCard}>
              {/* Packed Circle */}
              <TouchableOpacity 
                style={[
                  styles.packedCircle,
                  item.packed && styles.packedCircleFilled
                ]}
                onPress={() => togglePacked(item.id)}
              >
                {item.packed && (
                  <MaterialIcons name="check" size={16} color="#FFFFFF" />
                )}
              </TouchableOpacity>

              {/* Item Text */}
              <Text 
                style={[
                  styles.itemText,
                  item.packed && styles.itemTextPacked
                ]}
                numberOfLines={2}
              >
                {item.text}
              </Text>

              {/* Delete Button */}
              <TouchableOpacity 
                style={styles.deleteButton}
                onPress={() => deleteItem(item.id)}
                hitSlop={{ top: 10, right: 10, bottom: 10, left: 10 }}
              >
                <MaterialIcons name="close" size={20} color="#FF3B30" />
              </TouchableOpacity>
            </View>
          ))}
        </ScrollView>
      ) : (
        <View style={styles.emptyState}>
          <MaterialIcons name="luggage" size={48} color="#CCCCCC" />
          <Text style={styles.emptyStateText}>No items in your packing list</Text>
          <Text style={styles.emptyStateSubtext}>Add items you want to pack for your trip</Text>
        </View>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    backgroundColor: '#FFFFFF',
    borderRadius: 12,
    padding: 16,
    marginHorizontal: 16,
    marginVertical: 8,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 3.84,
    elevation: 5,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  title: {
    fontSize: 20,
    fontWeight: '600',
    color: '#333333',
  },
  counter: {
    fontSize: 14,
    color: '#8E8E93',
    fontWeight: '500',
  },
  inputContainer: {
    flexDirection: 'row',
    marginBottom: 16,
    gap: 8,
  },
  textInput: {
    flex: 1,
    backgroundColor: '#F2F2F7',
    borderRadius: 8,
    paddingHorizontal: 12,
    paddingVertical: 10,
    fontSize: 16,
    color: '#000000',
    borderWidth: 1,
    borderColor: '#E5E5EA',
  },
  addButton: {
    backgroundColor: '#007AFF',
    borderRadius: 8,
    width: 44,
    height: 44,
    justifyContent: 'center',
    alignItems: 'center',
  },
  itemsList: {
    maxHeight: 300,
  },
  itemCard: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#F9F9F9',
    borderRadius: 8,
    padding: 12,
    marginBottom: 8,
    gap: 12,
  },
  packedCircle: {
    width: 24,
    height: 24,
    borderRadius: 12,
    borderWidth: 2,
    borderColor: '#007AFF',
    justifyContent: 'center',
    alignItems: 'center',
  },
  packedCircleFilled: {
    backgroundColor: '#007AFF',
  },
  itemText: {
    flex: 1,
    fontSize: 16,
    color: '#333333',
    lineHeight: 20,
  },
  itemTextPacked: {
    textDecorationLine: 'line-through',
    color: '#8E8E93',
  },
  deleteButton: {
    padding: 4,
  },
  emptyState: {
    alignItems: 'center',
    paddingVertical: 32,
  },
  emptyStateText: {
    fontSize: 16,
    fontWeight: '600',
    color: '#8E8E93',
    marginTop: 12,
    textAlign: 'center',
  },
  emptyStateSubtext: {
    fontSize: 14,
    color: '#CCCCCC',
    marginTop: 4,
    textAlign: 'center',
  },
});

export default PackingList;
