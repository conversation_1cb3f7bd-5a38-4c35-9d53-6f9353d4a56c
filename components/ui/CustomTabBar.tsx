import React from 'react';
import { View, TouchableOpacity, StyleSheet, Text } from 'react-native';
import { MaterialIcons } from '@expo/vector-icons';
import { usePathname, router } from 'expo-router';
import { useTheme } from 'react-native-paper';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { useTranslation } from 'react-i18next';
import Colors from '../../constants/Colors';
import { CopilotStep, walkthroughable } from 'react-native-copilot';
import { TUTORIAL_STEPS } from '../tutorial/TutorialWrapper';

// Create walkthroughable components
const CopilotTouchableOpacity = walkthroughable(TouchableOpacity);

const CustomTabBar = () => {
  const pathname = usePathname();
  const { colors, dark } = useTheme();
  const insets = useSafeAreaInsets();
  const { t } = useTranslation();

  const tabs = [
    {
      name: t('navigation.dashboard'),
      icon: 'home',
      path: '/(main)/dashboard'
    },
    {
      name: t('navigation.map'),
      icon: 'map',
      path: '/(main)/map'
    },
    {
      name: t('navigation.quickPlan'),
      icon: 'lightbulb',
      path: '/(main)/quick-plan'
    },
    {
      name: t('navigation.profile'),
      icon: 'person',
      path: '/(main)/profile'
    }
  ];

  const isActive = (path: string) => {
    return pathname.startsWith(path);
  };

  const navigateToScreen = (path: string) => {
    // Use router.replace instead of push to avoid animation
    router.replace(path as any);
  };

  return (
    <View style={[
      styles.container, 
      { 
        backgroundColor: dark ? Colors.dark.card : Colors.light.card,
        borderTopColor: dark ? 'rgba(255, 255, 255, 0.05)' : 'rgba(0, 0, 0, 0.05)',
        paddingBottom: insets.bottom,
        height: 70 + insets.bottom
      }
    ]}>
      {tabs.map((tab, index) => {
        const active = isActive(tab.path);

        // Wrap Map and Quick Plan tabs with tutorial steps
        if (tab.path === '/(main)/map') {
          return (
            <CopilotStep
              key={index}
              text="Use the map to explore and save restaurants, landmarks and other locations for your trip."
              order={6}
              name={TUTORIAL_STEPS.MAP_TAB}
            >
              <CopilotTouchableOpacity
                style={[
                  styles.tabButton,
                  active && styles.activeTabButton
                ]}
                onPress={() => navigateToScreen(tab.path)}
              >
                {active && (
                  <View style={[styles.activeIndicator, { backgroundColor: '#007AFF' }]} />
                )}
                <MaterialIcons
                  name={tab.icon as any}
                  size={24}
                  color={active ? '#007AFF' : '#8E8E93'}
                />
                <Text
                  style={[
                    styles.tabLabel,
                    active && styles.activeTabLabel,
                    {
                      color: active ? '#007AFF' : '#8E8E93'
                    }
                  ]}
                >
                  {tab.name}
                </Text>
              </CopilotTouchableOpacity>
            </CopilotStep>
          );
        }

        if (tab.path === '/(main)/quick-plan') {
          return (
            <CopilotStep
              key={index}
              text="Quick Plan gives you AI-powered suggestions for restaurants, attractions and day trips."
              order={7}
              name={TUTORIAL_STEPS.QUICK_PLAN_TAB}
            >
              <CopilotTouchableOpacity
                style={[
                  styles.tabButton,
                  active && styles.activeTabButton
                ]}
                onPress={() => navigateToScreen(tab.path)}
              >
                {active && (
                  <View style={[styles.activeIndicator, { backgroundColor: '#007AFF' }]} />
                )}
                <MaterialIcons
                  name={tab.icon as any}
                  size={24}
                  color={active ? '#007AFF' : '#8E8E93'}
                />
                <Text
                  style={[
                    styles.tabLabel,
                    active && styles.activeTabLabel,
                    {
                      color: active ? '#007AFF' : '#8E8E93'
                    }
                  ]}
                >
                  {tab.name}
                </Text>
              </CopilotTouchableOpacity>
            </CopilotStep>
          );
        }

        // Regular tab without tutorial
        return (
          <TouchableOpacity
            key={index}
            style={[
              styles.tabButton,
              active && styles.activeTabButton
            ]}
            onPress={() => navigateToScreen(tab.path)}
          >
            {active && (
              <View style={[styles.activeIndicator, { backgroundColor: '#007AFF' }]} />
            )}
            <MaterialIcons
              name={tab.icon as any}
              size={24}
              color={active ? '#007AFF' : '#8E8E93'}
            />
            <Text
              style={[
                styles.tabLabel,
                active && styles.activeTabLabel,
                {
                  color: active ? '#007AFF' : '#8E8E93'
                }
              ]}
            >
              {tab.name}
            </Text>
          </TouchableOpacity>
        );
      })}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    borderTopWidth: 1,
    justifyContent: 'space-around',
    alignItems: 'center',
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    elevation: 8,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: -2 },
    shadowOpacity: 0.1,
    shadowRadius: 5,
    zIndex: 1000,
  },
  tabButton: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: 8,
    position: 'relative',
  },
  activeTabButton: {
    backgroundColor: 'rgba(58, 123, 248, 0.08)',
  },
  activeIndicator: {
    position: 'absolute',
    top: 0,
    width: '50%',
    height: 3,
    borderBottomLeftRadius: 2,
    borderBottomRightRadius: 2,
  },
  tabLabel: {
    fontSize: 12,
    marginTop: 2,
    fontWeight: '500',
  },
  activeTabLabel: {
    fontWeight: '700',
  }
});

export default CustomTabBar; 