import React from 'react';
import { View } from 'react-native';
import { useSafeAreaInsets } from 'react-native-safe-area-context';

type TabBarSpacerProps = {
  extraSpace?: number;
};

/**
 * A component that adds space at the bottom to prevent content from being hidden by the bottom tab bar.
 * @param extraSpace Additional space to add in pixels (default: 0)
 */
const TabBarSpacer: React.FC<TabBarSpacerProps> = ({ extraSpace = 0 }) => {
  const insets = useSafeAreaInsets();
  
  return (
    <View style={{ 
      height: 70 + insets.bottom + extraSpace
    }} />
  );
};

export default TabBarSpacer; 