import React, { useState, useEffect, useRef } from 'react';
import { View, StyleSheet, Image, TouchableOpacity, Dimensions, ActivityIndicator } from 'react-native';
import { Text } from 'react-native-paper';
import { LinearGradient } from 'expo-linear-gradient';
import AsyncStorage from '@react-native-async-storage/async-storage';
import Colors from '../../constants/Colors';
import { getEnglishCityName } from '../../services/city/cityNormalization';

type TripCardProps = {
  destination: string;
  country?: string;
  date: string;
  imageUrl?: string; // Optional now as we'll fetch it dynamically
  onPress: () => void;
  compact?: boolean;
  fullWidth?: boolean; // Added for the All Trips screen
};

// Default fallback image if city image fetch fails
const DEFAULT_IMAGE = 'https://images.unsplash.com/photo-1506973035872-a4ec16b8e8d9?ixlib=rb-1.2.1&auto=format&fit=crop&w=500&q=60';

// Cache key prefix for AsyncStorage
const CITY_IMAGE_CACHE_PREFIX = 'city_image_';

// In-memory cache for city images (persists during app session)
const cityImagesCache: Record<string, string> = {};

const TripCard: React.FC<TripCardProps> = ({
  destination,
  country,
  date,
  imageUrl: providedImageUrl,
  onPress,
  compact = false,
  fullWidth = false
}) => {
  // Renamed to imageUrl as per requirements
  const [imageUrl, setImageUrl] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState(false);
  
  // Fixed height of 150px as per requirements (between 140-160px)
  const cardHeight = 150;
  // Full width with margins
  let cardWidth;
  if (compact) {
    cardWidth = Dimensions.get('window').width / 2 - 24;
  } else if (fullWidth) {
    cardWidth = Dimensions.get('window').width - 32;
  } else {
    cardWidth = Dimensions.get('window').width - 48;
  }
  
  // Function to fetch city image from the Cloud Function endpoint
  const fetchCityImage = async (city: string) => {
    try {
      // First normalize the city name to English
      const englishCity = await getEnglishCityName(city);
      console.log(`Normalized city name: ${city} → ${englishCity}`);
      
      // Use the English city name for all caching and API requests
      const lowerCaseCity = englishCity.toLowerCase();
      
      // First check in-memory cache (fastest)
      if (cityImagesCache[lowerCaseCity]) {
        console.log(`Using in-memory cached image for ${englishCity}`);
        setImageUrl(cityImagesCache[lowerCaseCity]);
        setIsLoading(false);
        return;
      }
      
      // Then check AsyncStorage cache
      const cacheKey = `${CITY_IMAGE_CACHE_PREFIX}${lowerCaseCity}`;
      const cachedImage = await AsyncStorage.getItem(cacheKey);
      
      if (cachedImage) {
        console.log(`Using AsyncStorage cached image for ${englishCity}`);
        // Update in-memory cache
        cityImagesCache[lowerCaseCity] = cachedImage;
        setImageUrl(cachedImage);
        setIsLoading(false);
        return;
      }
      
      // If not in any cache, fetch from API using the English city name
      console.log(`Fetching image for ${englishCity} from API`);
      const response = await fetch(
        `https://europe-west1-traveling-app-c19a8.cloudfunctions.net/getCityImage?city=${encodeURIComponent(englishCity)}`
      );
      
      if (!response.ok) {
        throw new Error(`Failed to fetch image for ${englishCity}: ${response.status}`);
      }
      
      const data = await response.json();
      
      if (data && data.imageUrl) {
        // Save to both caches
        cityImagesCache[lowerCaseCity] = data.imageUrl;
        await AsyncStorage.setItem(cacheKey, data.imageUrl);
        setImageUrl(data.imageUrl);
        // Add debug log to verify the image URL is being set correctly
        console.log('Image URL set:', data.imageUrl);
      } else {
        throw new Error('Invalid response format');
      }
    } catch (err) {
      console.error('Error fetching city image:', err);
      setError(true);
      // Use provided fallback image if available
      setImageUrl(providedImageUrl || DEFAULT_IMAGE);
    } finally {
      setIsLoading(false);
    }
  };
  
  useEffect(() => {
    // If an image URL is already provided, use it
    if (providedImageUrl) {
      setImageUrl(providedImageUrl);
      setIsLoading(false);
    } else if (destination) {
      // Otherwise fetch from the API
      fetchCityImage(destination);
    } else {
      // If no destination, use default image
      setImageUrl(DEFAULT_IMAGE);
      setIsLoading(false);
    }
    
    // Debug log to verify the destination is being passed correctly
    console.log('TripCard destination:', destination);
  }, [destination, providedImageUrl]);

  return (
    <TouchableOpacity 
      style={[styles.container, { height: cardHeight, width: cardWidth }]} 
      onPress={onPress}
      activeOpacity={0.8}
    >
      {/* Wrap everything in a relative positioned View as per requirements */}
      <View style={styles.cardWrapper}>
        {isLoading ? (
          <View style={[styles.image, styles.loadingContainer]}>
            <ActivityIndicator size="large" color="#3A7BF8" />
          </View>
        ) : (
          <>
            {/* Image as absolute positioned background */}
            <Image
              source={{ uri: imageUrl || DEFAULT_IMAGE }}
              style={styles.backgroundImage}
              resizeMode="cover"
            />
            {/* LinearGradient overlay for better text readability */}
            <LinearGradient
              colors={['rgba(0,0,0,0)', 'rgba(0,0,0,0.7)']}
              style={styles.gradient}
            />
            {/* Text content */}
            <View style={styles.textContainer}>
              <Text style={styles.destination}>{destination}</Text>
              {country && <Text style={styles.country}>{country}</Text>}
              <Text style={styles.date}>{date}</Text>
            </View>
          </>
        )}
      </View>
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  container: {
    borderRadius: 16,
    overflow: 'hidden',
    marginHorizontal: 8,
    marginVertical: 8,
    elevation: 3,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  // Added cardWrapper style as per requirements
  cardWrapper: {
    position: 'relative',
    flex: 1,
    borderRadius: 16,
    overflow: 'hidden',
  },
  // Added backgroundImage style as per requirements
  backgroundImage: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    width: '100%',
    height: '100%',
    borderRadius: 16,
  },
  loadingContainer: {
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#f0f0f0',
    borderRadius: 16, // Match container border radius to prevent layout shift
    flex: 1,
  },
  image: {
    flex: 1,
    justifyContent: 'flex-end',
  },
  imageStyle: {
    borderRadius: 16,
  },
  gradient: {
    position: 'absolute',
    left: 0,
    right: 0,
    bottom: 0,
    height: '70%', // Increased height for better text readability
    borderBottomLeftRadius: 16,
    borderBottomRightRadius: 16,
  },
  textContainer: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    padding: 16,
    zIndex: 2, // Ensure text is above gradient
  },
  destination: {
    color: 'white',
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 2,
  },
  country: {
    color: 'rgba(255, 255, 255, 0.8)',
    fontSize: 14,
    marginBottom: 4,
  },
  date: {
    color: 'rgba(255, 255, 255, 0.7)',
    fontSize: 12,
  },
});

export default TripCard; 