import React, { ReactNode } from 'react';
import { View, StyleSheet, TouchableOpacity, SafeAreaView, Platform } from 'react-native';
import { Text, useTheme } from 'react-native-paper';
import { MaterialIcons } from '@expo/vector-icons';
import { useRouter } from 'expo-router';
import Colors from '../../constants/Colors';

type AppHeaderProps = {
  title: string;
  showBackButton?: boolean;
  rightContent?: ReactNode;
  onBackPress?: () => void;
  transparent?: boolean;
};

const AppHeader: React.FC<AppHeaderProps> = ({
  title,
  showBackButton = false,
  rightContent,
  onBackPress,
  transparent = false,
}) => {
  const router = useRouter();
  const { colors } = useTheme();
  
  const handleBackPress = () => {
    if (onBackPress) {
      onBackPress();
    } else {
      router.back();
    }
  };

  return (
    <SafeAreaView style={[
      styles.container,
      { 
        backgroundColor: transparent ? 'transparent' : colors.background,
        borderBottomColor: transparent ? 'transparent' : colors.outline 
      }
    ]}>
      <View style={styles.content}>
        <View style={styles.leftSection}>
          {showBackButton && (
            <TouchableOpacity 
              onPress={handleBackPress} 
              style={styles.backButton}
              hitSlop={{ top: 10, right: 10, bottom: 10, left: 10 }}
            >
              <MaterialIcons 
                name="arrow-back-ios" 
                size={24} 
                color={transparent ? 'white' : colors.onSurface} 
              />
            </TouchableOpacity>
          )}
        </View>

        <Text 
          style={[
            styles.title, 
            { 
              color: transparent ? 'white' : colors.onSurface,
              marginLeft: showBackButton ? 0 : 16 
            }
          ]} 
          numberOfLines={1}
        >
          {title}
        </Text>

        <View style={styles.rightSection}>
          {rightContent}
        </View>
      </View>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    width: '100%',
    borderBottomWidth: 1,
    ...Platform.select({
      ios: {
        paddingTop: 0,
      },
      android: {
        paddingTop: 30,
      },
    }),
  },
  content: {
    flexDirection: 'row',
    alignItems: 'center',
    height: 56,
    paddingHorizontal: 4,
  },
  leftSection: {
    width: 48,
    justifyContent: 'center',
    alignItems: 'center',
  },
  rightSection: {
    flexDirection: 'row',
    marginLeft: 'auto',
    paddingRight: 16,
  },
  backButton: {
    padding: 8,
  },
  title: {
    fontSize: 20,
    fontWeight: '600',
    flexShrink: 1,
  },
});

export default AppHeader; 