import React from 'react';
import { View, StyleSheet } from 'react-native';
import { Text, Avatar, useTheme } from 'react-native-paper';
import Colors from '../../constants/Colors';

type ChatBubbleProps = {
  message: string;
  isUser: boolean;
  timestamp?: string;
  avatar?: string;
};

const ChatBubble: React.FC<ChatBubbleProps> = ({
  message,
  isUser,
  timestamp,
  avatar
}) => {
  const { colors, dark } = useTheme();
  
  return (
    <View style={[
      styles.container, 
      isUser ? styles.userContainer : styles.aiContainer
    ]}>
      {!isUser && (
        <View style={styles.avatarContainer}>
          <Avatar.Image 
            size={36} 
            source={avatar ? { uri: avatar } : require('../../assets/images/ai-avatar.png')} 
          />
        </View>
      )}
      
      <View style={[
        styles.bubble,
        {
          backgroundColor: isUser 
            ? colors.primary 
            : dark ? colors.surfaceVariant : Colors.light.lightBlue,
          borderBottomLeftRadius: isUser ? 16 : 4,
          borderBottomRightRadius: isUser ? 4 : 16,
        }
      ]}>
        <Text style={[
          styles.message,
          { color: isUser ? 'white' : colors.onSurface }
        ]}>
          {message}
        </Text>
        
        {timestamp && (
          <Text style={[
            styles.timestamp,
            { color: isUser ? 'rgba(255, 255, 255, 0.7)' : 'rgba(0, 0, 0, 0.5)' }
          ]}>
            {timestamp}
          </Text>
        )}
      </View>
      
      {isUser && <View style={styles.avatarPlaceholder} />}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    marginVertical: 8,
    marginHorizontal: 16,
    maxWidth: '90%',
  },
  userContainer: {
    alignSelf: 'flex-end',
  },
  aiContainer: {
    alignSelf: 'flex-start',
  },
  avatarContainer: {
    marginRight: 8,
    alignSelf: 'flex-end',
    marginBottom: 4,
  },
  avatarPlaceholder: {
    width: 36,
    marginLeft: 8,
  },
  bubble: {
    padding: 12,
    borderRadius: 16,
    maxWidth: '100%',
  },
  message: {
    fontSize: 16,
    lineHeight: 22,
  },
  timestamp: {
    fontSize: 10,
    alignSelf: 'flex-end',
    marginTop: 4,
  },
});

export default ChatBubble; 