import React from 'react';
import { StyleSheet, TouchableOpacity, View } from 'react-native';
import { Text, useTheme } from 'react-native-paper';
import { MaterialIcons } from '@expo/vector-icons';
import Colors from '../../constants/Colors';

type AppButtonProps = {
  title: string;
  onPress: () => void;
  mode?: 'primary' | 'secondary' | 'outline' | 'text';
  icon?: keyof typeof MaterialIcons.glyphMap;
  disabled?: boolean;
  fullWidth?: boolean;
  style?: any;
};

const AppButton: React.FC<AppButtonProps> = ({
  title,
  onPress,
  mode = 'primary',
  icon,
  disabled = false,
  fullWidth = false,
  style
}) => {
  const { colors, dark } = useTheme();
  
  // Button styles based on mode
  const getButtonStyle = () => {
    switch (mode) {
      case 'primary':
        return {
          backgroundColor: disabled ? (dark ? '#444' : '#D0D0D0') : colors.primary,
          borderColor: colors.primary,
        };
      case 'secondary':
        return {
          backgroundColor: colors.secondary,
          borderColor: colors.secondary,
        };
      case 'outline':
        return {
          backgroundColor: 'transparent',
          borderColor: colors.primary,
          borderWidth: 1,
        };
      case 'text':
        return {
          backgroundColor: 'transparent',
          borderColor: 'transparent',
          elevation: 0,
          shadowOpacity: 0,
        };
      default:
        return {
          backgroundColor: colors.primary,
          borderColor: colors.primary,
        };
    }
  };

  // Text color based on mode
  const getTextColor = () => {
    switch (mode) {
      case 'primary':
      case 'secondary':
        return 'white';
      case 'outline':
      case 'text':
        return disabled ? (dark ? '#666' : '#999') : colors.primary;
      default:
        return 'white';
    }
  };

  return (
    <TouchableOpacity
      style={[
        styles.button,
        getButtonStyle(),
        fullWidth && styles.fullWidth,
        disabled && styles.disabled,
        style
      ]}
      onPress={onPress}
      disabled={disabled}
      activeOpacity={0.8}
    >
      <View style={styles.content}>
        {icon && (
          <MaterialIcons
            name={icon}
            size={20}
            color={getTextColor()}
            style={styles.icon}
          />
        )}
        <Text style={[styles.text, { color: getTextColor() }]}>
          {title}
        </Text>
      </View>
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  button: {
    borderRadius: 12,
    paddingVertical: 12,
    paddingHorizontal: 24,
    elevation: 1,
    justifyContent: 'center',
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.2,
    shadowRadius: 1.5,
  },
  fullWidth: {
    width: '100%',
  },
  disabled: {
    opacity: 0.7,
  },
  content: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
  },
  text: {
    fontSize: 16,
    fontWeight: '600',
    textAlign: 'center',
  },
  icon: {
    marginRight: 8,
  },
});

export default AppButton; 