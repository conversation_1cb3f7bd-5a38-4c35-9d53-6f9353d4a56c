import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  ActivityIndicator,
  Alert,
  ScrollView,
  SafeAreaView,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import Purchases, { 
  PurchasesOffering, 
  PurchasesPackage, 
  CustomerInfo,
  PurchasesError 
} from 'react-native-purchases';
import { REVENUECAT_CONFIG } from '../../config/revenuecat';

interface SpecificPaywallProps {
  paywallId: string;
  onClose?: () => void;
  onPurchaseSuccess?: (customerInfo: CustomerInfo) => void;
  onPurchaseError?: (error: PurchasesError) => void;
}

export default function SpecificPaywall({ 
  paywallId, 
  onClose, 
  onPurchaseSuccess, 
  onPurchaseError 
}: SpecificPaywallProps) {
  const [offerings, setOfferings] = useState<PurchasesOffering | null>(null);
  const [customerInfo, setCustomerInfo] = useState<CustomerInfo | null>(null);
  const [loading, setLoading] = useState(true);
  const [purchasing, setPurchasing] = useState<string | null>(null);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    initializePaywall();
  }, []);

  const initializePaywall = async () => {
    try {
      setLoading(true);
      setError(null);
      
      console.log(`Fetching RevenueCat offerings for paywall ID: ${paywallId}...`);
      
      // Fetch current offerings and customer info in parallel
      const [offeringsResult, customerInfoResult] = await Promise.all([
        Purchases.getOfferings(),
        Purchases.getCustomerInfo()
      ]);

      console.log('Offerings fetched:', offeringsResult);
      console.log('Customer info fetched:', customerInfoResult);

      // Get the current offering (usually the first one)
      // Note: In this version, we can't filter by paywall ID directly
      // So we'll use the current offering and log the paywall ID for reference
      const currentOffering = offeringsResult.current;
      if (currentOffering) {
        setOfferings(currentOffering);
        console.log(`Using offering for paywall ID ${paywallId}:`, currentOffering.identifier);
        console.log('Available packages:', currentOffering.availablePackages.map(p => ({
          identifier: p.identifier,
          packageType: p.packageType,
          product: {
            identifier: p.product.identifier,
            price: p.product.priceString,
            title: p.product.title,
          }
        })));
      } else {
        setError('No offerings available. Please check your RevenueCat configuration.');
        console.warn('No current offering found');
      }

      setCustomerInfo(customerInfoResult);
      
    } catch (err) {
      console.error('Error initializing paywall:', err);
      setError(`Failed to load paywall: ${err instanceof Error ? err.message : 'Unknown error'}`);
    } finally {
      setLoading(false);
    }
  };

  const handlePurchase = async (packageToPurchase: PurchasesPackage) => {
    try {
      setPurchasing(packageToPurchase.identifier);
      setError(null);
      
      console.log(`Attempting to purchase package: ${packageToPurchase.identifier} from paywall ${paywallId}`);
      
      const { customerInfo: updatedCustomerInfo } = await Purchases.purchasePackage(packageToPurchase);
      
      console.log('Purchase successful:', updatedCustomerInfo);
      setCustomerInfo(updatedCustomerInfo);
      
      // Check if user now has premium entitlement
      const hasPremium = updatedCustomerInfo.entitlements.active[REVENUECAT_CONFIG.ENTITLEMENTS.PREMIUM];
      
      if (hasPremium) {
        Alert.alert(
          'Purchase Successful!',
          'Welcome to Premium! You now have access to all premium features.',
          [{ text: 'OK', onPress: onClose }]
        );
        onPurchaseSuccess?.(updatedCustomerInfo);
      }
      
    } catch (err) {
      console.error('Purchase failed:', err);
      const error = err as PurchasesError;
      setError(`Purchase failed: ${error.message}`);
      onPurchaseError?.(error);
    } finally {
      setPurchasing(null);
    }
  };

  const handleRestore = async () => {
    try {
      setLoading(true);
      setError(null);
      
      console.log('Restoring purchases...');
      const restoredCustomerInfo = await Purchases.restorePurchases();
      
      console.log('Purchases restored:', restoredCustomerInfo);
      setCustomerInfo(restoredCustomerInfo);
      
      const hasPremium = restoredCustomerInfo.entitlements.active[REVENUECAT_CONFIG.ENTITLEMENTS.PREMIUM];
      
      if (hasPremium) {
        Alert.alert(
          'Purchases Restored!',
          'Your premium subscription has been restored.',
          [{ text: 'OK', onPress: onClose }]
        );
      } else {
        Alert.alert(
          'No Purchases Found',
          'No active premium subscriptions were found to restore.'
        );
      }
      
    } catch (err) {
      console.error('Restore failed:', err);
      setError(`Failed to restore purchases: ${err instanceof Error ? err.message : 'Unknown error'}`);
    } finally {
      setLoading(false);
    }
  };

  const isPremiumActive = customerInfo?.entitlements.active[REVENUECAT_CONFIG.ENTITLEMENTS.PREMIUM] != null;

  const formatPackageTitle = (pkg: PurchasesPackage): string => {
    switch (pkg.packageType) {
      case Purchases.PACKAGE_TYPE.WEEKLY:
        return 'Weekly Premium';
      case Purchases.PACKAGE_TYPE.MONTHLY:
        return 'Monthly Premium';
      case Purchases.PACKAGE_TYPE.ANNUAL:
        return 'Yearly Premium';
      default:
        return pkg.product.title || 'Premium Package';
    }
  };

  const formatPackageDescription = (pkg: PurchasesPackage): string => {
    switch (pkg.packageType) {
      case Purchases.PACKAGE_TYPE.WEEKLY:
        return 'Billed weekly';
      case Purchases.PACKAGE_TYPE.MONTHLY:
        return 'Billed monthly';
      case Purchases.PACKAGE_TYPE.ANNUAL:
        return 'Billed annually - Best Value!';
      default:
        return 'Premium subscription';
    }
  };

  if (isPremiumActive) {
    return (
      <SafeAreaView style={styles.container}>
        <View style={styles.header}>
          <TouchableOpacity onPress={onClose} style={styles.closeButton}>
            <Ionicons name="close" size={24} color="#333" />
          </TouchableOpacity>
        </View>
        <View style={styles.premiumActiveContainer}>
          <Ionicons name="checkmark-circle" size={80} color="#4CAF50" />
          <Text style={styles.premiumActiveTitle}>You're Premium!</Text>
          <Text style={styles.premiumActiveText}>
            You have access to all premium features. Enjoy your enhanced travel planning experience!
          </Text>
          <TouchableOpacity style={styles.continueButton} onPress={onClose}>
            <Text style={styles.continueButtonText}>Continue</Text>
          </TouchableOpacity>
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={styles.container}>
      <ScrollView style={styles.scrollView} showsVerticalScrollIndicator={false}>
        <View style={styles.header}>
          <TouchableOpacity onPress={onClose} style={styles.closeButton}>
            <Ionicons name="close" size={24} color="#333" />
          </TouchableOpacity>
        </View>

        <View style={styles.content}>
          <Text style={styles.title}>Upgrade to Premium</Text>
          <Text style={styles.subtitle}>
            Unlock unlimited trip planning and premium features
          </Text>
          
          <View style={styles.featuresContainer}>
            <View style={styles.featureRow}>
              <Ionicons name="checkmark-circle" size={20} color="#4CAF50" />
              <Text style={styles.featureText}>Unlimited trip plans</Text>
            </View>
            <View style={styles.featureRow}>
              <Ionicons name="checkmark-circle" size={20} color="#4CAF50" />
              <Text style={styles.featureText}>Advanced AI recommendations</Text>
            </View>
            <View style={styles.featureRow}>
              <Ionicons name="checkmark-circle" size={20} color="#4CAF50" />
              <Text style={styles.featureText}>Priority customer support</Text>
            </View>
            <View style={styles.featureRow}>
              <Ionicons name="checkmark-circle" size={20} color="#4CAF50" />
              <Text style={styles.featureText}>Offline access</Text>
            </View>
          </View>
        </View>

        {loading && (
          <View style={styles.loadingContainer}>
            <ActivityIndicator size="large" color="#3A7BF8" />
            <Text style={styles.loadingText}>Loading subscription options...</Text>
          </View>
        )}

        {error && (
          <View style={styles.errorContainer}>
            <Text style={styles.errorText}>{error}</Text>
            <TouchableOpacity onPress={initializePaywall} style={styles.retryButton}>
              <Text style={styles.retryButtonText}>Retry</Text>
            </TouchableOpacity>
          </View>
        )}

        {offerings && offerings.availablePackages.length > 0 ? (
          <View style={styles.packagesContainer}>
            {offerings.availablePackages.map((pkg) => (
              <TouchableOpacity
                key={pkg.identifier}
                style={[
                  styles.packageCard,
                  pkg.packageType === Purchases.PACKAGE_TYPE.ANNUAL && styles.popularPackage
                ]}
                onPress={() => handlePurchase(pkg)}
                disabled={purchasing !== null}
              >
                {pkg.packageType === Purchases.PACKAGE_TYPE.ANNUAL && (
                  <View style={styles.popularBadge}>
                    <Text style={styles.popularBadgeText}>MOST POPULAR</Text>
                  </View>
                )}
                
                <Text style={[
                  styles.packageTitle,
                  pkg.packageType === Purchases.PACKAGE_TYPE.ANNUAL && { color: '#FFFFFF' }
                ]}>
                  {formatPackageTitle(pkg)}
                </Text>
                <Text style={[
                  styles.packagePrice,
                  pkg.packageType === Purchases.PACKAGE_TYPE.ANNUAL ? { color: '#FFFFFF' } : { color: '#3A7BF8' }
                ]}>
                  {pkg.product.priceString}
                </Text>
                <Text style={[
                  styles.packageDescription,
                  pkg.packageType === Purchases.PACKAGE_TYPE.ANNUAL ? { color: '#E5E7EB' } : { color: '#666' }
                ]}>
                  {formatPackageDescription(pkg)}
                </Text>
                
                {purchasing === pkg.identifier ? (
                  <ActivityIndicator size="small" color="#FFFFFF" style={styles.purchaseLoader} />
                ) : (
                  <Text style={styles.packageButtonText}>Subscribe</Text>
                )}
              </TouchableOpacity>
            ))}
          </View>
        ) : (
          <View style={styles.noPackagesContainer}>
            <Text style={styles.noPackagesText}>
              No subscription packages available at the moment.
            </Text>
          </View>
        )}

        <TouchableOpacity onPress={handleRestore} style={styles.restoreButton}>
          <Text style={styles.restoreButtonText}>Restore Purchases</Text>
        </TouchableOpacity>
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#FFFFFF',
  },
  scrollView: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'flex-end',
    paddingHorizontal: 20,
    paddingTop: 10,
  },
  closeButton: {
    padding: 8,
  },
  content: {
    paddingHorizontal: 20,
    paddingBottom: 20,
  },
  title: {
    fontSize: 28,
    fontWeight: 'bold',
    color: '#1A1A1A',
    textAlign: 'center',
    marginBottom: 8,
  },
  subtitle: {
    fontSize: 16,
    color: '#666',
    textAlign: 'center',
    marginBottom: 30,
    lineHeight: 22,
  },
  featuresContainer: {
    backgroundColor: '#F8F9FA',
    borderRadius: 12,
    padding: 20,
    marginBottom: 30,
  },
  featureRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
  },
  featureText: {
    fontSize: 16,
    color: '#333',
    marginLeft: 12,
    fontWeight: '500',
  },
  loadingContainer: {
    alignItems: 'center',
    paddingVertical: 40,
  },
  loadingText: {
    marginTop: 12,
    fontSize: 16,
    color: '#666',
  },
  errorContainer: {
    backgroundColor: '#FEF2F2',
    borderRadius: 8,
    padding: 16,
    marginHorizontal: 20,
    marginBottom: 20,
  },
  errorText: {
    color: '#DC2626',
    fontSize: 14,
    textAlign: 'center',
    marginBottom: 12,
  },
  retryButton: {
    backgroundColor: '#DC2626',
    borderRadius: 6,
    paddingVertical: 8,
    paddingHorizontal: 16,
    alignSelf: 'center',
  },
  retryButtonText: {
    color: '#FFFFFF',
    fontSize: 14,
    fontWeight: '600',
  },
  packagesContainer: {
    paddingHorizontal: 20,
    marginBottom: 20,
  },
  packageCard: {
    backgroundColor: '#FFFFFF',
    borderRadius: 12,
    padding: 20,
    marginBottom: 12,
    borderWidth: 2,
    borderColor: '#E5E7EB',
    position: 'relative',
  },
  popularPackage: {
    backgroundColor: '#3A7BF8',
    borderColor: '#3A7BF8',
  },
  popularBadge: {
    position: 'absolute',
    top: -8,
    left: 20,
    backgroundColor: '#FF6B35',
    borderRadius: 12,
    paddingHorizontal: 12,
    paddingVertical: 4,
  },
  popularBadgeText: {
    color: '#FFFFFF',
    fontSize: 12,
    fontWeight: 'bold',
  },
  packageTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#1A1A1A',
    marginBottom: 4,
  },
  packagePrice: {
    fontSize: 24,
    fontWeight: 'bold',
    marginBottom: 4,
  },
  packageDescription: {
    fontSize: 14,
    marginBottom: 16,
  },
  packageButtonText: {
    color: '#FFFFFF',
    fontSize: 16,
    fontWeight: '600',
    textAlign: 'center',
    backgroundColor: '#3A7BF8',
    paddingVertical: 12,
    borderRadius: 8,
  },
  purchaseLoader: {
    paddingVertical: 12,
  },
  noPackagesContainer: {
    alignItems: 'center',
    paddingVertical: 40,
    paddingHorizontal: 20,
  },
  noPackagesText: {
    fontSize: 16,
    color: '#666',
    textAlign: 'center',
  },
  restoreButton: {
    marginHorizontal: 20,
    marginBottom: 30,
    paddingVertical: 12,
  },
  restoreButtonText: {
    color: '#3A7BF8',
    fontSize: 16,
    fontWeight: '600',
    textAlign: 'center',
  },
  premiumActiveContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 40,
  },
  premiumActiveTitle: {
    fontSize: 28,
    fontWeight: 'bold',
    color: '#1A1A1A',
    marginTop: 20,
    marginBottom: 12,
  },
  premiumActiveText: {
    fontSize: 16,
    color: '#666',
    textAlign: 'center',
    lineHeight: 22,
    marginBottom: 30,
  },
  continueButton: {
    backgroundColor: '#4CAF50',
    borderRadius: 12,
    paddingVertical: 16,
    paddingHorizontal: 32,
  },
  continueButtonText: {
    color: '#FFFFFF',
    fontSize: 18,
    fontWeight: '600',
  },
});
