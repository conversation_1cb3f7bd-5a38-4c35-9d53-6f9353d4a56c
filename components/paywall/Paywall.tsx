import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  ActivityIndicator,
  Alert,
  ScrollView,
  SafeAreaView,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import Purchases, { 
  PurchasesOffering, 
  PurchasesPackage, 
  CustomerInfo,
  PurchasesError 
} from 'react-native-purchases';
import { REVENUECAT_CONFIG } from '../../config/revenuecat';

interface PaywallProps {
  onClose?: () => void;
  onPurchaseSuccess?: (customerInfo: CustomerInfo) => void;
  onPurchaseError?: (error: PurchasesError) => void;
}

export default function Paywall({ onClose, onPurchaseSuccess, onPurchaseError }: PaywallProps) {
  const [offerings, setOfferings] = useState<PurchasesOffering | null>(null);
  const [customerInfo, setCustomerInfo] = useState<CustomerInfo | null>(null);
  const [loading, setLoading] = useState(true);
  const [purchasing, setPurchasing] = useState<string | null>(null);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    initializePaywall();
  }, []);

  const initializePaywall = async () => {
    try {
      setLoading(true);
      setError(null);
      
      console.log('Fetching RevenueCat offerings and customer info...');
      
      // Fetch current offerings and customer info in parallel
      const [offeringsResult, customerInfoResult] = await Promise.all([
        Purchases.getOfferings(),
        Purchases.getCustomerInfo()
      ]);

      console.log('Offerings fetched:', offeringsResult);
      console.log('Customer info fetched:', customerInfoResult);

      // Get the current offering (usually the first one)
      const currentOffering = offeringsResult.current;
      if (currentOffering) {
        setOfferings(currentOffering);
        console.log('Available packages:', currentOffering.availablePackages.map(p => ({
          identifier: p.identifier,
          packageType: p.packageType,
          product: {
            identifier: p.product.identifier,
            price: p.product.priceString,
            title: p.product.title,
          }
        })));
      } else {
        setError('No offerings available. Please check your RevenueCat configuration.');
        console.warn('No current offering found');
      }

      setCustomerInfo(customerInfoResult);
      
    } catch (err) {
      console.error('Error initializing paywall:', err);
      setError(`Failed to load paywall: ${err instanceof Error ? err.message : 'Unknown error'}`);
    } finally {
      setLoading(false);
    }
  };

  const handlePurchase = async (packageToPurchase: PurchasesPackage) => {
    try {
      setPurchasing(packageToPurchase.identifier);
      setError(null);
      
      console.log('Attempting to purchase package:', packageToPurchase.identifier);
      
      const { customerInfo: updatedCustomerInfo } = await Purchases.purchasePackage(packageToPurchase);
      
      console.log('Purchase successful:', updatedCustomerInfo);
      setCustomerInfo(updatedCustomerInfo);
      
      // Check if user now has premium entitlement
      const hasPremium = updatedCustomerInfo.entitlements.active[REVENUECAT_CONFIG.ENTITLEMENTS.PREMIUM];
      
      if (hasPremium) {
        Alert.alert(
          'Purchase Successful!',
          'Welcome to Premium! You now have access to all premium features.',
          [{ text: 'OK', onPress: onClose }]
        );
        onPurchaseSuccess?.(updatedCustomerInfo);
      }
      
    } catch (err) {
      console.error('Purchase failed:', err);
      const error = err as PurchasesError;
      
      if (!error.userCancelled) {
        const errorMessage = `Purchase failed: ${error.message || 'Unknown error'}`;
        setError(errorMessage);
        Alert.alert('Purchase Failed', errorMessage);
        onPurchaseError?.(error);
      }
    } finally {
      setPurchasing(null);
    }
  };

  const handleRestore = async () => {
    try {
      setLoading(true);
      setError(null);
      
      console.log('Restoring purchases...');
      const restoredCustomerInfo = await Purchases.restorePurchases();
      
      console.log('Purchases restored:', restoredCustomerInfo);
      setCustomerInfo(restoredCustomerInfo);
      
      const hasPremium = restoredCustomerInfo.entitlements.active[REVENUECAT_CONFIG.ENTITLEMENTS.PREMIUM];
      
      if (hasPremium) {
        Alert.alert(
          'Purchases Restored!',
          'Your premium subscription has been restored.',
          [{ text: 'OK', onPress: onClose }]
        );
      } else {
        Alert.alert(
          'No Purchases Found',
          'No active premium subscriptions were found to restore.'
        );
      }
      
    } catch (err) {
      console.error('Restore failed:', err);
      setError(`Failed to restore purchases: ${err instanceof Error ? err.message : 'Unknown error'}`);
    } finally {
      setLoading(false);
    }
  };

  const isPremiumActive = customerInfo?.entitlements.active[REVENUECAT_CONFIG.ENTITLEMENTS.PREMIUM] != null;

  const formatPackageTitle = (pkg: PurchasesPackage): string => {
    switch (pkg.packageType) {
      case Purchases.PACKAGE_TYPE.WEEKLY:
        return 'Weekly Premium';
      case Purchases.PACKAGE_TYPE.MONTHLY:
        return 'Monthly Premium';
      case Purchases.PACKAGE_TYPE.ANNUAL:
        return 'Yearly Premium';
      default:
        return pkg.product.title || 'Premium Package';
    }
  };

  const formatPackageDescription = (pkg: PurchasesPackage): string => {
    switch (pkg.packageType) {
      case Purchases.PACKAGE_TYPE.WEEKLY:
        return 'Perfect for short trips';
      case Purchases.PACKAGE_TYPE.MONTHLY:
        return 'Great for regular travelers';
      case Purchases.PACKAGE_TYPE.ANNUAL:
        return 'Best value for frequent travelers';
      default:
        return 'Premium features included';
    }
  };

  if (loading) {
    return (
      <SafeAreaView style={styles.container}>
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color="#3A7BF8" />
          <Text style={styles.loadingText}>Loading premium options...</Text>
        </View>
      </SafeAreaView>
    );
  }

  if (isPremiumActive) {
    return (
      <SafeAreaView style={styles.container}>
        <View style={styles.header}>
          <TouchableOpacity onPress={onClose} style={styles.closeButton}>
            <Ionicons name="close" size={24} color="#333" />
          </TouchableOpacity>
        </View>
        <View style={styles.premiumActiveContainer}>
          <Ionicons name="checkmark-circle" size={80} color="#4CAF50" />
          <Text style={styles.premiumActiveTitle}>You're Premium!</Text>
          <Text style={styles.premiumActiveText}>
            You have access to all premium features. Enjoy your enhanced travel planning experience!
          </Text>
          <TouchableOpacity style={styles.continueButton} onPress={onClose}>
            <Text style={styles.continueButtonText}>Continue</Text>
          </TouchableOpacity>
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={styles.container}>
      <View style={styles.header}>
        <TouchableOpacity onPress={onClose} style={styles.closeButton}>
          <Ionicons name="close" size={24} color="#333" />
        </TouchableOpacity>
      </View>
      
      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        <View style={styles.titleContainer}>
          <Text style={styles.title}>Upgrade to Premium</Text>
          <Text style={styles.subtitle}>
            Unlock unlimited trip planning, advanced AI features, and more!
          </Text>
        </View>

        <View style={styles.featuresContainer}>
          <View style={styles.feature}>
            <Ionicons name="infinite" size={24} color="#3A7BF8" />
            <Text style={styles.featureText}>Unlimited trip plans</Text>
          </View>
          <View style={styles.feature}>
            <Ionicons name="sparkles" size={24} color="#3A7BF8" />
            <Text style={styles.featureText}>Advanced AI recommendations</Text>
          </View>
          <View style={styles.feature}>
            <Ionicons name="map" size={24} color="#3A7BF8" />
            <Text style={styles.featureText}>Unlimited saved places</Text>
          </View>
          <View style={styles.feature}>
            <Ionicons name="cloud-download" size={24} color="#3A7BF8" />
            <Text style={styles.featureText}>Offline trip access</Text>
          </View>
        </View>

        {error && (
          <View style={styles.errorContainer}>
            <Text style={styles.errorText}>{error}</Text>
            <TouchableOpacity onPress={initializePaywall} style={styles.retryButton}>
              <Text style={styles.retryButtonText}>Retry</Text>
            </TouchableOpacity>
          </View>
        )}

        {offerings && offerings.availablePackages.length > 0 ? (
          <View style={styles.packagesContainer}>
            {offerings.availablePackages.map((pkg) => (
              <TouchableOpacity
                key={pkg.identifier}
                style={[
                  styles.packageCard,
                  pkg.packageType === Purchases.PACKAGE_TYPE.ANNUAL && styles.popularPackage
                ]}
                onPress={() => handlePurchase(pkg)}
                disabled={purchasing !== null}
              >
                {pkg.packageType === Purchases.PACKAGE_TYPE.ANNUAL && (
                  <View style={styles.popularBadge}>
                    <Text style={styles.popularBadgeText}>MOST POPULAR</Text>
                  </View>
                )}
                
                <Text style={[
                  styles.packageTitle,
                  pkg.packageType === Purchases.PACKAGE_TYPE.ANNUAL && { color: '#FFFFFF' }
                ]}>
                  {formatPackageTitle(pkg)}
                </Text>
                <Text style={[
                  styles.packagePrice,
                  pkg.packageType === Purchases.PACKAGE_TYPE.ANNUAL ? { color: '#FFFFFF' } : { color: '#3A7BF8' }
                ]}>
                  {pkg.product.priceString}
                </Text>
                <Text style={[
                  styles.packageDescription,
                  pkg.packageType === Purchases.PACKAGE_TYPE.ANNUAL ? { color: '#E5E7EB' } : { color: '#666' }
                ]}>
                  {formatPackageDescription(pkg)}
                </Text>
                
                {purchasing === pkg.identifier ? (
                  <ActivityIndicator size="small" color="#FFFFFF" style={styles.purchaseLoader} />
                ) : (
                  <Text style={styles.packageButtonText}>Subscribe</Text>
                )}
              </TouchableOpacity>
            ))}
          </View>
        ) : (
          <View style={styles.noPackagesContainer}>
            <Text style={styles.noPackagesText}>
              No subscription packages available at the moment.
            </Text>
          </View>
        )}

        <TouchableOpacity onPress={handleRestore} style={styles.restoreButton}>
          <Text style={styles.restoreButtonText}>Restore Purchases</Text>
        </TouchableOpacity>
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#FFFFFF',
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'flex-end',
    paddingHorizontal: 20,
    paddingTop: 10,
  },
  closeButton: {
    padding: 8,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 20,
  },
  loadingText: {
    marginTop: 16,
    fontSize: 16,
    color: '#666',
    textAlign: 'center',
  },
  premiumActiveContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 40,
  },
  premiumActiveTitle: {
    fontSize: 28,
    fontWeight: 'bold',
    color: '#333',
    marginTop: 20,
    marginBottom: 12,
  },
  premiumActiveText: {
    fontSize: 16,
    color: '#666',
    textAlign: 'center',
    lineHeight: 24,
    marginBottom: 40,
  },
  continueButton: {
    backgroundColor: '#3A7BF8',
    paddingHorizontal: 40,
    paddingVertical: 16,
    borderRadius: 12,
  },
  continueButtonText: {
    color: '#FFFFFF',
    fontSize: 16,
    fontWeight: '600',
  },
  content: {
    flex: 1,
    paddingHorizontal: 20,
  },
  titleContainer: {
    alignItems: 'center',
    marginBottom: 40,
  },
  title: {
    fontSize: 32,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 12,
    textAlign: 'center',
  },
  subtitle: {
    fontSize: 16,
    color: '#666',
    textAlign: 'center',
    lineHeight: 24,
  },
  featuresContainer: {
    marginBottom: 40,
  },
  feature: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 16,
    paddingHorizontal: 20,
  },
  featureText: {
    fontSize: 16,
    color: '#333',
    marginLeft: 16,
    fontWeight: '500',
  },
  errorContainer: {
    backgroundColor: '#FFF5F5',
    borderColor: '#FEB2B2',
    borderWidth: 1,
    borderRadius: 12,
    padding: 16,
    marginBottom: 20,
    alignItems: 'center',
  },
  errorText: {
    color: '#E53E3E',
    fontSize: 14,
    textAlign: 'center',
    marginBottom: 12,
  },
  retryButton: {
    backgroundColor: '#E53E3E',
    paddingHorizontal: 20,
    paddingVertical: 8,
    borderRadius: 8,
  },
  retryButtonText: {
    color: '#FFFFFF',
    fontSize: 14,
    fontWeight: '600',
  },
  packagesContainer: {
    marginBottom: 30,
  },
  packageCard: {
    backgroundColor: '#F8F9FA',
    borderRadius: 16,
    padding: 24,
    marginBottom: 16,
    alignItems: 'center',
    position: 'relative',
    borderWidth: 2,
    borderColor: 'transparent',
  },
  popularPackage: {
    backgroundColor: '#3A7BF8',
    borderColor: '#2563EB',
  },
  popularBadge: {
    position: 'absolute',
    top: -8,
    backgroundColor: '#F59E0B',
    paddingHorizontal: 12,
    paddingVertical: 4,
    borderRadius: 12,
  },
  popularBadgeText: {
    color: '#FFFFFF',
    fontSize: 12,
    fontWeight: 'bold',
  },
  packageTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 8,
  },
  packagePrice: {
    fontSize: 24,
    fontWeight: 'bold',
    marginBottom: 8,
  },
  packageDescription: {
    fontSize: 14,
    textAlign: 'center',
    marginBottom: 16,
  },
  packageButtonText: {
    color: '#FFFFFF',
    fontSize: 16,
    fontWeight: '600',
    backgroundColor: '#3A7BF8',
    paddingHorizontal: 32,
    paddingVertical: 12,
    borderRadius: 8,
    overflow: 'hidden',
  },
  purchaseLoader: {
    marginVertical: 12,
  },
  noPackagesContainer: {
    alignItems: 'center',
    paddingVertical: 40,
  },
  noPackagesText: {
    fontSize: 16,
    color: '#666',
    textAlign: 'center',
  },
  restoreButton: {
    alignItems: 'center',
    paddingVertical: 16,
    marginBottom: 40,
  },
  restoreButtonText: {
    color: '#3A7BF8',
    fontSize: 16,
    fontWeight: '500',
  },
});
