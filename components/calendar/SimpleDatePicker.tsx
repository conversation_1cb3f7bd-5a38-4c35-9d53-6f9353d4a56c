import React, { useState } from 'react';
import { View, Text, StyleSheet, TouchableOpacity, ScrollView } from 'react-native';
import { Ionicons } from '@expo/vector-icons';

// A simple date picker component to replace react-native-calendar-picker
const SimpleDatePicker = ({ 
  onDateChange, 
  selectedStartDate = null,
  selectedEndDate = null,
  minDate = new Date(),
  maxDate = new Date(new Date().setFullYear(new Date().getFullYear() + 2)),
  allowRangeSelection = false,
  ...props 
}) => {
  const [currentMonth, setCurrentMonth] = useState(new Date());
  const [selectedDate, setSelectedDate] = useState(selectedStartDate ? new Date(selectedStartDate) : null);
  
  // Generate days for the current month
  const getDaysInMonth = (year, month) => {
    return new Date(year, month + 1, 0).getDate();
  };
  
  // Get day of week for the first day of the month (0 = Sunday, 1 = Monday, etc.)
  const getFirstDayOfMonth = (year, month) => {
    return new Date(year, month, 1).getDay();
  };
  
  const goToPreviousMonth = () => {
    setCurrentMonth(new Date(currentMonth.getFullYear(), currentMonth.getMonth() - 1, 1));
  };
  
  const goToNextMonth = () => {
    setCurrentMonth(new Date(currentMonth.getFullYear(), currentMonth.getMonth() + 1, 1));
  };
  
  const handleDateSelect = (date) => {
    const selectedDate = new Date(currentMonth.getFullYear(), currentMonth.getMonth(), date);
    setSelectedDate(selectedDate);
    
    if (onDateChange) {
      onDateChange(selectedDate);
    }
  };
  
  // Render the calendar
  const renderCalendar = () => {
    const year = currentMonth.getFullYear();
    const month = currentMonth.getMonth();
    const daysInMonth = getDaysInMonth(year, month);
    const firstDayOfMonth = getFirstDayOfMonth(year, month);
    
    const monthNames = [
      'January', 'February', 'March', 'April', 'May', 'June', 
      'July', 'August', 'September', 'October', 'November', 'December'
    ];
    
    const dayNames = ['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'];
    
    // Create empty spaces for days before the first day of the month
    const blanks = [];
    for (let i = 0; i < firstDayOfMonth; i++) {
      blanks.push(
        <View key={`blank-${i}`} style={styles.dayCell} />
      );
    }
    
    // Create day cells
    const days = [];
    for (let d = 1; d <= daysInMonth; d++) {
      const date = new Date(year, month, d);
      const isSelected = selectedDate && 
        selectedDate.getDate() === d && 
        selectedDate.getMonth() === month && 
        selectedDate.getFullYear() === year;
      
      const isDisabled = 
        (minDate && date < minDate) || 
        (maxDate && date > maxDate);
      
      days.push(
        <TouchableOpacity
          key={`day-${d}`}
          style={[
            styles.dayCell,
            isSelected && styles.selectedDay,
            isDisabled && styles.disabledDay
          ]}
          onPress={() => !isDisabled && handleDateSelect(d)}
          disabled={isDisabled}
        >
          <Text style={[
            styles.dayText,
            isSelected && styles.selectedDayText,
            isDisabled && styles.disabledDayText
          ]}>
            {d}
          </Text>
        </TouchableOpacity>
      );
    }
    
    // Combine blanks and days
    const totalCells = [...blanks, ...days];
    const rows = [];
    let cells = [];
    
    // Create rows with 7 cells each (for each day of the week)
    totalCells.forEach((cell, i) => {
      if (i % 7 === 0 && cells.length > 0) {
        rows.push(cells);
        cells = [];
      }
      cells.push(cell);
      if (i === totalCells.length - 1) {
        rows.push(cells);
      }
    });
    
    return (
      <View style={styles.calendar}>
        <View style={styles.header}>
          <TouchableOpacity onPress={goToPreviousMonth}>
            <Ionicons name="chevron-back" size={24} color="#2F80ED" />
          </TouchableOpacity>
          <Text style={styles.monthTitle}>
            {monthNames[month]} {year}
          </Text>
          <TouchableOpacity onPress={goToNextMonth}>
            <Ionicons name="chevron-forward" size={24} color="#2F80ED" />
          </TouchableOpacity>
        </View>
        
        <View style={styles.daysHeader}>
          {dayNames.map(day => (
            <Text key={day} style={styles.dayName}>{day}</Text>
          ))}
        </View>
        
        <View style={styles.daysContainer}>
          {rows.map((row, i) => (
            <View key={`row-${i}`} style={styles.row}>
              {row}
            </View>
          ))}
        </View>
      </View>
    );
  };
  
  return (
    <View style={styles.container}>
      {renderCalendar()}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    backgroundColor: '#fff',
    borderRadius: 10,
    padding: 10,
    width: '100%',
  },
  calendar: {
    width: '100%',
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 15,
    paddingHorizontal: 10,
  },
  monthTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
  },
  daysHeader: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    marginBottom: 10,
  },
  dayName: {
    width: 40,
    textAlign: 'center',
    fontSize: 14,
    color: '#666',
  },
  daysContainer: {
    flexDirection: 'column',
  },
  row: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    marginBottom: 10,
  },
  dayCell: {
    width: 40,
    height: 40,
    justifyContent: 'center',
    alignItems: 'center',
    borderRadius: 20,
  },
  dayText: {
    fontSize: 14,
    color: '#333',
  },
  selectedDay: {
    backgroundColor: '#2F80ED',
  },
  selectedDayText: {
    color: 'white',
    fontWeight: 'bold',
  },
  disabledDay: {
    opacity: 0.3,
  },
  disabledDayText: {
    color: '#999',
  },
});

export default SimpleDatePicker;
