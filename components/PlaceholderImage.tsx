import React from 'react';
import { View, Text, StyleSheet } from 'react-native';

interface PlaceholderImageProps {
  label: string;
  size?: number;
  color?: string;
  textColor?: string;
  fontSize?: number;
  borderRadius?: number;
}

/**
 * A simple placeholder image component that displays a colored square with text
 */
const PlaceholderImage: React.FC<PlaceholderImageProps> = ({
  label,
  size = 40,
  color = '#4285F4',
  textColor = '#FFFFFF',
  fontSize = 16,
  borderRadius = 8
}) => {
  // Get the first letter of each word
  const initials = label
    .split(' ')
    .map(word => word.charAt(0))
    .join('')
    .toUpperCase()
    .substring(0, 2);

  return (
    <View
      style={[
        styles.container,
        {
          width: size,
          height: size,
          backgroundColor: color,
          borderRadius: borderRadius
        }
      ]}
    >
      <Text style={[styles.text, { color: textColor, fontSize: fontSize }]}>
        {initials}
      </Text>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    justifyContent: 'center',
    alignItems: 'center'
  },
  text: {
    fontWeight: 'bold'
  }
});

export default PlaceholderImage;
