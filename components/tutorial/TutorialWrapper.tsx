import React, { useEffect, useRef } from 'react';
import { useCopilot } from 'react-native-copilot';
import { useTutorial } from '../../contexts/TutorialContext';
import { usePathname, useRouter } from 'expo-router';
import { useAuth } from '../../contexts/AuthContext';

interface TutorialWrapperProps {
  children: React.ReactNode;
}

// Tutorial step definitions
export const TUTORIAL_STEPS = {
  PLAN_NEW_TRIP: 'plan_new_trip',
  YES_SPECIFIC_ATTRACTIONS: 'yes_specific_attractions', 
  NO_BUILD_ITINERARY: 'no_build_itinerary',
  BACK_BUTTON: 'back_button',
  PACKING_LIST_INPUT: 'packing_list_input',
  MAP_TAB: 'map_tab',
  QUICK_PLAN_TAB: 'quick_plan_tab',
  FIND_RESTAURANT: 'find_restaurant',
  TOP_ATTRACTIONS: 'top_attractions',
  DAY_TRIP_IDEAS: 'day_trip_ideas',
} as const;

const TutorialWrapperComponent: React.FC = () => {
  const { isTutorialCompleted, completeTutorial, isLoading } = useTutorial();
  const { user } = useAuth();
  const pathname = usePathname();
  const router = useRouter();
  const hasStartedTutorial = useRef(false);
  const { start, copilotEvents } = useCopilot();

  // Check if we should start the tutorial
  useEffect(() => {
    // Don't do anything while tutorial context is still loading
    if (isLoading) {
      return;
    }

    // Only start tutorial if:
    // 1. User is authenticated
    // 2. Tutorial context has finished loading
    // 3. Tutorial hasn't been completed (explicitly false, not null/loading)
    // 4. We're on the dashboard (check both possible paths)
    // 5. We haven't already started the tutorial
    const isDashboardRoute = pathname === '/(main)/dashboard' || pathname === '/(main)/dashboard/index';

    if (
      user &&
      !isLoading &&
      isTutorialCompleted === false && // Explicitly check for false (not null/loading)
      isDashboardRoute &&
      !hasStartedTutorial.current
    ) {
      // Small delay to ensure components are mounted
      const timer = setTimeout(() => {
        console.log('🎯 Starting tutorial automatically...');
        hasStartedTutorial.current = true;
        start();
      }, 2000); // Reduced delay for faster start

      return () => clearTimeout(timer);
    }
  }, [user, isTutorialCompleted, isLoading, pathname, start]);

  // Handle tutorial events
  useEffect(() => {
    const handleStepChange = (step: any) => {
      console.log('Tutorial step changed:', step);

      // Handle navigation for specific steps
      switch (step.name) {
        case TUTORIAL_STEPS.YES_SPECIFIC_ATTRACTIONS:
          // User should tap the "Plan New Trip" button first
          // This will be handled by the button's onPress
          break;
        case TUTORIAL_STEPS.BACK_BUTTON:
          // User should be on the planning choice screen
          break;
        case TUTORIAL_STEPS.MAP_TAB:
          // User should be back on dashboard
          break;
        case TUTORIAL_STEPS.QUICK_PLAN_TAB:
          // User should tap the Quick Plan tab
          break;
        case TUTORIAL_STEPS.FIND_RESTAURANT:
          // User should be on Quick Plan screen
          break;
      }
    };

    const handleTutorialFinish = () => {
      console.log('Tutorial finished');
      completeTutorial();
      hasStartedTutorial.current = false;
    };

    const handleTutorialSkip = () => {
      console.log('Tutorial skipped');
      completeTutorial();
      hasStartedTutorial.current = false;
    };

    // Register event listeners
    copilotEvents.on('stepChange', handleStepChange);
    copilotEvents.on('stop', handleTutorialFinish);
    copilotEvents.on('skip', handleTutorialSkip);

    return () => {
      copilotEvents.off('stepChange', handleStepChange);
      copilotEvents.off('stop', handleTutorialFinish);
      copilotEvents.off('skip', handleTutorialSkip);
    };
  }, [copilotEvents, completeTutorial]);

  return null; // This component only handles tutorial logic, doesn't render anything
};

// Export the tutorial wrapper component directly
const TutorialWrapper: React.FC<TutorialWrapperProps> = ({ children }) => {
  return (
    <>
      <TutorialWrapperComponent />
      {children}
    </>
  );
};

export default TutorialWrapper;
